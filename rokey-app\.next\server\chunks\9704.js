"use strict";exports.id=9704,exports.ids=[9704],exports.modules={99704:(e,t,r)=>{function a(e,t,r){let a=e.getReader(),s=new TextDecoder;return new TextEncoder,new ReadableStream({async start(e){let t=!1;Date.now();try{for(;;){let{done:r,value:c}=await a.read();if(r){e.close();break}let n=s.decode(c,{stream:!0});if(!t&&n.includes("delta"))try{for(let e of n.split("\n"))if(e.startsWith("data: ")&&!e.includes("[DONE]")){let r=e.substring(6);try{let e=JSON.parse(r);if(e.choices?.[0]?.delta?.content){Date.now(),t=!0;break}}catch(e){}}}catch(e){t||(Date.now(),t=!0)}e.enqueue(c)}}catch(t){e.error(t)}}})}r.d(t,{createFirstTokenTrackingStream:()=>a})}};