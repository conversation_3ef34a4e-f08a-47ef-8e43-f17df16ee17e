(()=>{var e={};e.id=1729,e.ids=[1729],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},50853:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>l,routeModule:()=>u,serverHooks:()=>h,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var r={};s.r(r),s.d(r,{GET:()=>p});var a=s(96559),i=s(48088),n=s(37719),o=s(32190);async function p(){try{let e="https://hpkzzhpufhbxtxqaugjh.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8";if(!e||!t)return o.NextResponse.json({success:!1,error:"Missing Supabase environment variables",details:{hasUrl:!!e,hasKey:!!t}});let s=`${e}/rest/v1/`,r=await fetch(s,{method:"GET",headers:{apikey:t,Authorization:`Bearer ${t}`,"Content-Type":"application/json"}}),a=await r.text();return o.NextResponse.json({success:!0,connectivity:{status:r.status,statusText:r.statusText,headers:Object.fromEntries(r.headers.entries()),body:a.substring(0,500)},environment:{nodeEnv:"production",supabaseUrl:e,hasKey:!!t}})}catch(e){return o.NextResponse.json({success:!1,error:"Connectivity test failed",details:{message:e.message,code:e.code,cause:e.cause?.toString(),stack:e.stack?.split("\n").slice(0,5)}})}}let u=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/debug/supabase-test/route",pathname:"/api/debug/supabase-test",filename:"route",bundlePath:"app/api/debug/supabase-test/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\debug\\supabase-test\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:h}=u;function l(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[7719,580],()=>s(50853));module.exports=r})();