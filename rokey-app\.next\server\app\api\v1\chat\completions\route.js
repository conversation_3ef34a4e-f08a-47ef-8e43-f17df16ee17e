"use strict";(()=>{var e={};e.id=8246,e.ids=[2842,8246],e.modules={507:(e,t,s)=>{s.d(t,{Dc:()=>a,p2:()=>r});let r=[{id:"general_chat",name:"General Chat",description:"Handles general conversation, Q&A, and tasks not covered by other specific roles. Often the default fallback."},{id:"logic_reasoning",name:"Logic & Reasoning",description:"For tasks requiring logical deduction, problem-solving, mathematical reasoning, and complex analytical thinking."},{id:"writing",name:"Writing & Content Creation",description:"For all writing tasks, including articles, blog posts, marketing copy, creative content, essays, and more."},{id:"coding_frontend",name:"Coding - Frontend",description:"For generating and assisting with HTML, CSS, JavaScript, and frontend frameworks (React, Vue, Angular, etc.)."},{id:"coding_backend",name:"Coding - Backend",description:"For generating and assisting with server-side logic, APIs, databases, and backend frameworks (Node.js, Python, Java, etc.)."},{id:"research_synthesis",name:"Research & Synthesis",description:"For information retrieval from various sources, data analysis, and synthesizing findings into reports or summaries."},{id:"summarization_briefing",name:"Summarization & Briefing",description:"For condensing long texts, documents, or conversations into concise summaries or executive briefings."},{id:"translation_localization",name:"Translation & Localization",description:"For translating text between languages and adapting content culturally for different locales."},{id:"data_extraction_structuring",name:"Data Extraction & Structuring",description:"For identifying and extracting specific pieces of information from unstructured/semi-structured text and organizing it."},{id:"brainstorming_ideation",name:"Brainstorming & Ideation",description:"For generating new ideas, exploring concepts, and creative problem-solving sessions."},{id:"education_tutoring",name:"Education & Tutoring",description:"For explaining concepts, answering educational questions, and providing tutoring assistance across various subjects."},{id:"image_generation",name:"Image Generation",description:"For creating images from textual descriptions. Assign to keys linked to image generation models."},{id:"audio_transcription",name:"Audio Transcription",description:"For converting speech from audio files into written text. Assign to keys linked to transcription models."}],a=e=>r.find(t=>t.id===e)},2842:(e,t,s)=>{s.d(t,{trainingDataCache:()=>a});class r{set(e,t,s){this.cache.set(e,{data:t,timestamp:Date.now(),jobId:s})}get(e){let t=this.cache.get(e);return t?Date.now()-t.timestamp>this.TTL?(this.cache.delete(e),null):t:null}invalidate(e){return this.cache.delete(e)}clear(){this.cache.clear()}cleanup(){let e=Date.now();for(let[t,s]of this.cache.entries())e-s.timestamp>this.TTL&&this.cache.delete(t)}getStats(){return{size:this.cache.size,keys:Array.from(this.cache.keys())}}constructor(){this.cache=new Map,this.TTL=3e5}}let a=new r;setInterval(()=>{a.cleanup()},6e5)},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},56534:(e,t,s)=>{s.d(t,{Y:()=>u,w:()=>l});var r=s(55511),a=s.n(r);let i="aes-256-gcm",n=process.env.ROKEY_ENCRYPTION_KEY;if(!n||64!==n.length)throw Error("Invalid ROKEY_ENCRYPTION_KEY. Please set a 64-character hex string (32 bytes) for ROKEY_ENCRYPTION_KEY in your .env.local file.");let o=Buffer.from(n,"hex");function l(e){if("string"!=typeof e||0===e.length)throw Error("Encryption input must be a non-empty string.");let t=a().randomBytes(12),s=a().createCipheriv(i,o,t),r=s.update(e,"utf8","hex");r+=s.final("hex");let n=s.getAuthTag();return`${t.toString("hex")}:${n.toString("hex")}:${r}`}function u(e){if("string"!=typeof e||0===e.length)throw Error("Decryption input must be a non-empty string.");let t=e.split(":");if(3!==t.length)throw Error("Invalid encrypted text format. Expected iv:authTag:encryptedData");let s=Buffer.from(t[0],"hex"),r=Buffer.from(t[1],"hex"),n=t[2];if(12!==s.length)throw Error("Invalid IV length. Expected 12 bytes.");if(16!==r.length)throw Error("Invalid authTag length. Expected 16 bytes.");let l=a().createDecipheriv(i,o,s);l.setAuthTag(r);let u=l.update(n,"hex","utf8");return u+l.final("utf8")}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},98811:(e,t,s)=>{s.r(t),s.d(t,{patchFetch:()=>er,routeModule:()=>Z,serverHooks:()=>es,workAsyncStorage:()=>ee,workUnitAsyncStorage:()=>et});var r={};s.r(r),s.d(r,{OPTIONS:()=>Q,POST:()=>W});var a=s(96559),i=s(48088),n=s(37719),o=s(32190),l=s(2507),u=s(39398),d=s(56534),c=s(507),m=s(45697);let p={defaultKeySuccess:(e=1)=>1===e?"default_key_success":`default_key_success_attempt_${e}`,allKeysFailed:e=>`default_all_${e}_attempts_failed`,roleRouting:e=>e,intelligentRoleRouting:e=>`intelligent_role_${e}`,fallbackRouting:e=>`fallback_position_${e}`};var _=s(23097),g=s(55511),f=s.n(g),y=s(2842),h=s(55591),w=s.n(h),v=s(81630),k=s.n(v),x=s(79551);let b=new Map,A=new Map,S=new Map;function C(e,t){A.has(e)||A.set(e,{});let s=A.get(e);s[t]||(s[t]={count:0,lastUsed:0}),s[t].count++,s[t].lastUsed=Date.now()}let R=new Map;async function T(e,t,s,r,a=[]){let i=E(e);if(R.has(i))try{return await R.get(i)}catch(e){R.delete(i)}let n=D(e,t,s,r,a);R.set(i,n);try{return await n}catch(e){throw e}finally{R.delete(i)}}async function D(e,t,s,r,a=[]){let i=a?a.slice(-3).map(e=>`${e.role}: ${"string"==typeof e.content?e.content.substring(0,200):Array.isArray(e.content)?e.content.find(e=>"text"===e.type)?.text?.substring(0,200)||"[non-text]":"[unknown]"}`).join("\n"):"",n=t.map(e=>`- Role ID: "${e.id}", Name: "${e.name}", Description: "${(e.description||"N/A").substring(0,150)}"`).join("\n"),o=`Available Roles:
${n}

Recent Conversation:
${i}

Current Request: "${e.substring(0,2e3)}"

Most Appropriate Role ID:`,l=await X("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`,Connection:"keep-alive","User-Agent":"RoKey/1.0 (Classification-Optimized)",Origin:"https://rokey.app"},body:JSON.stringify({model:"gemini-2.0-flash-lite",messages:[{role:"system",content:"You are an expert task classification system. Analyze the user's request considering both the current message AND recent conversation context. Key rules: 1) If user is responding to options/choices in an ongoing task (like '1, 2, 3' after coding options), continue with same role. 2) Only switch roles for clear new tasks ('write story', 'solve math', etc.). 3) Examples: 'write code'=coding roles, 'write story'=writing roles, 'solve math'=logic_reasoning. Respond with ONLY the Role ID string."},{role:"user",content:o}],temperature:.1,max_tokens:50})},2,Y.CLASSIFICATION);if(!l.ok)throw Error(`Gemini API error: ${l.status}`);let u=await l.json(),d=u.choices?.[0]?.message?.content?.trim().replace(/["'`]/g,"")||null;if(d){d=d.replace(/^(Role ID:\s*|Role:\s*|Classification:\s*)/i,"").trim();let e=t.find(e=>e.id===d);if(e||(e=t.find(e=>e.id.toLowerCase()===d.toLowerCase())),e||(e=t.find(e=>e.name&&e.name.toLowerCase()===d.toLowerCase())),!e)return{roleId:d,confidence:.5};d=e.id}else throw Error("Empty classification result");return{roleId:d,confidence:.95}}let $=new Map,I=new Map,q=new Map;function E(e){return f().createHash("md5").update(e.toLowerCase().trim()).digest("hex")}function O(e,t,s){let r=e.map(e=>`${e.role}:${"string"==typeof e.content?e.content:JSON.stringify(e.content)}`).join("|"),a=`${t}|${r}|${s||0}`;return f().createHash("md5").update(a).digest("hex")}function K(e,t,s,r,a,i){if(s&&r&&a&&t.custom_api_config_id){let e=`${t.custom_api_config_id}:${t.messages?.[t.messages.length-1]?.content?.substring(0,100)||"default"}`;setImmediate(()=>{q.set(e,{provider:s,model:r,apiKey:a,timestamp:Date.now()})})}let n={};if(i?.roleUsed&&(n["X-RoKey-Role-Used"]=i.roleUsed),i?.routingStrategy&&(n["X-RoKey-Routing-Strategy"]=i.routingStrategy),i?.complexityLevel&&(n["X-RoKey-Complexity-Level"]=i.complexityLevel.toString()),s&&(n["X-RoKey-API-Key-Provider"]=s),i?.processingTime&&(n["X-RoKey-Processing-Time"]=`${i.processingTime}ms`),t.stream&&e.response){let t={...Object.fromEntries(e.response.headers.entries()),...n};return new Response(e.response.body,{status:e.response.status,headers:t})}if(t.stream||void 0===e.responseData)throw Error("Invalid provider result: no response data available");{let t={...e.responseHeaders||{},...n};return o.NextResponse.json(e.responseData,{status:e.status||200,headers:t})}}async function L(e,t,s){let r=`${e}_${t}`,a=S.get(r);if(a&&Date.now()-a.timestamp<9e5)return{customRoles:a.customRoles,roleAssignments:a.roleAssignments,apiKeys:a.apiKeys};try{let[a,i,n]=await Promise.allSettled([s.from("user_custom_roles").select("role_id, name, description").eq("user_id",e),s.from("api_key_role_assignments").select("role_name, api_key_id").eq("custom_api_config_id",t),s.from("api_keys").select("*").eq("custom_api_config_id",t).eq("status","active")]),o="fulfilled"===a.status&&a.value.data||[],l="fulfilled"===i.status&&i.value.data||[],u="fulfilled"===n.status&&n.value.data||[];return S.set(r,{customRoles:o,roleAssignments:l,apiKeys:u,timestamp:Date.now()}),{customRoles:o,roleAssignments:l,apiKeys:u}}catch(e){return null}}async function N(e){try{let t=y.trainingDataCache.get(e);if(t)return{trainingData:t.data,trainingJobId:t.jobId};let s=await (0,l.createSupabaseServerClientOnRequest)(),{data:r,error:a}=await s.from("training_jobs").select("id, training_data, created_at").eq("custom_api_config_id",e).eq("status","completed").order("created_at",{ascending:!1}).limit(1).single();if(a&&"PGRST116"!==a.code||!r?.training_data)return null;return y.trainingDataCache.set(e,r.training_data,r.id),{trainingData:r.training_data,trainingJobId:r.id}}catch(e){return null}}async function P(e,t,r,a){try{let{jinaEmbeddings:i}=await s.e(8108).then(s.bind(s,88108)),n=await i.embedQuery(e),{data:o,error:l}=await a.rpc("search_document_chunks",{query_embedding:n,config_id:t,user_id_param:r,match_threshold:.5,match_count:8});if(l)return{context:"",sources:[]};if(!o||0===o.length){let{data:e,error:s}=await a.rpc("search_document_chunks",{query_embedding:n,config_id:t,user_id_param:r,match_threshold:.3,match_count:5});if(s||!e||0===e.length)return{context:"",sources:[]};o=e}o.forEach((e,t)=>{});let u=[...new Set(o.map(e=>e.document_id))],{data:d}=await a.from("documents").select("id, filename").in("id",u),c=o.map((e,t)=>`[Document ${t+1} - Similarity: ${e.similarity.toFixed(3)}]
${e.content.trim()}`),m=o.map(e=>{let t=d?.find(t=>t.id===e.document_id);return{filename:t?.filename||"Unknown Document",document_id:e.document_id,similarity:Math.round(100*e.similarity)/100}});return{context:c.join("\n\n"),sources:m}}catch(e){return{context:"",sources:[]}}}async function j(e,t){if(!t||!t.processed_prompts)return e;let{processed_prompts:s}=t,r="";if(s.system_instructions?.trim()&&(r+=`${s.system_instructions.trim()}

`),s.general_instructions?.trim()&&(r+=`${s.general_instructions.trim()}

`),s.behavior_guidelines?.trim()&&(r+=`## Behavior Guidelines:
${s.behavior_guidelines.trim()}

`),s.examples&&s.examples.length>0&&(r+=`## Training Examples:
`,s.examples.forEach((e,t)=>{r+=`Example ${t+1}:
User: ${e.input}
Assistant: ${e.output}

`})),r.trim()){r+=`---
IMPORTANT INSTRUCTIONS:
1. Follow the training examples and behavior guidelines above
2. Maintain the personality and behavior patterns shown in the examples
3. Apply the system instructions and general instructions consistently

Now respond to the user following these patterns and guidelines.`;let t=[...e],s=t.findIndex(e=>"system"===e.role);if(s>=0){let e=t[s].content,a="string"==typeof e?e:Array.isArray(e)&&e.find(e=>"text"===e.type)?.text||"";t[s].content=r+"\n\n"+a}else t.unshift({role:"system",content:r});return t}return e}setInterval(function(){let e=Date.now();for(let[t,s]of(y.trainingDataCache.cleanup(),b.entries()))e-s.timestamp>36e5&&b.delete(t)},6e5);let U=m.z.object({custom_api_config_id:m.z.string().uuid({message:"custom_api_config_id must be a valid UUID."}),role:m.z.string().optional(),messages:m.z.array(m.z.object({role:m.z.enum(["user","assistant","system"]),content:m.z.any()})).min(1,{message:"Messages array cannot be empty and must contain at least one message."}),model:m.z.string().optional(),stream:m.z.boolean().optional().default(!1),temperature:m.z.number().optional(),max_tokens:m.z.number().int().positive().optional(),specific_api_key_id:m.z.string().uuid().optional()}).catchall(m.z.any());async function z(e,t){if(!e||!t)return null;let s={model:"gemini-2.0-flash-lite",messages:[{role:"system",content:"You are a prompt complexity classification expert. Your task is to analyze the user's prompt and classify its complexity on a scale of 1 to 5, where 1 is Very Simple, 2 is Simple, 3 is Moderate, 4 is Complex, and 5 is Very Complex. Output ONLY the integer number corresponding to the complexity level. Do not provide any explanation or any other text. Just the number."},{role:"user",content:`User's original prompt: "${e}"

Classify the complexity of this prompt (1-5):`}],temperature:.1,max_tokens:10,top_p:1};try{let e=await fetch("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify(s)});if(!e.ok)return await e.json().catch(()=>({message:e.statusText})),null;let r=await e.json(),a=r.choices?.[0]?.message?.content?.trim();if(a){let e=parseInt(a,10);if(!isNaN(e)&&e>=1&&e<=5)return e}}catch(e){}return null}async function M(e,t,s,r,a){let i=process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;if(!i)return{targetApiKeyData:null,roleUsedState:"missing_classification_api_key"};if(!e.user_id)return{targetApiKeyData:null,roleUsedState:"missing_user_id"};let n="";if(s.messages&&s.messages.length>0){let e=s.messages[s.messages.length-1];if("user"===e.role&&"string"==typeof e.content)n=e.content;else if("user"===e.role&&Array.isArray(e.content)){let t=e.content.find(e=>"text"===e.type);t&&"string"==typeof t.text&&(n=t.text)}}if(!n&&s.prompt&&(n=s.prompt),!n)return{targetApiKeyData:null,roleUsedState:"no_prompt_for_classification"};try{let r=new _.z,a=await r.analyzeForHybridOrchestration(n,s.messages||[],t);if(a.shouldUseHybrid&&a.confidence>.5){let i=e.user_id,o=await r.initiateHybridOrchestration(i,t,n,s.messages||[],{analysis:a,requestTimestamp:new Date,originalRequest:s});if(o.isHybridOrchestration&&o.streamingResponse)return{targetApiKeyData:null,roleUsedState:`hybrid_orchestration_${a.orchestrationType}_${a.detectedRoles.join("_")}`,hybridResponse:o.streamingResponse}}}catch(e){}let o=function(e){let t=function(e){let t=e.find(e=>"user"===e.role);if(t&&"string"==typeof t.content){let e=f().createHash("md5").update(t.content.substring(0,200)).digest("hex").substring(0,12);return`conv_${e}`}return`conv_${Date.now()}`}(e);for(let[e,s]of $.entries())if(e.startsWith(t.substring(0,15))&&Date.now()-s.lastActivity<18e5)return e;return t}(s.messages),l=$.get(o);if(l){let a=Date.now()-l.lastActivity,i=function(e,t){let s=e.toLowerCase().trim();for(let[e,r]of Object.entries({coding_frontend:["write code","write python","write javascript","write java","write c++","code this","program this","create a script","build an app","make a website","write html","write css","write react","frontend code","client code","create component","build interface","ui code","web development"],coding_backend:["write api","create server","database code","backend code","server code","write sql","create endpoint","api development","microservice","write node","express code","django code","flask code"],data_analysis:["analyze this data","create a chart","make a graph","data analysis","statistical analysis","create visualization","data science","machine learning","pandas code","numpy analysis","plot this","visualize data"],writing:["write an article","write a blog","create content","write an essay","write documentation","create copy","marketing content","blog post","article about","essay on","content for"],translation:["translate this","translate to","convert to language","in spanish","in french","in german","in chinese","translate into"],summarization:["summarize this","create summary","tldr","brief overview","key points","main ideas","executive summary"]}))if(e!==t){for(let t of r)if(s.includes(t))return{isTransition:!0,newTaskRole:e,reasoning:`explicit_task_transition: "${t}" -> ${e}`}}let r=["now","instead","switch to","change to","help me","can you","please","i want you to","i need you to"].some(e=>s.includes(e)),a=["create","build","make","develop","design","implement","generate","produce","construct","craft","compose"].some(e=>s.includes(e));if(r&&a)return{isTransition:!0,newTaskRole:null,reasoning:"transition_keyword_with_action_verb"};for(let e of["now write","now create","now build","now make","now help","instead write","instead create","can you write","can you create","help me write","help me create","help me build"])if(s.includes(e))return{isTransition:!0,newTaskRole:null,reasoning:`strong_transition_phrase: "${e}"`};return{isTransition:!1,newTaskRole:null,reasoning:"no_transition_detected"}}(n,l.lastClassifiedRole);if(i.isTransition){if(i.newTaskRole){C(e.user_id,i.newTaskRole),$.set(o,{lastClassifiedRole:i.newTaskRole,messageCount:s.messages.length,lastActivity:Date.now(),confidence:.9,conversationId:o});let a=await L(e.user_id,t,r);if(a){let e=a.roleAssignments.find(e=>e.role_name===i.newTaskRole);if(e&&e.api_key_id){let t=a.apiKeys.find(t=>t.id===e.api_key_id);if(t)return{targetApiKeyData:t,roleUsedState:`context_transition_${l.lastClassifiedRole}_to_${i.newTaskRole}`}}}}}else{let i=function(e,t,s,r){let a=e.toLowerCase().trim(),i=["continue","continue please","keep going","go on","more","more please","finish","complete","what next","then what","and then"],n=s.slice().reverse().find(e=>"assistant"===e.role),o=n&&"string"==typeof n.content&&(n.content.includes("continues")||n.content.includes("The response will continue")||n.content.length>1500);if(i.includes(a)&&r<6e5&&o)return{isContinuation:!0,confidence:.98,reasoning:"universal_continuation"};if(i.includes(a)&&r<12e4)return{isContinuation:!0,confidence:.85,reasoning:"universal_short_continuation"};let l={StoryTeller:{strong:["continue","what happens next","keep going","more story","then what"],medium:["be creative","more","and then","what about","tell me more"],weak:["go on","next","more please","continue please"]},coding_frontend:{strong:["fix this","debug this","improve this code","add feature"],medium:["make it better","optimize","refactor","enhance"],weak:["change","update","modify"]},coding_backend:{strong:["fix this","debug this","improve this code","add feature"],medium:["make it better","optimize","refactor","enhance"],weak:["change","update","modify"]},writing:{strong:["revise this","edit this","improve this","rewrite this"],medium:["make it better","enhance","polish"],weak:["change","update","fix"]},data_analysis:{strong:["analyze more","deeper analysis","what else","more insights"],medium:["explain further","elaborate","more details"],weak:["continue","more"]}}[t];if(!l)return{isContinuation:!1,confidence:0,reasoning:"no_patterns_for_role"};for(let e of l.strong)if(a.includes(e))return{isContinuation:!0,confidence:.95,reasoning:`strong_pattern_match: "${e}"`};for(let e of l.medium)if(a.includes(e))return{isContinuation:!0,confidence:.8,reasoning:`medium_pattern_match: "${e}"`};if(r<12e4){for(let e of l.weak)if(a.includes(e))return{isContinuation:!0,confidence:.6,reasoning:`weak_pattern_match: "${e}" (recent)`}}return a.length<20&&r<3e5&&["yes","no","ok","sure","please","thanks","more","again"].some(e=>a.includes(e))?{isContinuation:!0,confidence:.7,reasoning:"short_continuation_prompt"}:r<6e4?{isContinuation:!0,confidence:.65,reasoning:"very_recent_message"}:r<3e5?{isContinuation:!0,confidence:.4,reasoning:"recent_message"}:{isContinuation:!1,confidence:0,reasoning:"no_continuation_detected"}}(n,l.lastClassifiedRole,s.messages,a);if(i.isContinuation&&i.confidence>.6){l.lastActivity=Date.now(),l.messageCount++,l.confidence=i.confidence;let s=l.lastClassifiedRole;C(e.user_id,s);let a=await L(e.user_id,t,r);if(a){let e=a.roleAssignments.find(e=>e.role_name===s);if(e&&e.api_key_id){let t=a.apiKeys.find(t=>t.id===e.api_key_id);if(t)return{targetApiKeyData:t,roleUsedState:`contextual_continuation_${s}_confidence_${Math.round(100*i.confidence)}`}}}}}}let u=E(n),d=`${t}_${u}`;e.user_id;let m=b.get(d);if(m&&Date.now()-m.timestamp<36e5){let s=await L(e.user_id,t,r);if(s)if("general_chat"===m.roleId){let e=s.apiKeys.find(e=>!0===e.is_default_general_chat_model);if(e)return{targetApiKeyData:e,roleUsedState:p.intelligentRoleRouting(m.roleId)}}else{let e=s.roleAssignments.find(e=>e.role_name===m.roleId);if(e&&e.api_key_id){let t=s.apiKeys.find(t=>t.id===e.api_key_id);if(t)return{targetApiKeyData:t,roleUsedState:p.intelligentRoleRouting(m.roleId||"general_chat")}}}}let[g,y]=await Promise.all([L(e.user_id,t,r),Promise.resolve().then(()=>c.p2.map(e=>({id:e.id,name:e.name,description:e.description||""})))]);if(!g)return{targetApiKeyData:null,roleUsedState:"ecosystem_load_failed"};let h=[...y,...g.customRoles.map(e=>({id:e.role_id,name:e.name,description:e.description||""}))],w=function(e,t){let s=function(e,t=5){let s=A.get(e);return s?Object.entries(s).filter(([e,t])=>Date.now()-t.lastUsed<6048e5).sort(([e,t],[s,r])=>r.count-t.count).slice(0,t).map(([e,t])=>e):[]}(t,8);return 0===s.length?e:[...e.filter(e=>s.includes(e.id)),...e.filter(e=>!s.includes(e.id))]}(h,e.user_id),v=g.roleAssignments;if(0===h.length)return{targetApiKeyData:null,roleUsedState:"no_roles_available"};s.messages&&s.messages.slice(-3).map(e=>`${e.role}: ${"string"==typeof e.content?e.content.substring(0,200):Array.isArray(e.content)?e.content.find(e=>"text"===e.type)?.text?.substring(0,200)||"[non-text]":"[unknown]"}`).join("\n"),w.map(e=>`- ${e.id}: ${e.name}`).join("\n"),n.substring(0,1e3);let k=null;try{let r=await T(n,w,i,t,s.messages||[]);if(r){(k=r.roleId)&&b.set(d,{roleId:k,timestamp:Date.now()});let t=`user_${e.user_id}_general_pattern`;if("general_chat"===k){let e=b.get(t)||{consecutiveGeneralChat:0,timestamp:Date.now()};b.set(t,{consecutiveGeneralChat:(e.consecutiveGeneralChat||0)+1,timestamp:Date.now()})}else b.set(t,{consecutiveGeneralChat:0,timestamp:Date.now()})}else k="general_chat"}catch(e){k="general_chat"}if(k){if(C(e.user_id,k),"general_chat"===k){let e=g.apiKeys.find(e=>!0===e.is_default_general_chat_model);if(e)return{targetApiKeyData:e,roleUsedState:p.intelligentRoleRouting(k)}}let t=v.find(e=>e.role_name===k);if(t&&t.api_key_id){let e=g.apiKeys.find(e=>e.id===t.api_key_id);if(e)return{targetApiKeyData:e,roleUsedState:p.intelligentRoleRouting(k)}}}return{targetApiKeyData:null,roleUsedState:"classification_no_key_found"}}async function F(e,t,s){let r=e?.ordered_api_key_ids;if(!Array.isArray(r)||0===r.length)return{targetApiKeyData:null,roleUsedState:"strict_fallback_no_keys_defined"};let a=r.map(async(e,r)=>{try{let{data:a,error:i}=await s.from("api_keys").select("*").eq("id",e).eq("custom_api_config_id",t).single();return{index:r,keyId:e,apiKey:a,error:i,success:!i&&a&&"active"===a.status}}catch(t){return{index:r,keyId:e,apiKey:null,error:t,success:!1}}}),i=await Promise.allSettled(a);for(let e=0;e<i.length;e++){let t=i[e];if("fulfilled"===t.status&&t.value.success){let{apiKey:s,keyId:r}=t.value;return{targetApiKeyData:s,roleUsedState:p.fallbackRouting(e)}}if("fulfilled"===t.status){let{keyId:e,apiKey:s,error:r}=t.value;r&&r.code}}return{targetApiKeyData:null,roleUsedState:"strict_fallback_no_active_key_in_list"}}async function B(e,t,s){let r=process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;if(!r)return{targetApiKeyData:null,roleUsedState:"complexity_rr_missing_classifier_key"};let a="";if(t.messages&&t.messages.length>0){let e=t.messages[t.messages.length-1];if("user"===e.role&&"string"==typeof e.content)a=e.content;else if("user"===e.role&&Array.isArray(e.content)){let t=e.content.find(e=>"text"===e.type);t&&"string"==typeof t.text&&(a=t.text)}}if(!a&&t.prompt&&(a=t.prompt),!a)return{targetApiKeyData:null,roleUsedState:"complexity_rr_no_prompt"};let i=await z(a,r);if(null===i||i<1||i>5)return{targetApiKeyData:null,roleUsedState:"complexity_rr_invalid_classification"};let{data:n,error:o}=await s.from("config_api_key_complexity_assignments").select(`
      api_key_id,
      complexity_level,
      api_keys!inner (
        id,
        status,
        provider,
        predefined_model_id,
        is_default_general_chat_model,
        encrypted_api_key,
        label
      )
    `).eq("custom_api_config_id",e.id).eq("complexity_level",i);if(o)return{targetApiKeyData:null,roleUsedState:"complexity_rr_db_error",classifiedComplexityLevel:i,classifiedComplexityLLM:"gemini-2.0-flash-lite"};let l=n?.filter(e=>"active"===e.api_keys.status)?.map(e=>e.api_keys)||[];if(l.length>0){let t=e.routing_strategy_params||{},r=`_complexity_${i}_rr_idx`,a="number"==typeof t[r]?t[r]:0,n=[...l].sort((e,t)=>e.id.localeCompare(t.id)),o=n[a%n.length],u=(a+1)%n.length;return t[r]=u,setImmediate(async()=>{let{error:r}=await s.from("custom_api_configs").update({routing_strategy_params:t}).eq("id",e.id)}),{targetApiKeyData:o,roleUsedState:`complexity_rr_level_${i}_key_found`,classifiedComplexityLevel:i,classifiedComplexityLLM:"gemini-2.0-flash-lite"}}let u=[];for(let e=1;e<=4;e++)i-e>=1&&u.push(i-e),i+e<=5&&u.push(i+e);for(let t of u){let{data:r,error:a}=await s.from("config_api_key_complexity_assignments").select(`
        api_key_id,
        complexity_level,
        api_keys!inner (
          id,
          status,
          provider,
          predefined_model_id,
          is_default_general_chat_model,
          encrypted_api_key,
          label
        )
      `).eq("custom_api_config_id",e.id).eq("complexity_level",t);if(a)continue;let n=r?.filter(e=>"active"===e.api_keys.status)?.map(e=>e.api_keys)||[];if(n.length>0){let r=e.routing_strategy_params||{},a=`_complexity_${t}_rr_idx`,o="number"==typeof r[a]?r[a]:0,l=[...n].sort((e,t)=>e.id.localeCompare(t.id)),u=l[o%l.length],d=(o+1)%l.length;return r[a]=d,setImmediate(async()=>{let{error:t}=await s.from("custom_api_configs").update({routing_strategy_params:r}).eq("id",e.id)}),{targetApiKeyData:u,roleUsedState:`complexity_rr_level_${i}_proximal_${t}_key_found`,classifiedComplexityLevel:i,classifiedComplexityLLM:"gemini-2.0-flash-lite"}}}return{targetApiKeyData:null,roleUsedState:"complexity_rr_no_keys_found",classifiedComplexityLevel:i,classifiedComplexityLLM:"gemini-2.0-flash-lite"}}async function H(e,t,s){let[r,a]=await Promise.allSettled([s?e.from("api_key_role_assignments").select("api_key_id").eq("custom_api_config_id",t).eq("role_name",s).single():Promise.resolve({data:null,error:null}),e.from("api_keys").select("*").eq("custom_api_config_id",t).eq("is_default_general_chat_model",!0).single()]);if(s&&"fulfilled"===r.status&&r.value.data){let t=r.value.data,{data:s,error:a}=await e.from("api_keys").select("*").eq("id",t.api_key_id).single();if(a);else if(s&&"active"===s.status)return s}else s&&r.status;if("fulfilled"===a.status&&a.value.data){let e=a.value.data;if("active"===e.status)return e}else a.status;return null}let Y={CLASSIFICATION:5e3,LLM_REQUEST:8e3,SOCKET:1500,GOOGLE_CLASSIFICATION:7e3},G=new(w()).Agent({keepAlive:!0,keepAliveMsecs:12e4,maxSockets:200,maxFreeSockets:50,timeout:Y.SOCKET,scheduling:"lifo",maxTotalSockets:500}),J=new(k()).Agent({keepAlive:!0,keepAliveMsecs:12e4,maxSockets:200,maxFreeSockets:50,timeout:Y.SOCKET,scheduling:"lifo",maxTotalSockets:500});async function X(e,t,r=3,a){let i,n=a||(e.includes("generativelanguage.googleapis.com")?Y.GOOGLE_CLASSIFICATION:Y.LLM_REQUEST);for(let a=1;a<=r;a++)try{return await function(e,t,r=Y.LLM_REQUEST){return new Promise((a,i)=>{let n=new x.URL(e),o="https:"===n.protocol,l=o?w():k(),u={hostname:n.hostname,port:n.port||(o?443:80),path:n.pathname+n.search,method:t.method||"GET",headers:{...t.headers,Connection:"keep-alive","Keep-Alive":`timeout=${Math.floor(r/1e3)}, max=100`},timeout:r,agent:o?G:J},d=l.request(u,e=>{let t=e,r=e.headers["content-encoding"];if("gzip"===r){let r=s(74075);t=e.pipe(r.createGunzip())}else if("deflate"===r){let r=s(74075);t=e.pipe(r.createInflate())}else if("br"===r){let r=s(74075);t=e.pipe(r.createBrotliDecompress())}if(e.headers["content-type"]?.includes("text/event-stream")||e.headers["content-type"]?.includes("text/plain")||e.headers["content-type"]?.includes("application/x-ndjson"))a(new Response(new ReadableStream({start(e){t.on("data",t=>{let s=t.toString("utf8");e.enqueue(new TextEncoder().encode(s))}),t.on("end",()=>{e.close()}),t.on("error",t=>{e.error(t)})}}),{status:e.statusCode,statusText:e.statusMessage||"",headers:new Headers(e.headers)}));else{let s=[];t.on("data",e=>{s.push(Buffer.isBuffer(e)?e:Buffer.from(e))}),t.on("end",()=>{a(new Response(Buffer.concat(s).toString("utf8"),{status:e.statusCode,statusText:e.statusMessage||"",headers:new Headers(e.headers)}))})}});d.on("error",e=>{i(Error(`Network request failed: ${e.message}`))}),d.on("timeout",()=>{d.destroy(),i(Error(`Request timeout after ${r}ms`))}),t.body&&d.write(t.body),d.end()})}(e,t,n)}catch(t){if(i=t,a===r)throw t;let e=t.message.includes("timeout")?50:100*a;await new Promise(t=>setTimeout(t,e))}throw i}async function V(e,t,r,a){let i,n,o,l,u,d=null,c=new Date;if(!a.stream&&a.messages&&t){let e=O(a.messages,t,a.temperature),s=I.get(e);if(s&&Date.now()-s.timestamp<12e4)return{success:!0,response:void 0,responseData:s.response,responseHeaders:new Headers({"x-rokey-cache":"hit"}),status:200,error:null,llmRequestTimestamp:new Date,llmResponseTimestamp:new Date}}let m={method:"POST",headers:{"Content-Type":"application/json",Connection:"keep-alive","Keep-Alive":"timeout=30, max=100","User-Agent":"RoKey/1.0 (Performance-Optimized)",Accept:"application/json","Cache-Control":"no-cache"}};try{let p=function(e,t){if(!e)return"";let s=t.toLowerCase(),r=`${s}/`;return e.toLowerCase().startsWith(r)?e.substring(r.length):e}(t,e||""),_=e?.toLowerCase()==="openrouter"?t:p;if(!_)throw{message:`Effective model ID is missing for provider ${e} (DB Model: ${t})`,status:500,internal:!0};if(d=new Date,e?.toLowerCase()==="openai"){let{custom_api_config_id:e,role:t,...s}=a,d={...s,model:_,messages:a.messages,stream:a.stream};Object.keys(d).forEach(e=>void 0===d[e]&&delete d[e]);let p={...m};p.headers={...m.headers,Authorization:`Bearer ${r}`,Origin:"https://rokey.app",Connection:"keep-alive","Keep-Alive":"timeout=30, max=100","Cache-Control":"no-cache",Priority:"u=1, i"},p.body=JSON.stringify(d);let g=await X("https://api.openai.com/v1/chat/completions",p);if(c=new Date,i=g.status,u=g.headers,!g.ok){let e=await g.json().catch(()=>({error:{message:g.statusText}}));throw n={message:`OpenAI Error: ${e?.error?.message||g.statusText}`,status:g.status,provider_error:e}}if(a.stream){if(!g.body)throw n={message:"OpenAI stream body null",status:500};o=g,l={note:"streamed"}}else l=await g.json()}else if(e?.toLowerCase()==="openrouter"){let{custom_api_config_id:e,role:t,...d}=a,p={...d,model:_,messages:a.messages,stream:a.stream,usage:{include:!0}};Object.keys(p).forEach(e=>void 0===p[e]&&delete p[e]);let g={...m};g.headers={...m.headers,Authorization:`Bearer ${r}`,"HTTP-Referer":"https://rokey.app","X-Title":"RoKey",Origin:"https://rokey.app",Connection:"keep-alive","Keep-Alive":"timeout=30, max=100","Cache-Control":"no-cache",Priority:"u=1, i"},g.body=JSON.stringify(p);let f=await X("https://openrouter.ai/api/v1/chat/completions",g);if(c=new Date,i=f.status,u=f.headers,!f.ok){let e=await f.json().catch(()=>({error:{message:f.statusText}}));throw n={message:`OpenRouter Error: ${e?.error?.message||f.statusText}`,status:f.status,provider_error:e}}if(a.stream){if(!f.body)throw n={message:"OpenRouter stream body null",status:500};let{createFirstTokenTrackingStream:e}=await s.e(9704).then(s.bind(s,99704)),t=e(f.body,"OpenRouter",_);o=new Response(t,{status:f.status,statusText:f.statusText,headers:f.headers}),l={note:"streamed"}}else l=await f.json()}else if(e?.toLowerCase()==="google"){let e=_.replace(/^models\//,""),t=a.messages.map(e=>{if("string"==typeof e.content)return{role:e.role,content:e.content};if(Array.isArray(e.content)){let t=e.content.map(e=>"text"===e.type&&"string"==typeof e.text?{type:"text",text:e.text}:"image_url"===e.type&&e.image_url?.url?{type:"image_url",image_url:{url:e.image_url.url}}:null).filter(Boolean);return{role:e.role,content:t}}return{role:e.role,content:"[RoKey: Invalid content structure for Google]"}});if(0===t.length)throw n={message:"No processable message content found for Google provider after filtering.",status:400};let d={model:e,messages:t,stream:a.stream};void 0!==a.temperature&&(d.temperature=a.temperature),void 0!==a.max_tokens&&(d.max_tokens=a.max_tokens),void 0!==a.top_p&&(d.top_p=a.top_p);let p={...m};p.headers={...m.headers,Authorization:`Bearer ${r}`,Connection:"keep-alive","Keep-Alive":"timeout=30, max=100","User-Agent":"RoKey/1.0 (Performance-Optimized)",Origin:"https://rokey.app","Cache-Control":"no-cache",Priority:"u=1, i"},p.body=JSON.stringify(d);let g=await X("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",p);if(c=new Date,i=g.status,u=g.headers,!g.ok){let e=await g.json().catch(()=>({error:{message:g.statusText}})),t=e?.error?.message||g.statusText;throw Array.isArray(e)&&e[0]?.error?.message&&(t=e[0].error.message),n={message:`Google Error: ${t}`,status:g.status,provider_error:e}}if(a.stream){if(!g.body)throw n={message:"Google stream body null",status:500};l={note:"streamed"};let{createFirstTokenTrackingStream:t}=await s.e(9704).then(s.bind(s,99704)),r=t(g.body,"Google",e);o=new Response(r,{status:g.status,statusText:g.statusText,headers:g.headers})}else l=await g.json()}else if(e?.toLowerCase()==="anthropic"){let e,t=a.max_tokens||2048,d=a.messages.filter(t=>"system"!==t.role||("string"==typeof t.content&&(e=t.content),!1)).map(e=>({role:e.role,content:e.content}));if(0===d.length||"user"!==d[0].role)throw n={message:"Invalid messages format for Anthropic: Must contain at least one user message and start with user after system filter.",status:400};let p={model:_,messages:d,max_tokens:t,stream:a.stream};e&&(p.system=e),void 0!==a.temperature&&(p.temperature=a.temperature);let g={...m};g.headers={...m.headers,"x-api-key":r,"anthropic-version":"2023-06-01",Origin:"https://rokey.app"},g.body=JSON.stringify(p);let f=await X("https://api.anthropic.com/v1/messages",g);if(c=new Date,i=f.status,u=f.headers,!f.ok){let e=await f.json().catch(()=>({error:{message:f.statusText}}));throw n={message:`Anthropic Error: ${e?.error?.message||f.statusText} (Type: ${e?.error?.type})`,status:f.status,provider_error:e}}if(a.stream){if(!f.body)throw n={message:"Anthropic stream body null",status:500};let{createFirstTokenTrackingStream:e}=await s.e(9704).then(s.bind(s,99704)),t=e(f.body,"Anthropic",_);o=new Response(t,{status:f.status,headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive"}}),l={note:"streamed"}}else{let e=await f.json(),t=e.content?.find(e=>"text"===e.type)?.text||"";l={id:e.id||`anthropic-exPR-${Date.now()}`,object:"chat.completion",created:Math.floor(Date.now()/1e3),model:e.model||_,choices:[{index:0,message:{role:"assistant",content:t},finish_reason:e.stop_reason?.toLowerCase()||"stop"}],usage:{prompt_tokens:e.usage?.input_tokens,completion_tokens:e.usage?.output_tokens,total_tokens:(e.usage?.input_tokens||0)+(e.usage?.output_tokens||0)}}}}else if(e?.toLowerCase()==="deepseek"){let{custom_api_config_id:e,role:t,...d}=a,p={...d,model:_,messages:a.messages,stream:a.stream};Object.keys(p).forEach(e=>void 0===p[e]&&delete p[e]);let g={...m};g.headers={...m.headers,Authorization:`Bearer ${r}`,Origin:"https://rokey.app"},g.body=JSON.stringify(p);let f=await X("https://api.deepseek.com/chat/completions",g);if(c=new Date,i=f.status,u=f.headers,!f.ok){let e=await f.json().catch(()=>({error:{message:f.statusText}}));throw n={message:`DeepSeek Error: ${e?.error?.message||f.statusText} (Type: ${e?.error?.type})`,status:f.status,provider_error:e}}if(a.stream){if(!f.body)throw n={message:"DeepSeek stream body null",status:500};let{createFirstTokenTrackingStream:e}=await s.e(9704).then(s.bind(s,99704)),t=e(f.body,"DeepSeek",_);o=new Response(t,{status:f.status,statusText:f.statusText,headers:f.headers}),l={note:"streamed"}}else l=await f.json()}else if(e?.toLowerCase()==="xai"){let{custom_api_config_id:e,role:t,...d}=a,p={...d,model:_,messages:a.messages,stream:a.stream||!1};"number"==typeof a.temperature&&(p.temperature=a.temperature),"number"==typeof a.max_tokens&&(p.max_tokens=a.max_tokens),"number"==typeof a.top_p&&(p.top_p=a.top_p),"number"==typeof a.frequency_penalty&&(p.frequency_penalty=a.frequency_penalty),"number"==typeof a.presence_penalty&&(p.presence_penalty=a.presence_penalty);let g={...m};g.headers={...m.headers,Authorization:`Bearer ${r}`,Origin:"https://rokey.app"},g.body=JSON.stringify(p);let f=await X("https://api.x.ai/v1/chat/completions",g);if(c=new Date,i=f.status,u=f.headers,!f.ok){let e=await f.json().catch(()=>({error:{message:f.statusText}}));throw n={message:`XAI/Grok Error: ${e?.error?.message||f.statusText} (Type: ${e?.error?.type})`,status:f.status,provider_error:e}}if(a.stream){if(!f.body)throw n={message:"XAI/Grok stream body null",status:500};let{createFirstTokenTrackingStream:e}=await s.e(9704).then(s.bind(s,99704)),t=e(f.body,"XAI",_);o=new Response(t,{status:f.status,headers:{...Object.fromEntries(f.headers),"Content-Type":"text/event-stream"}}),l={note:"streamed"}}else l=await f.json()}else throw n={message:`Provider '${e}' is configured but not supported by RoKey proxy (executeProviderRequest).`,status:501,internal:!0};if(!a.stream&&l&&a.messages&&t){let s=O(a.messages,t,a.temperature);if(I.set(s,{response:l,timestamp:Date.now(),provider:e||"unknown",model:t}),I.size>1e3){let e=Array.from(I.entries());e.sort((e,t)=>e[1].timestamp-t[1].timestamp);let t=Math.floor(.2*e.length);for(let s=0;s<t;s++)I.delete(e[s][0])}}return{success:!0,response:o,responseData:l,responseHeaders:u,status:i,error:null,llmRequestTimestamp:d,llmResponseTimestamp:c}}catch(r){let e=n||r,t="ProviderCommsError",s="";return"AbortError"===r.name?(t="TimeoutError",s="Request timed out after 30 seconds"):r.message?.includes("fetch failed")?(t="NetworkError",s="Network connection failed - check internet connectivity"):"ENOTFOUND"===r.code?(t="DNSError",s="DNS resolution failed - check network settings"):"ECONNREFUSED"===r.code&&(t="ConnectionRefused",s="Connection refused by server"),{success:!1,status:e.status||500,error:e.provider_error||{message:`${e.message}${s?` (${s})`:""}`,type:e.internal?"RoKeyInternal":t,diagnostic:s},llmRequestTimestamp:d||new Date,llmResponseTimestamp:c||new Date,response:void 0,responseData:void 0,responseHeaders:u}}}async function W(e){let t,s,r=new Date,a=await (0,l.createSupabaseServerClientOnRequest)();performance.now();let i=e.headers.get("Authorization"),n=process.env.ROKEY_API_ACCESS_TOKEN;if(!n)return o.NextResponse.json({error:"Server configuration error: API access token not configured."},{status:500});if(!i||!i.startsWith("Bearer "))return o.NextResponse.json({error:"Unauthorized: Missing or invalid Authorization header format."},{status:401});if(i.substring(7)!==n)return o.NextResponse.json({error:"Unauthorized: Invalid API token."},{status:401});let c=null,m="unknown",_=null,g=null,f=null,y=null,h=null,w=null,v=null,k=null,x=null,b=null,A=null,S=null,C=null,R=null,T=null,D=!1,I=null;try{let i=await e.json();i&&"_internal_user_id"in i&&(S=i._internal_user_id,delete i._internal_user_id,delete i._internal_user_email);let n=U.safeParse(i);if(!n.success)throw y={message:"Invalid request body",issues:n.error.flatten().fieldErrors,status:400};_=(t=n.data).custom_api_config_id;let l=t.messages?.[t.messages.length-1];if(l?.role==="user"&&"string"==typeof l.content){let e=l.content.toLowerCase().trim();["continue","continue please","keep going","go on","more","more please","finish","complete","what next","then what","and then"].includes(e)}let u=Date.now();for(let[e,t]of $.entries())u-t.lastActivity>18e5&&$.delete(e);if(t.specific_api_key_id)try{let{data:e,error:s}=await a.from("api_keys").select("*").eq("id",t.specific_api_key_id).eq("custom_api_config_id",_).eq("status","active").single();if(s||!e)throw y={message:`Specific API key ${t.specific_api_key_id} not found or not active in this configuration.`,status:404};let r=(0,d.Y)(e.encrypted_api_key),i=await V(e.provider,e.predefined_model_id,r,t);if(c=e.id,g=e.predefined_model_id,f=e.provider,v=i.llmRequestTimestamp,k=i.llmResponseTimestamp,h=i.status??null,A=i.responseHeaders??null,m="specific_key_retry",i.success){if(w=i.responseData||{note:"streamed via specific key routing"},t.stream&&i.response)return i.response;if(!t.stream&&void 0!==i.responseData)return o.NextResponse.json(w,{status:h||200,headers:i.responseHeaders})}else throw y={message:`Specific API key ${e.id} failed: ${i.error?.message||"Unknown error"}`,status:i.status||500,provider_error:i.error},w=i.error,y}catch(e){y||(y={message:`Error using specific API key: ${e.message}`,status:500})}let C=`${_}:${t.messages?.[t.messages.length-1]?.content?.substring(0,100)||"default"}`,R=q.get(C);if(R&&Date.now()-R.timestamp<18e5){let e=await V(R.provider,R.model,R.apiKey,t);if(e.success)return K(e,t);q.delete(C)}let[T,D]=await Promise.allSettled([N(_),a.from("custom_api_configs").select("id, name, user_id, routing_strategy, routing_strategy_params").eq("id",_).single()]);if("rejected"===D.status||!D.value.data){let e="rejected"===D.status?D.reason:D.value.error;throw y={message:"Custom API Configuration not found or error fetching it.",status:404,provider_error:e}}let E=D.value.data;I=E.user_id;let O=E.routing_strategy,L=E.routing_strategy_params,z=O&&"none"!==O&&"auto"!==O,Y=performance.now(),[G,J]=await Promise.allSettled([(async()=>{let e=t.messages,s=null,r="",i=[];if("fulfilled"===T.status&&T.value&&(s=T.value.trainingData,e=await j(e,s)),I&&_){let t=e.filter(e=>"user"===e.role);if(t.length>0){let s=t[t.length-1],n="string"==typeof s.content?s.content:s.content?.[0]?.text||"";if(n.trim()){let t=await P(n,_,I,a);if(r=t.context,i=t.sources,r){let t=e.findIndex(e=>"system"===e.role),s=`

=== IMPORTANT KNOWLEDGE BASE CONTEXT ===
${r}
=== END KNOWLEDGE BASE ===

IMPORTANT: The above knowledge base contains specific information that should be prioritized when answering questions. Use this information to provide detailed, accurate responses. If the user asks about topics covered in the knowledge base, draw extensively from this content rather than giving generic responses.`;t>=0?e[t].content+=s:e.unshift({role:"system",content:`You are a helpful AI assistant.${s}`})}}}}return{enhancedMessages:e,trainingData:s,documentContext:r,documentSources:i}})(),(async()=>"intelligent_role"===O?await M(E,_,t,a,e):"strict_fallback"===O?await F(L,_,a):"complexity_round_robin"===O?await B(E,t,a):{targetApiKeyData:null,roleUsedState:"no_strategy"})()]),X=performance.now(),W=null,Q=[];if("fulfilled"===G.status&&(t.messages=G.value.enhancedMessages,Q=G.value.documentSources||[],t.messages.find(e=>"system"===e.role),Q.length),"fulfilled"===J.status){if("hybridResponse"in J.value&&J.value.hybridResponse)return m=J.value.roleUsedState,f="hybrid_orchestration",g="crewai_autogen_hybrid",v=new Date,k=new Date,h=200,w={note:"Revolutionary hybrid orchestration response"},J.value.hybridResponse;W=J.value.targetApiKeyData,m=J.value.roleUsedState,"classifiedComplexityLevel"in J.value&&void 0!==J.value.classifiedComplexityLevel&&(x=J.value.classifiedComplexityLevel),"classifiedComplexityLLM"in J.value&&void 0!==J.value.classifiedComplexityLLM&&(b=J.value.classifiedComplexityLLM)}else m="routing_failed";if(!W)if(z)if(W=await H(a,_,t.role))if(t.role){let{data:e}=await a.from("api_key_role_assignments").select("api_key_id").eq("custom_api_config_id",_).eq("api_key_id",W.id).eq("role_name",t.role).maybeSingle();m=e?p.roleRouting(t.role):p.defaultKeySuccess()}else m=p.defaultKeySuccess();else m=`${m}_then_fb_failed_completely`;else{let{data:e,error:s}=await a.from("api_keys").select("*").eq("custom_api_config_id",_).eq("status","active");if(s)y={message:"Database error fetching keys for default routing.",status:500,provider_error:s},m="default_db_error_fetching_keys";else if(e&&0!==e.length){let s=E.routing_strategy_params||{};"number"==typeof s._default_rr_idx&&s._default_rr_idx;let r=[...e].sort((e,t)=>e.id.localeCompare(t.id)),i=r.map(async(e,s)=>{let r=s+1;try{let s=(0,d.Y)(e.encrypted_api_key),a=await V(e.provider,e.predefined_model_id,s,t);if(a.success)return{success:!0,key:e,result:a,attemptNumber:r};return{success:!1,key:e,error:a.error,status:a.status,attemptNumber:r}}catch(t){return{success:!1,key:e,error:t,status:t.status||500,attemptNumber:r}}});try{let e=await Promise.allSettled(i),n=null,l=null,u=e.length;for(let t of e)if("fulfilled"===t.status&&t.value.success){n=t.value;break}else"fulfilled"===t.status&&(l=t.value);if(n){let e=n.key,i=n.result,l=n.attemptNumber;if(s._default_rr_idx=(r.findIndex(t=>t.id===e.id)+1)%r.length,E.routing_strategy_params=s,setImmediate(async()=>{let{error:e}=await a.from("custom_api_configs").update({routing_strategy_params:s}).eq("id",E.id)}),c=e.id,g=e.predefined_model_id,f=e.provider,v=i?.llmRequestTimestamp||null,k=i?.llmResponseTimestamp||null,h=i?.status??null,A=i?.responseHeaders??null,m=p.defaultKeySuccess(l),y=null,t.stream&&i?.response)return w=i.responseData||{note:"streamed via parallel default routing"},i.response;if(!t.stream&&i?.responseData!==void 0)return w=i.responseData,o.NextResponse.json(w,{status:h||200,headers:i.responseHeaders});w={error:(y={message:`Internal error: Key ${e.id} success but no response data/stream.`,status:500}).message},h=500}else l?(w=l.error,h=l.status??null,y={message:`All ${u} key(s) for parallel default routing failed. Last error from key ${l.key.id}: ${l.error?.message||"Unknown"}`,status:l.status||500,provider_error:l.error},m=p.allKeysFailed(u)):(y={message:`All ${u} key(s) for parallel default routing failed with unknown errors.`,status:500},m=`default_all_parallel_attempts_failed_${u}`)}catch(e){y={message:`Parallel default routing failed: ${e.message}`,status:500},m="default_parallel_execution_error"}!y&&!c&&r.length>0&&(y={message:`All ${r.length} key(s) for default routing were attempted but failed. Status: ${h||"N/A"}. An internal error may have occurred.`,status:h||500,provider_error:null},r.length>0&&(m=p.allKeysFailed(r.length))),y&&!c&&(m=`default_all_attempts_failed_final_err_summary_${y?.message?.substring(0,70)}`)}else y={message:`No active keys configured for RoKey Config ID ${_} to use with default routing.`,status:404},m="default_no_active_keys_for_config"}if(W&&!y)if(c=W.id,g=W.predefined_model_id,f=W.provider){if(!s)try{s=(0,d.Y)(W.encrypted_api_key)}catch(e){y={message:`API Key decryption failed for selected key ${W.id}.`,status:500}}}else y={message:`Selected API key '${c}' does not have a provider configured. Please check the API key settings.`,status:500};else W||y||(y={message:`RoKey could not resolve an API key for this request. Last routing state: ${m||"unknown"}.`,status:404});if(W&&s&&!y&&f&&z){let i=await V(f,g,s,t);if(v=i.llmRequestTimestamp,k=i.llmResponseTimestamp,h=i.status??null,A=i.responseHeaders??null,i.success)return w=i.responseData||{note:"streamed via explicit strategy"},K(i,t,f,g||void 0,s,{roleUsed:m,routingStrategy:O,complexityLevel:x||void 0,processingTime:X-Y});{let s={keyId:c,provider:f,status:i.status,error:i.error,strategy:O};try{let s={custom_api_config_id:_,api_key_id:c,user_id:I||S,predefined_model_id:g,role_requested:t?.role||null,role_used:`${m}_FAILED`,ip_address:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||null,request_timestamp:r.toISOString(),response_timestamp:new Date().toISOString(),status_code:i.status||500,request_payload_summary:t?{messages_count:t.messages?.length,model_requested_passthrough:t.model,stream:t.stream,temp:t.temperature,max_tok:t.max_tokens}:{note:"Request body was not available."},response_payload_summary:{error_type:i.error?.type||i.error?.error?.type||"provider_error",error_message_summary:i.error?.message||i.error?.error?.message||i.error?.details?.message||"Unknown error",provider_status:i.error?.status||i.status,full_error_details:i.error,fallback_initiated:!0},error_message:`ORIGINAL FAILURE: ${i.error?.message||i.error?.error?.message||i.error?.details?.message||JSON.stringify(i.error)||"Unknown error"}. Fallback will be attempted.`,error_source:f,error_details_zod:null,llm_provider_name:f,llm_model_name:g,llm_provider_status_code:i.status,llm_provider_latency_ms:i.llmResponseTimestamp&&i.llmRequestTimestamp?i.llmResponseTimestamp.getTime()-i.llmRequestTimestamp.getTime():null,processing_duration_ms:new Date().getTime()-r.getTime(),classified_role_llm:null,classified_complexity_level:x,classified_complexity_llm:b,cost:null,input_tokens:null,output_tokens:null,is_multimodal:!!t?.messages&&t.messages.some(e=>Array.isArray(e.content)&&e.content.some(e=>"image_url"===e.type))},{error:n}=await a.from("request_logs").insert(s)}catch(e){}let n=!1,l=await H(a,_,void 0);if(l&&l.id!==c){let e;try{e=(0,d.Y)(l.encrypted_api_key)}catch(t){e=""}if(e){let s=await V(l.provider,l.predefined_model_id,e,t);if(s.success){if(m=`${m}_FALLBACK_SUCCESS_default`,n=!0,v=s.llmRequestTimestamp,k=s.llmResponseTimestamp,h=s.status??null,A=s.responseHeaders??null,c=l.id,g=l.predefined_model_id,f=l.provider,t.stream&&s.response)return w=s.responseData||{note:"streamed via intelligent fallback to default"},s.response;if(!t.stream&&void 0!==s.responseData)return w=s.responseData,o.NextResponse.json(w,{status:h||200,headers:s.responseHeaders})}}}if(!n){let{data:e,error:s}=await a.from("api_keys").select("*").eq("custom_api_config_id",_).eq("status","active");if(s);else if(e&&e.length>0){let s=new Set([c]);l&&s.add(l.id);let r=e.filter(e=>!s.has(e.id));if(r.length>0){let e=r.map(async e=>{try{let s=(0,d.Y)(e.encrypted_api_key),r=await V(e.provider,e.predefined_model_id,s,t);if(r.success)return{success:!0,key:e,result:r};return{success:!1,key:e,error:r.error}}catch(t){return{success:!1,key:e,error:t}}});try{for(let s of(await Promise.allSettled(e)))if("fulfilled"===s.status&&s.value.success){let e=s.value,r=e.key,a=e.result;if(m=`${m}_PARALLEL_FALLBACK_SUCCESS_${r.id}`,n=!0,v=a?.llmRequestTimestamp||null,k=a?.llmResponseTimestamp||null,h=a?.status??null,A=a?.responseHeaders??null,c=r.id,g=r.predefined_model_id,f=r.provider,t.stream&&a?.response)return w=a.responseData||{note:`streamed via parallel fallback to ${r.id}`},a.response;if(!t.stream&&a?.responseData!==void 0)return w=a.responseData,o.NextResponse.json(w,{status:h||200,headers:a.responseHeaders});break}}catch(e){}}}}n||(m=`${m}_all_fallbacks_failed`,y={message:`Intelligent routing (${s.strategy}) failed for key ${s.keyId} (${s.provider}). All fallback attempts also failed. Original error: ${s.error?.message||"Unknown"}`,status:s.status||500,provider_error:s.error,fallback_attempted:!0},w=s.error,h=s.status??null)}}else W&&!s&&!y&&f&&z?y||(y={message:`API key ${c} selected but decryption failed (safeguard).`,status:500}):W&&s&&!y&&!f&&z&&!y&&(y={message:`API key ${c} selected but has no provider configured (safeguard).`,status:500});if(y){let e=y.status||500,t=y.message||"An unexpected internal server error occurred.",s=y.issues,r=y.provider_error;return!h&&y.status&&y.provider_error&&(h=y.status),!w&&t&&(w={error:{message:t,...r&&{details:r}}}),o.NextResponse.json({error:t,...s&&{issues:s},...r&&{provider_error_details:r}},{status:e})}if(!y&&!c)return y={message:"Critical internal error: No API key processed and no explicit error state.",status:500},o.NextResponse.json({error:y.message},{status:y.status});if(w&&!t?.stream)return o.NextResponse.json(w,{status:h||200});return o.NextResponse.json({error:"An unexpected critical server error occurred."},{status:500})}finally{let s=new Date,i=s.getTime()-r.getTime(),n=null;v&&k&&(n=k.getTime()-v.getTime());let o=i-(n||0);performance.now(),t?.messages&&(D=t.messages.some(e=>Array.isArray(e.content)&&e.content.some(e=>"image_url"===e.type))),w?.usage?(R=w.usage.prompt_tokens||w.usage.input_tokens||null,T=w.usage.completion_tokens||w.usage.output_tokens||null):f?.toLowerCase()==="google"&&w?.promptFeedback?.tokenCount!==void 0&&(R=w.promptFeedback.tokenCount,T=w.candidates?.[0]?.tokenCount||null),function(e,t){if(!e)return!1;let s=e.toLowerCase();if("deepseek"===s)return!0;if(("google"===s||"gemini"===s)&&t)for(let e of["x-ratelimit-limit","x-ratelimit-requests-limit","x-goog-quota-limit","quota-limit"]){let s=t.get(e);if(s){let e=parseInt(s);if(!isNaN(e))return e<=60}}return!1}(f,A||void 0)?C=0:w?.usage?.cost&&f?.toLowerCase()==="openrouter"?C=1e-6*w.usage.cost:null!==R&&null!==T&&g&&(setImmediate(async()=>{try{let{data:e,error:t}=await a.from("models").select("input_token_price, output_token_price").eq("id",g).single();if(!t&&e?.input_token_price&&e?.output_token_price&&null!==R&&null!==T){let t=R*e.input_token_price,s=T*e.output_token_price;l&&await a.from("request_logs").update({cost:t+s}).eq("custom_api_config_id",l).eq("request_timestamp",r.toISOString())}}catch(e){}}),C=null);let l=_||t?.custom_api_config_id;l?setImmediate(async()=>{try{let a=I||S,i={custom_api_config_id:l,api_key_id:c,user_id:a,predefined_model_id:g,role_requested:t?.role||null,role_used:m,ip_address:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||null,request_timestamp:r.toISOString(),response_timestamp:s.toISOString(),status_code:y?y.status||500:h||200,request_payload_summary:t?{messages_count:t.messages?.length,model_requested_passthrough:t.model,stream:t.stream,temp:t.temperature,max_tok:t.max_tokens}:{note:"Request body was not available or Zod validation failed."},response_payload_summary:{usage:w?.usage,finish_reason:w?.choices?.[0]?.finish_reason,error_type:w?.error?.type,error_message_summary:w?.error?.message,full_error_details:w?.error,is_fallback_success:m?.includes("FALLBACK_SUCCESS")||!1,original_failure_summary:m?.includes("FAILED")?"See previous log entry for original failure details":null},error_message:m?.includes("FALLBACK_SUCCESS")?"FALLBACK SUCCESS: Original model failed, successfully used fallback model. Check previous log entry for failure details.":y?.message?y.message:w?.error?.message?w.error.message:null,error_source:y?y.provider_error&&f?f:"RoKey":w?.error?f:null,error_details_zod:y?.issues?JSON.stringify(y.issues):null,llm_provider_name:f,llm_model_name:g,llm_provider_status_code:h,llm_provider_latency_ms:n,processing_duration_ms:o,classified_role_llm:null,classified_complexity_level:x,classified_complexity_llm:b,cost:C,input_tokens:R,output_tokens:T,is_multimodal:D},d=(0,u.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}}),{error:p}=await d.from("request_logs").insert(i)}catch(e){}}):y&&setImmediate(async()=>{try{let a={custom_api_config_id:null,api_key_id:null,user_id:S,predefined_model_id:null,role_requested:t?.role||null,role_used:null,ip_address:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||null,request_timestamp:r.toISOString(),response_timestamp:s.toISOString(),status_code:y.status||500,request_payload_summary:{note:"Early error, request body may be malformed.",custom_api_config_id_attempted:_},response_payload_summary:{error_message_summary:y.message?.substring(0,100)},error_message:y.message,error_source:"RoKey",error_details_zod:y.issues?JSON.stringify(y.issues):null,llm_provider_name:null,llm_model_name:null,llm_provider_status_code:null,llm_provider_latency_ms:null,processing_duration_ms:i,classified_role_llm:null,classified_complexity_level:null,classified_complexity_llm:null,cost:null,input_tokens:null,output_tokens:null,is_multimodal:!1},n=(0,u.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}}),{error:o}=await n.from("request_logs").insert(a)}catch(e){}})}}async function Q(e){return o.NextResponse.json({},{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}let Z=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/v1/chat/completions/route",pathname:"/api/v1/chat/completions",filename:"route",bundlePath:"app/api/v1/chat/completions/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\v1\\chat\\completions\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:ee,workUnitAsyncStorage:et,serverHooks:es}=Z;function er(){return(0,n.patchFetch)({workAsyncStorage:ee,workUnitAsyncStorage:et})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[7719,580,9398,3410,5697,563],()=>s(98811));module.exports=r})();