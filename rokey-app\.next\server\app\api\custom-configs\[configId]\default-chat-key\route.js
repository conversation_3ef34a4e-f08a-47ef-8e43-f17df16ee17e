(()=>{var e={};e.id=7086,e.ids=[1489,7086],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{Q:()=>a,createSupabaseServerClientOnRequest:()=>o});var s=r(34386),i=r(44999);async function o(){let e=await (0,i.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}function a(e){return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},92520:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>c});var i=r(96559),o=r(48088),a=r(37719),u=r(32190),n=r(2507);async function c(e,{params:t}){let r=await (0,n.createSupabaseServerClientOnRequest)(),{configId:s}=await t;if(!s)return u.NextResponse.json({error:"Configuration ID is required"},{status:400});try{let{data:e,error:t}=await r.from("api_keys").select("id, label, provider, predefined_model_id, status, custom_api_config_id, is_default_general_chat_model").eq("custom_api_config_id",s).eq("is_default_general_chat_model",!0).maybeSingle();if(t)return u.NextResponse.json({error:"Failed to fetch default chat key",details:t.message},{status:500});if(!e)return u.NextResponse.json(null,{status:200});return u.NextResponse.json(e,{status:200})}catch(e){return u.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/custom-configs/[configId]/default-chat-key/route",pathname:"/api/custom-configs/[configId]/default-chat-key",filename:"route",bundlePath:"app/api/custom-configs/[configId]/default-chat-key/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\custom-configs\\[configId]\\default-chat-key\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:h}=p;function f(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,580,9398,3410],()=>r(92520));module.exports=s})();