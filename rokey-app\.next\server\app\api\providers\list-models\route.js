(()=>{var e={};e.id=7790,e.ids=[7790],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58455:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{POST:()=>a});var i=t(96559),o=t(48088),n=t(37719),p=t(32190),u=t(39398);async function a(e){try{let e="https://hpkzzhpufhbxtxqaugjh.supabase.co",r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8";if(!e||!r)return p.NextResponse.json({error:"Server configuration error: Supabase credentials missing."},{status:500});let t=(0,u.createClient)(e,r),{data:s,error:i}=await t.from("models").select(`
        id,
        name,
        display_name,
        description,
        provider_id,
        family,
        context_window,
        input_token_limit,
        output_token_limit,
        modality,
        is_public
      `).order("provider_id",{ascending:!0}).order("name",{ascending:!0});if(i)return p.NextResponse.json({error:`Failed to fetch models: ${i.message}`},{status:500});if(!s)return p.NextResponse.json({models:[]},{status:200});let o=s.map(e=>({id:e.id,name:e.name,display_name:e.display_name||e.name,description:e.description,provider_id:e.provider_id,family:e.family,context_window:e.context_window,input_token_limit:e.input_token_limit,output_token_limit:e.output_token_limit,modality:e.modality||"unknown",is_public:e.is_public})),n=["text","multimodal","image"],a=o.filter(e=>e.modality&&n.includes(e.modality));return p.NextResponse.json({models:a},{status:200})}catch(e){return p.NextResponse.json({error:`An unexpected error occurred: ${e.message||"Unknown error"}`},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/providers/list-models/route",pathname:"/api/providers/list-models",filename:"route",bundlePath:"app/api/providers/list-models/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\providers\\list-models\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:l,serverHooks:m}=d;function x(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:l})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,580,9398],()=>t(58455));module.exports=s})();