(()=>{var e={};e.id=3544,e.ids=[2842,3544],e.modules={2842:(e,t,r)=>{"use strict";r.d(t,{trainingDataCache:()=>s});class a{set(e,t,r){this.cache.set(e,{data:t,timestamp:Date.now(),jobId:r})}get(e){let t=this.cache.get(e);return t?Date.now()-t.timestamp>this.TTL?(this.cache.delete(e),null):t:null}invalidate(e){return this.cache.delete(e)}clear(){this.cache.clear()}cleanup(){let e=Date.now();for(let[t,r]of this.cache.entries())e-r.timestamp>this.TTL&&this.cache.delete(t)}getStats(){return{size:this.cache.size,keys:Array.from(this.cache.keys())}}constructor(){this.cache=new Map,this.TTL=3e5}}let s=new a;setInterval(()=>{s.cleanup()},6e5)},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},36239:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>u,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var a={};r.r(a),r.d(a,{POST:()=>p});var s=r(96559),i=r(48088),n=r(37719),c=r(32190),o=r(2842);async function p(e){try{let{type:t,configId:r}=await e.json();if(!t||!r)return c.NextResponse.json({error:"Missing type or configId"},{status:400});let a=!1;if("training"!==t)return c.NextResponse.json({error:"Invalid cache type"},{status:400});return a=o.trainingDataCache.invalidate(r),c.NextResponse.json({success:!0,invalidated:a,message:`${t} cache ${a?"invalidated":"was not cached"} for config: ${r}`})}catch(e){return c.NextResponse.json({error:"Failed to invalidate cache"},{status:500})}}let u=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/cache/invalidate/route",pathname:"/api/cache/invalidate",filename:"route",bundlePath:"app/api/cache/invalidate/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\cache\\invalidate\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:h}=u;function v(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[7719,580],()=>r(36239));module.exports=a})();