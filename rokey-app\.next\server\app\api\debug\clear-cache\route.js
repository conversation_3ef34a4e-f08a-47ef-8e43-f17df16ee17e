(()=>{var e={};e.id=7640,e.ids=[7640],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},93016:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>u,serverHooks:()=>l,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{POST:()=>i});var a=t(96559),n=t(48088),o=t(37719),c=t(32190);async function i(){try{return c.NextResponse.json({success:!0,message:"Cache clearing instructions",instructions:["Open browser DevTools (F12)","Right-click on the refresh button",'Select "Empty Cache and Hard Reload"',"Or use Ctrl+Shift+R (Windows) / Cmd+Shift+R (Mac)"],clientScript:`
        // Client-side cache clearing
        if ('caches' in window) {
          caches.keys().then(names => {
            names.forEach(name => {
              caches.delete(name);
            });
          });
        }
        
        // Clear localStorage and sessionStorage
        localStorage.clear();
        sessionStorage.clear();
        
        console.log('✅ Browser caches cleared');
      `})}catch(e){return c.NextResponse.json({error:"Failed to provide cache clearing instructions"},{status:500})}}let u=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/debug/clear-cache/route",pathname:"/api/debug/clear-cache",filename:"route",bundlePath:"app/api/debug/clear-cache/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\debug\\clear-cache\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:d,serverHooks:l}=u;function h(){return(0,o.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:d})}},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,580],()=>t(93016));module.exports=s})();