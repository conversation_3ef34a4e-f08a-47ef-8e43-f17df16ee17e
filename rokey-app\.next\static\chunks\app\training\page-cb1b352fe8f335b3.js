(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7637],{3408:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(95155),s=r(12115),a=r(73360);function i(){let[e,t]=(0,s.useState)([]),[r,i]=(0,s.useState)(""),[o,l]=(0,s.useState)([]),[c,d]=(0,s.useState)(!1),[u,m]=(0,s.useState)(null),[h,p]=(0,s.useState)(null),[x,g]=(0,s.useState)(""),f=async e=>{if(e)try{let r=await fetch("/api/training/jobs?custom_api_config_id=".concat(e));if(r.ok){let e=await r.json();if(e.length>0){var t;let r=e[0];(null==(t=r.training_data)?void 0:t.raw_prompts)&&g(r.training_data.raw_prompts)}}}catch(e){}};(0,s.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/custom-configs");if(!e.ok)throw Error("Failed to fetch configurations");let r=await e.json();t(r),r.length>0&&(i(r[0].id),f(r[0].id))}catch(e){m("Failed to load configurations: ".concat(e.message))}})()},[]),(0,s.useEffect)(()=>{r&&f(r)},[r]);let y=e=>{let t={system_instructions:"",examples:[],behavior_guidelines:"",general_instructions:""};for(let r of e.split("\n").filter(e=>e.trim())){let e=r.trim();if(e.startsWith("SYSTEM:"))t.system_instructions+=e.replace("SYSTEM:","").trim()+"\n";else if(e.startsWith("BEHAVIOR:"))t.behavior_guidelines+=e.replace("BEHAVIOR:","").trim()+"\n";else if(e.includes("→")||e.includes("->")){let r=e.includes("→")?"→":"->",n=e.split(r);if(n.length>=2){let e=n[0].trim(),s=n.slice(1).join(r).trim();t.examples.push({input:e,output:s})}}else e.length>0&&(t.general_instructions+=e+"\n")}return t},b=async()=>{if(!r||!x.trim())return void m("Please select an API configuration and provide training prompts.");if(!c){d(!0),m(null),p(null);try{var t;let n=y(x),s=(null==(t=e.find(e=>e.id===r))?void 0:t.name)||"Unknown Config",a={custom_api_config_id:r,name:"".concat(s," Training - ").concat(new Date().toLocaleDateString()),description:"Training job for ".concat(s," with ").concat(n.examples.length," examples"),training_data:{processed_prompts:n,raw_prompts:x.trim(),last_prompt_update:new Date().toISOString()},parameters:{training_type:"prompt_engineering",created_via:"training_page",version:"1.0"}},i=await fetch("/api/training/jobs/upsert",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!i.ok){let e=await i.text();throw Error("Failed to save training job: ".concat(i.status," ").concat(e))}let o=await i.json(),l="updated"===o.operation,c="".concat(l?"\uD83D\uDD04":"\uD83C\uDF89"," Prompt Engineering ").concat(l?"updated":"completed"," successfully!\n\n")+'Your "'.concat(s,'" configuration has been ').concat(l?"updated":"enhanced"," with:\n")+"• ".concat(n.examples.length," training examples\n")+"• Custom system instructions and behavior guidelines\n\n✨ All future chats using this configuration will automatically:\n• Follow your training examples\n• Apply your behavior guidelines\n• Maintain consistent personality and responses\n\n"+"\uD83D\uDE80 Try it now in the Playground to see your ".concat(l?"updated":"enhanced"," model in action!\n\n")+"\uD83D\uDCA1 Your training prompts remain here so you can modify them anytime.";p(c)}catch(e){m("Failed to create prompt engineering: ".concat(e.message))}finally{d(!1)}}};return(0,n.jsx)("div",{className:"min-h-screen bg-[#faf8f5] p-6",children:(0,n.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"AI Training & Enhancement"}),(0,n.jsx)("p",{className:"text-lg text-gray-600 max-w-3xl",children:"Enhance your AI models with custom prompts and knowledge documents. Upload your proprietary content to create domain-specific AI assistants."})]}),u&&(0,n.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-xl p-4",children:(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("svg",{className:"w-5 h-5 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,n.jsx)("p",{className:"text-red-800 text-sm font-medium",children:u})]})}),h&&(0,n.jsx)("div",{className:"mb-6 bg-green-50 border border-green-200 rounded-xl p-4",children:(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("svg",{className:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,n.jsx)("p",{className:"text-green-800 text-sm font-medium",children:h})]})}),(0,n.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg p-8 mb-8",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,n.jsx)("div",{className:"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center",children:(0,n.jsx)("svg",{className:"w-5 h-5 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Knowledge Documents"}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:"Upload documents to enhance your AI with proprietary knowledge"})]})]}),(0,n.jsx)(a.A,{configId:r,onDocumentUploaded:()=>{}})]}),(0,n.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg p-8",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,n.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,n.jsx)("svg",{className:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Custom Prompts"}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:"Define behavior, examples, and instructions for your AI"})]})]}),(0,n.jsxs)("div",{className:"space-y-8",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"configSelect",className:"block text-sm font-medium text-gray-700 mb-2",children:"Select API Configuration"}),(0,n.jsxs)("select",{id:"configSelect",value:r,onChange:e=>i(e.target.value),className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm",children:[(0,n.jsx)("option",{value:"",children:"Choose which model to train..."}),e.map(e=>(0,n.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"trainingPrompts",className:"block text-sm font-medium text-gray-700 mb-2",children:"Custom Prompts & Instructions"}),(0,n.jsx)("textarea",{id:"trainingPrompts",value:x,onChange:e=>g(e.target.value),placeholder:"Enter your training prompts using these formats:\n\nSYSTEM: You are a helpful customer service agent for our company\nBEHAVIOR: Always be polite and offer solutions\n\nUser asks about returns → I'd be happy to help with your return! Let me check our policy for you.\nCustomer is frustrated → I understand your frustration. Let me see how I can resolve this for you.\n\nGeneral instructions can be written as regular text.",rows:12,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm resize-none font-mono"}),(0,n.jsxs)("div",{className:"mt-3 bg-blue-50 border border-blue-200 rounded-lg p-3",children:[(0,n.jsx)("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"Training Format Guide:"}),(0,n.jsxs)("ul",{className:"text-xs text-blue-800 space-y-1",children:[(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"SYSTEM:"})," Core instructions for the AI's role and personality"]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"BEHAVIOR:"})," Guidelines for how the AI should behave"]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Examples:"}),' Use "User input → Expected response" format']}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"General:"})," Any other instructions written as normal text"]})]})]})]}),(0,n.jsxs)("div",{className:"flex justify-between items-center pt-6 border-t border-gray-200",children:[(0,n.jsx)("div",{className:"flex space-x-3",children:(0,n.jsx)("button",{type:"button",className:"btn-secondary",onClick:()=>{confirm("Clear all training prompts?")&&g("")},children:"Clear Form"})}),(0,n.jsx)("button",{type:"button",onClick:b,disabled:!r||!x.trim()||c,className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:c?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Processing Prompts..."]}):"Save Prompts"})]})]})]})]})})}},42005:(e,t,r)=>{Promise.resolve().then(r.bind(r,3408))},58637:(e,t,r)=>{"use strict";r.d(t,{RI:()=>m,rA:()=>h,ZH:()=>p,iU:()=>x,kr:()=>g,_O:()=>f,X:()=>y});var n=r(12115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:a=2,absoluteStrokeWidth:i,className:d="",children:u,iconNode:m,...h}=e;return(0,n.createElement)("svg",{ref:t,...c,width:s,height:s,stroke:r,strokeWidth:i?24*Number(a)/Number(s):a,className:o("lucide",d),...!u&&!l(h)&&{"aria-hidden":"true"},...h},[...m.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let r=(0,n.forwardRef)((r,a)=>{let{className:l,...c}=r;return(0,n.createElement)(d,{ref:a,iconNode:t,className:o("lucide-".concat(s(i(e))),"lucide-".concat(e),l),...c})});return r.displayName=i(e),r},m=u("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),h=u("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),p=u("file",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]]),x=u("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),g=u("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),f=u("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]),y=u("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[274,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(42005)),_N_E=e.O()}]);