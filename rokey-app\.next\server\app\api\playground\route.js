(()=>{var e={};e.id=4881,e.ids=[4881],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75948:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{OPTIONS:()=>c,POST:()=>p});var o=t(96559),n=t(48088),a=t(37719),i=t(32190);let u=process.env.ROKEY_API_ACCESS_TOKEN;async function p(e){let{createSupabaseServerClientOnRequest:r}=await Promise.all([t.e(9398),t.e(3410),t.e(1489)]).then(t.bind(t,2507)),s=await r(),{data:{user:o},error:n}=await s.auth.getUser();if(n||!o)return i.NextResponse.json({error:"Unauthorized: You must be logged in to use the playground."},{status:401});if(!u)return i.NextResponse.json({error:"Server configuration error: Master API token not configured."},{status:500});try{let r=await e.json(),t=!0===r.stream,s={...r,_internal_user_id:o.id,_internal_user_email:o.email},n=new URL("/api/v1/chat/completions",e.url).toString(),a=await fetch(n,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${u}`},body:JSON.stringify(s),cache:"no-store"});if(!a.ok){let e=await a.json().catch(()=>({error:{message:a.statusText}}));return i.NextResponse.json({error:`Error from backend completions API: ${e?.error?.message||a.statusText}`},{status:a.status})}if(t&&a.body){let e=new Headers(a.headers);return new Response(a.body,{status:a.status,statusText:a.statusText,headers:e})}{let e=await a.json();return i.NextResponse.json(e,{status:a.status})}}catch(r){let e="An unexpected error occurred in the playground proxy.";return r instanceof SyntaxError?e="Invalid JSON in request body.":r.message&&(e=r.message),i.NextResponse.json({error:e,details:r.toString()},{status:500})}}async function c(e){return i.NextResponse.json({},{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type"}})}let d=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/playground/route",pathname:"/api/playground",filename:"route",bundlePath:"app/api/playground/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\playground\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:g}=d;function y(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,580],()=>t(75948));module.exports=s})();