(()=>{var e={};e.id=7545,e.ids=[7545],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},18536:(e,t,s)=>{Promise.resolve().then(s.bind(s,35291))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25333:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=s(65239),n=s(48088),a=s(88170),i=s.n(a),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["routing-setup",{children:["[configId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,26697)),"C:\\RoKey App\\rokey-app\\src\\app\\routing-setup\\[configId]\\page.tsx"]}]},{}]},{loading:[()=>Promise.resolve().then(s.bind(s,71238)),"C:\\RoKey App\\rokey-app\\src\\app\\routing-setup\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\routing-setup\\[configId]\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/routing-setup/[configId]/page",pathname:"/routing-setup/[configId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},26697:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\routing-setup\\[configId]\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32216:(e,t,s)=>{Promise.resolve().then(s.bind(s,26697))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44725:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(43210);let n=r.forwardRef(function({title:e,titleId:t,...s},n){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))})},47417:(e,t,s)=>{"use strict";s.d(t,{AnalyticsSkeleton:()=>l,ConfigSelectorSkeleton:()=>a,MessageSkeleton:()=>n,MyModelsSkeleton:()=>i,RoutingSetupSkeleton:()=>o});var r=s(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call LoadingSkeleton() from the server but LoadingSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","LoadingSkeleton"),(0,r.registerClientReference)(function(){throw Error("Attempted to call ChatHistorySkeleton() from the server but ChatHistorySkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","ChatHistorySkeleton"),(0,r.registerClientReference)(function(){throw Error("Attempted to call EnhancedChatHistorySkeleton() from the server but EnhancedChatHistorySkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","EnhancedChatHistorySkeleton");let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call MessageSkeleton() from the server but MessageSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","MessageSkeleton"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call ConfigSelectorSkeleton() from the server but ConfigSelectorSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","ConfigSelectorSkeleton");(0,r.registerClientReference)(function(){throw Error("Attempted to call DashboardSkeleton() from the server but DashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","DashboardSkeleton");let i=(0,r.registerClientReference)(function(){throw Error("Attempted to call MyModelsSkeleton() from the server but MyModelsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","MyModelsSkeleton"),o=(0,r.registerClientReference)(function(){throw Error("Attempted to call RoutingSetupSkeleton() from the server but RoutingSetupSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","RoutingSetupSkeleton");(0,r.registerClientReference)(function(){throw Error("Attempted to call TrainingSkeleton() from the server but TrainingSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","TrainingSkeleton");let l=(0,r.registerClientReference)(function(){throw Error("Attempted to call AnalyticsSkeleton() from the server but AnalyticsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","AnalyticsSkeleton");(0,r.registerClientReference)(function(){throw Error("Attempted to call PlaygroundSkeleton() from the server but PlaygroundSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","PlaygroundSkeleton"),(0,r.registerClientReference)(function(){throw Error("Attempted to call LogsSkeleton() from the server but LogsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","LogsSkeleton"),(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LoadingSkeleton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","default")},52074:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var r=s(60687),n=s(43210),a=s(16189),i=s(85814),o=s.n(i),l=s(51426),d=s(62392),c=s(74461);let m=n.forwardRef(function({title:e,titleId:t,...s},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"}))});var u=s(70149),p=s(55296),x=s(58089),h=s(44725),g=s(60925);function f(){return(0,r.jsx)("div",{className:"min-h-screen bg-cream animate-fade-in",children:(0,r.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"})}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-64 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-48 animate-pulse"})]})]})}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded-lg animate-pulse"})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-32 mb-6 animate-pulse"}),(0,r.jsx)("div",{className:"space-y-4",children:[1,2,3,4].map(e=>(0,r.jsx)("div",{className:"border border-gray-200 rounded-xl p-4 animate-pulse",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-lg animate-pulse"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"h-5 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-full animate-pulse"})]})]})},e))})]})}),(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsxs)("div",{className:"card p-8 min-h-[600px]",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("div",{className:"h-7 bg-gray-200 rounded w-48 mb-3 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-full animate-pulse"})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded animate-pulse"})]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,r.jsx)("div",{className:"h-5 bg-gray-200 rounded w-40 mb-4 animate-pulse"}),(0,r.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-32 animate-pulse"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-16 animate-pulse"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-12 animate-pulse"})]})]},e))})]}),(0,r.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,r.jsx)("div",{className:"h-5 bg-gray-200 rounded w-48 mb-4 animate-pulse"}),(0,r.jsx)("div",{className:"grid grid-cols-5 gap-3 mb-4",children:[1,2,3,4,5].map(e=>(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 mx-auto mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse"})]},e))}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 pt-6",children:(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-40 animate-pulse"})})]})]})})]})]})})}function b(){return(0,r.jsx)("div",{className:"min-h-screen bg-cream animate-fade-in",children:(0,r.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-48 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 animate-pulse"})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8",children:[1,2,3,4].map(e=>(0,r.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-16 animate-pulse"})]})]})},e))}),(0,r.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-40 mb-4 animate-pulse"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-20 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})]})]})]})})}let y=[{id:"none",name:"Default Behavior",shortDescription:"Automatic load balancing",description:"RouKey will automatically load balance across all keys assigned to this configuration with intra-request retries. No extra setup needed.",icon:l.A},{id:"intelligent_role",name:"Intelligent Role Routing",shortDescription:"AI-powered role classification",description:"RouKey uses an LLM to classify the user's prompt and routes to a key associated with that role. If no match, uses the 'Default General Chat Model'.",icon:d.A},{id:"complexity_round_robin",name:"Complexity-Based Round-Robin",shortDescription:"Route by prompt complexity",description:"RouKey classifies prompt complexity (1-5) and round-robins among active keys assigned to that complexity. Searches proximal levels if no exact match.",icon:c.A},{id:"strict_fallback",name:"Strict Fallback",shortDescription:"Ordered failover sequence",description:"Define an ordered list of API keys. RouKey will try them in sequence until one succeeds.",icon:m}];function v({apiKey:e,index:t,onMoveUp:s,onMoveDown:n}){return(0,r.jsx)("li",{className:"bg-white border border-gray-200 rounded-lg p-4 shadow-sm transition-all duration-200 hover:shadow-md hover:border-gray-300",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-orange-50 border border-orange-200 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-sm font-semibold text-orange-600",children:t+1})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.label}),(0,r.jsxs)("p",{className:"text-xs text-gray-600",children:[e.provider," - ",e.predefined_model_id]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[s&&(0,r.jsx)("button",{onClick:s,className:"p-1 text-gray-400 hover:text-orange-600 hover:bg-orange-50 rounded transition-all duration-200",title:"Move up",children:(0,r.jsx)(u.A,{className:"w-4 h-4"})}),n&&(0,r.jsx)("button",{onClick:n,className:"p-1 text-gray-400 hover:text-orange-600 hover:bg-orange-50 rounded transition-all duration-200",title:"Move down",children:(0,r.jsx)(p.A,{className:"w-4 h-4"})})]})]})})}function j(){let e=(0,a.useParams)(),t=(0,a.useRouter)(),s=(0,a.useSearchParams)(),i=e.configId,{getCachedData:u,isCached:p}=(0,g.c)(),j=()=>{let e=s.get("from");return"routing-setup"===e?"/routing-setup":`/my-models/${i}`},[k,N]=(0,n.useState)(null),[w,C]=(0,n.useState)(!0),[S,A]=(0,n.useState)(null),[_,R]=(0,n.useState)(null),[L,E]=(0,n.useState)(!1),[M,P]=(0,n.useState)("none"),[I,K]=(0,n.useState)({}),[B,q]=(0,n.useState)([]),[D,F]=(0,n.useState)(!1),[H,$]=(0,n.useState)([]),[O,W]=(0,n.useState)(null),[T,z]=(0,n.useState)({}),[G,Z]=(0,n.useState)([]),[V,U]=(0,n.useState)(!1),[J,X]=(0,n.useState)(!1),[Q,Y]=(0,n.useState)(null),[ee,et]=(0,n.useState)(null);(0,n.useCallback)(async()=>{if(!i){A("Configuration ID is missing."),C(!1);return}let e=u(i);if(e&&e.configDetails&&e.apiKeys){N(e.configDetails),q(e.apiKeys);let t=e.routingStrategy||"none";if(P(t),K(e.routingParams||{}),"strict_fallback"===t&&e.routingParams?.ordered_api_key_ids){let t=e.routingParams.ordered_api_key_ids;$([...t.map(t=>e.apiKeys.find(e=>e.id===t)).filter(Boolean),...e.apiKeys.filter(e=>!t.includes(e.id))])}else $([...e.apiKeys]);C(!1),F(!1);return}p(i)||E(!0),C(!0),F(!0),A(null),R(null);try{let e=await fetch(`/api/custom-configs/${i}`);if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to fetch configuration")}let t=await e.json();N(t);let s=t.routing_strategy||"none";P(s);let r=t.routing_strategy_params||{};K(r);let n=await fetch(`/api/keys?custom_config_id=${i}`);if(!n.ok){let e=await n.json();throw Error(e.error||"Failed to fetch API keys for this configuration")}let a=await n.json();if(q(a),"strict_fallback"===s&&r.ordered_api_key_ids){let e=r.ordered_api_key_ids,t=e.map(e=>a.find(t=>t.id===e)).filter(Boolean),s=a.filter(t=>!e.includes(t.id));$([...t,...s])}else $([...a])}catch(e){A(`Error loading data: ${e.message}`),N(null),q([])}finally{C(!1),F(!1),E(!1)}},[i,u,p]),(0,n.useCallback)(async e=>{if(i&&e){U(!0),Y(null),et(null);try{let t=await fetch(`/api/custom-configs/${i}/keys/${e}/complexity-assignments`);if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to fetch complexity assignments")}let s=await t.json();z(t=>({...t,[e]:s})),Z(s)}catch(e){Y(`Error fetching assignments for key: ${e.message}`),Z([])}finally{U(!1)}}},[i]);let es=(e,t)=>{Z(s=>t?[...s,e].sort((e,t)=>e-t):s.filter(t=>t!==e))},er=(0,n.useCallback)(async()=>{if(!i||!O)return void Y("No API key selected to save assignments for.");X(!0),Y(null),et(null);try{let e=await fetch(`/api/custom-configs/${i}/keys/${O}/complexity-assignments`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({complexity_levels:G})});if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to save complexity assignments")}let t=await e.json();z(e=>({...e,[O]:[...G]})),et(t.message||"Complexity assignments saved successfully!")}catch(e){Y(`Error saving assignments: ${e.message}`)}finally{X(!1)}},[i,O,G]),en=(e,t)=>{let s=[...H],r=s[e];"up"===t&&e>0?(s.splice(e,1),s.splice(e-1,0,r)):"down"===t&&e<s.length-1&&(s.splice(e,1),s.splice(e+1,0,r)),$(s),K({ordered_api_key_ids:s.map(e=>e.id)})},ea=async e=>{if(e.preventDefault(),!i||!k)return void A("Configuration details not loaded.");C(!0),A(null),R(null);let t=I;"strict_fallback"===M&&(t={ordered_api_key_ids:H.map(e=>e.id)});try{let e=await fetch(`/api/custom-configs/${i}/routing`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({routing_strategy:M,routing_strategy_params:t})});if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to save routing settings")}let s=await e.json();R(s.message||"Routing settings saved successfully!"),N(e=>e?{...e,routing_strategy:M,routing_strategy_params:t}:null),K(t)}catch(e){A(`Error saving settings: ${e.message}`)}finally{C(!1)}},ei=()=>"complexity_round_robin"!==M?null:(0,r.jsxs)("div",{className:"mt-8 pt-6 border-t border-gray-200",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Complexity-Based Key Assignments"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-6",children:"Assign API keys to handle prompts of specific complexity levels (1-5). The system will classify incoming prompts and round-robin requests among keys assigned to that complexity."}),D&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 ml-2",children:"Loading API keys..."})]}),!D&&0===B.length&&(0,r.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,r.jsx)("p",{className:"text-sm text-yellow-800",children:"No API keys found for this configuration. Please add API keys first on the model configuration page."})}),B.length>0&&(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{htmlFor:"apiKeyForComplexity",className:"block text-sm font-medium text-gray-700 mb-2",children:"Select API Key to Assign Complexities:"}),(0,r.jsxs)("select",{id:"apiKeyForComplexity",value:O||"",onChange:e=>W(e.target.value||null),className:"form-select max-w-md",children:[(0,r.jsx)("option",{value:"",disabled:!0,children:"-- Select an API Key --"}),B.map(e=>(0,r.jsxs)("option",{value:e.id,children:[e.label," (",e.provider," - ",e.predefined_model_id,")"]},e.id))]})]}),O&&(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h4",{className:"text-md font-medium text-gray-900 mb-4",children:["Assign Complexity Levels for: ",(0,r.jsx)("span",{className:"text-orange-600",children:B.find(e=>e.id===O)?.label})]}),V&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 ml-2",children:"Loading current assignments..."})]}),Q&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:Q})}),ee&&(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3 mb-4",children:(0,r.jsx)("p",{className:"text-green-800 text-sm",children:ee})}),!V&&(0,r.jsx)("div",{className:"space-y-3 mb-6",children:[1,2,3,4,5].map(e=>(0,r.jsxs)("label",{className:"flex items-center space-x-3 p-3 bg-gray-50 border border-gray-200 rounded-lg hover:border-gray-300 cursor-pointer transition-colors duration-200",children:[(0,r.jsx)("input",{type:"checkbox",checked:G.includes(e),onChange:t=>es(e,t.target.checked),className:"h-4 w-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500 focus:ring-2"}),(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:["Complexity Level ",e]})]},e))}),(0,r.jsx)("button",{onClick:er,disabled:J||V,className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:J?"Saving Assignments...":"Save Assignments for this Key"})]})]});return L&&!p(i)?(0,r.jsx)(f,{}):w&&!k?(0,r.jsx)(b,{}):!S||k||w?(0,r.jsx)("div",{className:"min-h-screen bg-cream",children:(0,r.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)("button",{onClick:()=>{t.push(j())},className:"btn-secondary inline-flex items-center text-sm",children:[(0,r.jsx)(h.A,{className:"w-4 h-4 mr-2"}),(()=>{let e=s.get("from");return"routing-setup"===e?"Back to Routing Setup":"Back to Configuration"})()]})}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("h1",{className:"text-h1 text-gray-900",children:"Advanced Routing Setup"}),k&&(0,r.jsxs)("p",{className:"text-body-sm text-gray-600 mt-1",children:["Configuration: ",(0,r.jsx)("span",{className:"text-orange-600 font-semibold",children:k.name})]})]})]})}),S&&!_&&(0,r.jsx)("div",{className:"card border-red-200 bg-red-50 p-6 mb-8 max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-red-800",children:"Configuration Error"}),(0,r.jsx)("p",{className:"text-red-700 mt-1",children:S})]})]})}),_&&(0,r.jsx)("div",{className:"card border-green-200 bg-green-50 p-6 mb-8 max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-green-800",children:"Settings Saved"}),(0,r.jsx)("p",{className:"text-green-700 mt-1",children:_})]})]})}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"card p-6 sticky top-8",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:"Routing Strategy"}),(0,r.jsx)("div",{className:"space-y-3",children:y.map(e=>{let t=e.icon,s=M===e.id;return(0,r.jsx)("button",{onClick:()=>{if(P(e.id),"strict_fallback"===e.id){let e=I.ordered_api_key_ids;e&&Array.isArray(e)?$([...e.map(e=>B.find(t=>t.id===e)).filter(Boolean),...B.filter(t=>!e.includes(t.id))]):$([...B]),K({ordered_api_key_ids:H.map(e=>e.id)})}else K({}),$([...B]);W(null),Z([]),Y(null),et(null)},disabled:w,className:`w-full text-left p-4 rounded-xl border-2 transition-all duration-300 group ${s?"border-orange-500 bg-orange-50 shadow-lg transform scale-[1.02]":"border-gray-200 bg-white hover:border-orange-300 hover:bg-orange-25 hover:shadow-md"}`,children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:`p-3 rounded-lg transition-colors duration-300 ${s?"bg-orange-100 text-orange-600":"bg-gray-100 text-gray-600 group-hover:bg-orange-100 group-hover:text-orange-600"}`,children:(0,r.jsx)(t,{className:"w-6 h-6"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,r.jsx)("h3",{className:`font-semibold text-sm transition-colors duration-300 ${s?"text-orange-900":"text-gray-900"}`,children:e.name}),s&&(0,r.jsx)(x.A,{className:"w-4 h-4 text-orange-600 animate-in fade-in duration-300"})]}),(0,r.jsx)("p",{className:`text-xs leading-relaxed transition-colors duration-300 ${s?"text-orange-700":"text-gray-600"}`,children:e.shortDescription})]})]})},e.id)})})]})}),(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsx)("form",{onSubmit:ea,children:(0,r.jsx)("div",{className:"card p-8 min-h-[600px]",children:(0,r.jsx)("div",{className:"animate-in fade-in slide-in-from-right-4 duration-500",children:(()=>{let e=y.find(e=>e.id===M);return"none"===M?(0,r.jsxs)("div",{className:"text-center py-16",children:[(0,r.jsx)("div",{className:"w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,r.jsx)(l.A,{className:"w-10 h-10 text-orange-600"})}),(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Default Behavior"}),(0,r.jsx)("p",{className:"text-gray-600 max-w-md mx-auto leading-relaxed",children:e?.description}),(0,r.jsx)("div",{className:"mt-8 p-4 bg-green-50 border border-green-200 rounded-xl",children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,r.jsx)(x.A,{className:"w-5 h-5 text-green-600"}),(0,r.jsx)("span",{className:"text-green-800 font-medium",children:"No additional setup required"})]})}),(0,r.jsx)("div",{className:"mt-12 pt-6 border-t border-gray-200",children:(0,r.jsx)("button",{type:"submit",className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",disabled:w,children:w?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Save Routing Settings"]})})})]}):"intelligent_role"===M?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h3",{className:"text-2xl font-bold text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(d.A,{className:"w-7 h-7 mr-3 text-orange-600"}),"Intelligent Role Routing"]}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e?.description})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-xl p-6",children:[(0,r.jsx)("h4",{className:"font-semibold text-blue-900 mb-3",children:"How it works:"}),(0,r.jsxs)("div",{className:"space-y-3 text-sm text-blue-800",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs font-bold text-blue-600",children:"1"})}),(0,r.jsx)("p",{children:"System analyzes your prompt to understand the main task"})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs font-bold text-blue-600",children:"2"})}),(0,r.jsx)("p",{children:"Matches task to relevant RoKey operational roles (e.g., 'Coding - Frontend', 'Copywriting')"})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs font-bold text-blue-600",children:"3"})}),(0,r.jsx)("p",{children:"Routes to assigned API key or falls back to 'Default General Chat Model'"})]})]})]}),(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-xl p-6",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(x.A,{className:"w-6 h-6 text-green-600 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-green-900 mb-2",children:"Ready to use!"}),(0,r.jsx)("p",{className:"text-sm text-green-800 leading-relaxed",children:"No additional setup required. Future enhancements may allow further customization."})]})]})})]}),(0,r.jsx)("div",{className:"mt-12 pt-6 border-t border-gray-200 flex justify-end",children:(0,r.jsx)("button",{type:"submit",className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",disabled:w,children:w?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Save Routing Settings"]})})})]}):"strict_fallback"===M?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h3",{className:"text-2xl font-bold text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(m,{className:"w-7 h-7 mr-3 text-orange-600"}),"Strict Fallback Configuration"]}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e?.description})]}),D&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,r.jsx)("div",{className:"w-8 h-8 border-2 border-orange-600/20 border-t-orange-600 rounded-full animate-spin"}),(0,r.jsx)("p",{className:"text-gray-600 ml-3",children:"Loading API keys..."})]}),!D&&0===B.length&&(0,r.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-xl p-8 text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("svg",{className:"w-8 h-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,r.jsx)("h4",{className:"text-lg font-semibold text-yellow-900 mb-2",children:"No API Keys Found"}),(0,r.jsx)("p",{className:"text-yellow-800 leading-relaxed",children:"Please add API keys on the main configuration page to set up fallback order."})]}),!D&&B.length>0&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-xl p-6",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsx)("p",{className:"text-sm text-blue-800 leading-relaxed",children:"Drag and drop to arrange the API keys in the desired order of execution. The router will try the first key, then the second if the first fails, and so on."})]})}),(0,r.jsx)("ul",{className:"space-y-3",children:H.map((e,t)=>(0,r.jsx)(v,{apiKey:e,index:t,onMoveUp:t>0?()=>en(t,"up"):void 0,onMoveDown:t<H.length-1?()=>en(t,"down"):void 0},e.id))})]}),(0,r.jsx)("div",{className:"mt-12 pt-6 border-t border-gray-200 flex justify-end",children:(0,r.jsx)("button",{type:"submit",className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",disabled:w||0===B.length,children:w?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Save Routing Settings"]})})})]}):"complexity_round_robin"===M?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h3",{className:"text-2xl font-bold text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(c.A,{className:"w-7 h-7 mr-3 text-orange-600"}),"Complexity-Based Round-Robin"]}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e?.description})]}),ei(),(0,r.jsx)("div",{className:"mt-12 pt-6 border-t border-gray-200 flex justify-end",children:(0,r.jsx)("button",{type:"submit",className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",disabled:w,children:w?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Save Routing Settings"]})})})]}):null})()})})})})]})})]})}):(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Routing Setup Error"}),(0,r.jsxs)("div",{className:"card border-red-200 bg-red-50 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,r.jsx)("p",{className:"text-red-800",children:S})]}),(0,r.jsx)(o(),{href:"/my-models",className:"mt-4 btn-primary inline-block",children:"Back to My Models"})]})]})}},54984:(e,t,s)=>{Promise.resolve().then(s.bind(s,47417))},55296:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(43210);let n=r.forwardRef(function({title:e,titleId:t,...s},n){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3"}))})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58089:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(43210);let n=r.forwardRef(function({title:e,titleId:t,...s},n){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},60925:(e,t,s)=>{"use strict";s.d(t,{c:()=>a});var r=s(43210);let n={};function a(){let[e,t]=(0,r.useState)({}),s=(0,r.useRef)({}),a=(0,r.useCallback)(e=>{let t=n[e];return!!t&&!(Date.now()-t.timestamp>3e5)&&!t.isLoading},[]),i=(0,r.useCallback)(e=>{let t=n[e];return!t||t.isLoading?null:Date.now()-t.timestamp>3e5?(delete n[e],null):t.data},[]),o=(0,r.useCallback)(async(e,r="medium")=>{if(a(e))return i(e);if(n[e]?.isLoading)return null;s.current[e]&&s.current[e].abort();let o=new AbortController;s.current[e]=o,n[e]={data:{},timestamp:Date.now(),isLoading:!0},t(t=>({...t,[e]:"loading"}));try{"low"===r?await new Promise(e=>setTimeout(e,200)):"medium"===r&&await new Promise(e=>setTimeout(e,50));let[s,a,i]=await Promise.allSettled([fetch(`/api/custom-configs/${e}`,{signal:o.signal}),fetch(`/api/keys?custom_config_id=${e}`,{signal:o.signal}),fetch(`/api/complexity-assignments?custom_config_id=${e}`,{signal:o.signal})]),l=null,d=[],c="none",m={},u=[];"fulfilled"===s.status&&s.value.ok&&(c=(l=await s.value.json()).routing_strategy||"none",m=l.routing_strategy_params||{}),"fulfilled"===a.status&&a.value.ok&&(d=await a.value.json()),"fulfilled"===i.status&&i.value.ok&&(u=await i.value.json());let p={configDetails:l,apiKeys:d,routingStrategy:c,routingParams:m,complexityAssignments:u};return n[e]={data:p,timestamp:Date.now(),isLoading:!1},t(t=>({...t,[e]:"success"})),p}catch(s){if("AbortError"===s.name)return null;return delete n[e],t(t=>({...t,[e]:"error"})),null}finally{delete s.current[e]}},[a,i]),l=(0,r.useCallback)(e=>({onMouseEnter:()=>{a(e)||o(e,"high")}}),[o,a]),d=(0,r.useCallback)(e=>{delete n[e],t(t=>{let s={...t};return delete s[e],s})},[]),c=(0,r.useCallback)(()=>{Object.keys(n).forEach(e=>{delete n[e]}),t({})},[]);return{prefetchRoutingSetupData:o,getCachedData:i,isCached:a,createHoverPrefetch:l,clearCache:d,clearAllCache:c,getStatus:(0,r.useCallback)(t=>e[t]||"idle",[e]),getCacheInfo:(0,r.useCallback)(()=>({cachedConfigs:Object.keys(n),cacheSize:Object.keys(n).length,totalCacheAge:Object.values(n).reduce((e,t)=>e+(Date.now()-t.timestamp),0)/Object.keys(n).length}),[]),prefetchStatus:e}}},62392:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(43210);let n=r.forwardRef(function({title:e,titleId:t,...s},n){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z"}))})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67064:(e,t,s)=>{Promise.resolve().then(s.bind(s,52074))},70149:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(43210);let n=r.forwardRef(function({title:e,titleId:t,...s},n){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18"}))})},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71238:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(37413),n=s(47417);function a(){return(0,r.jsx)(n.RoutingSetupSkeleton,{})}},74075:e=>{"use strict";e.exports=require("zlib")},74461:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(43210);let n=r.forwardRef(function({title:e,titleId:t,...s},n){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125"}))})},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[7719,221,1658,7437],()=>s(25333));module.exports=r})();