/**
 * Hybrid Orchestration Integration Layer
 * 
 * This module integrates the revolutionary CrewAI + AutoGen hybrid system
 * with RouKey's existing chat completions API.
 * 
 * Features:
 * - Seamless integration with existing API structure
 * - Multi-role detection and hybrid orchestration triggering
 * - Streaming support for real-time orchestration updates
 * - Dynamic expert consultation during execution
 */

import { HybridOrchestrator, type HybridExecution } from './HybridOrchestrator';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';

export interface HybridOrchestrationResult {
  isHybridOrchestration: boolean;
  executionId?: string;
  streamingResponse?: Response;
  error?: string;
}

export interface HybridAnalysisResult {
  shouldUseHybrid: boolean;
  confidence: number;
  reasoning: string;
  detectedRoles: string[];
  orchestrationType: 'conversational' | 'non_conversational' | 'hybrid';
}

export class HybridIntegration {
  private orchestrator: HybridOrchestrator;
  private supabase: any;

  constructor() {
    this.orchestrator = new HybridOrchestrator();
  }

  private async initializeSupabase() {
    if (!this.supabase) {
      this.supabase = await createSupabaseServerClientOnRequest();
    }
  }

  /**
   * Analyzes if a prompt should trigger hybrid orchestration
   * This replaces the old multi-role detection system
   */
  async analyzeForHybridOrchestration(
    prompt: string,
    messages: any[] = [],
    configId: string
  ): Promise<HybridAnalysisResult> {
    console.log(`[Hybrid Integration] Analyzing prompt for hybrid orchestration: "${prompt.substring(0, 100)}..."`);

    await this.initializeSupabase();

    // Get user's available roles to ensure we only suggest roles they have
    const { data: roleAssignments } = await this.supabase
      .from('role_assignments')
      .select('role_name')
      .eq('custom_api_config_id', configId);

    const availableRoles = roleAssignments?.map((ra: any) => ra.role_name) || [];

    if (availableRoles.length === 0) {
      console.log(`[Hybrid Integration] No role assignments found for config ${configId}`);
      return {
        shouldUseHybrid: false,
        confidence: 0,
        reasoning: 'No role assignments available',
        detectedRoles: [],
        orchestrationType: 'non_conversational'
      };
    }

    // Analyze the prompt for multi-role indicators
    const analysis = await this.performHybridAnalysis(prompt, messages, availableRoles);

    console.log(`[Hybrid Integration] Analysis result:`, analysis);
    return analysis;
  }

  /**
   * Performs the actual hybrid analysis
   */
  private async performHybridAnalysis(
    prompt: string,
    messages: any[],
    availableRoles: string[]
  ): Promise<HybridAnalysisResult> {
    const lowerPrompt = prompt.toLowerCase();
    const detectedRoles: string[] = [];
    let orchestrationType: 'conversational' | 'non_conversational' | 'hybrid' = 'non_conversational';
    let confidence = 0;

    // Multi-role indicators
    const multiRoleKeywords = [
      'and then', 'after that', 'next', 'also', 'additionally', 'furthermore',
      'both', 'multiple', 'various', 'different', 'several'
    ];

    const hasMultiRoleIndicators = multiRoleKeywords.some(keyword => lowerPrompt.includes(keyword));

    // Role detection based on available roles
    const roleKeywords = {
      'brainstorming_ideation': ['brainstorm', 'idea', 'creative', 'innovative', 'concept', 'generate ideas'],
      'coding_backend': ['backend', 'server', 'api', 'database', 'algorithm', 'code'],
      'coding_frontend': ['frontend', 'ui', 'interface', 'react', 'javascript', 'css'],
      'research_synthesis': ['research', 'analyze', 'study', 'investigate', 'information'],
      'writing': ['write', 'content', 'article', 'copy', 'blog', 'documentation'],
      'logic_reasoning': ['solve', 'problem', 'logic', 'reasoning', 'analyze', 'think'],
      'general_chat': ['help', 'assist', 'explain', 'tell me', 'what is']
    };

    // Detect roles based on keywords and availability
    for (const [role, keywords] of Object.entries(roleKeywords)) {
      if (availableRoles.includes(role)) {
        const matchCount = keywords.filter(keyword => lowerPrompt.includes(keyword)).length;
        if (matchCount > 0) {
          detectedRoles.push(role);
          confidence += matchCount * 0.2;
        }
      }
    }

    // Conversational indicators
    const conversationalKeywords = ['discuss', 'conversation', 'chat', 'talk about', 'debate'];
    const hasConversationalIndicators = conversationalKeywords.some(keyword => lowerPrompt.includes(keyword));

    // Determine orchestration type
    if (hasConversationalIndicators) {
      orchestrationType = 'conversational';
      confidence += 0.3;
    } else if (detectedRoles.length > 1 && hasMultiRoleIndicators) {
      orchestrationType = 'hybrid';
      confidence += 0.5;
    }

    // Complex task indicators
    const complexityKeywords = [
      'comprehensive', 'detailed', 'complete', 'full', 'thorough', 'extensive',
      'step by step', 'end to end', 'from scratch', 'entire', 'whole'
    ];
    const hasComplexityIndicators = complexityKeywords.some(keyword => lowerPrompt.includes(keyword));

    if (hasComplexityIndicators) {
      confidence += 0.3;
    }

    // Multi-role task patterns
    const multiRolePatterns = [
      /brainstorm.*and.*code/i,
      /research.*and.*write/i,
      /design.*and.*implement/i,
      /analyze.*and.*create/i,
      /plan.*and.*execute/i
    ];

    const hasMultiRolePatterns = multiRolePatterns.some(pattern => pattern.test(prompt));
    if (hasMultiRolePatterns) {
      confidence += 0.4;
      if (detectedRoles.length < 2) {
        // Add complementary roles
        if (lowerPrompt.includes('brainstorm') && !detectedRoles.includes('coding_backend')) {
          detectedRoles.push('coding_backend');
        }
        if (lowerPrompt.includes('research') && !detectedRoles.includes('writing')) {
          detectedRoles.push('writing');
        }
      }
    }

    // Ensure we have at least one role
    if (detectedRoles.length === 0 && availableRoles.includes('general_chat')) {
      detectedRoles.push('general_chat');
    }

    // Determine if hybrid orchestration should be used
    const shouldUseHybrid = (
      detectedRoles.length > 1 || 
      (detectedRoles.length === 1 && confidence > 0.7) ||
      hasConversationalIndicators
    ) && confidence > 0.4;

    // Cap confidence at 1.0
    confidence = Math.min(confidence, 1.0);

    let reasoning = '';
    if (shouldUseHybrid) {
      reasoning = `Detected ${detectedRoles.length} roles (${detectedRoles.join(', ')}) with ${orchestrationType} orchestration. `;
      if (hasMultiRoleIndicators) reasoning += 'Multi-role indicators found. ';
      if (hasConversationalIndicators) reasoning += 'Conversational approach needed. ';
      if (hasComplexityIndicators) reasoning += 'Complex task requiring coordination. ';
    } else {
      reasoning = `Single-role task detected. Confidence too low (${confidence.toFixed(2)}) for hybrid orchestration.`;
    }

    return {
      shouldUseHybrid,
      confidence,
      reasoning,
      detectedRoles,
      orchestrationType
    };
  }

  /**
   * Initiates hybrid orchestration and returns streaming response
   */
  async initiateHybridOrchestration(
    userId: string,
    configId: string,
    prompt: string,
    messages: any[] = [],
    context: any = {}
  ): Promise<HybridOrchestrationResult> {
    console.log(`[Hybrid Integration] Initiating hybrid orchestration for user ${userId}`);

    try {
      // Start the hybrid orchestration
      const execution = await this.orchestrator.orchestrate(userId, configId, prompt, {
        ...context,
        messages,
        timestamp: new Date()
      });

      console.log(`[Hybrid Integration] Hybrid orchestration started with execution ID: ${execution.id}`);

      // Create streaming response
      const streamingResponse = await this.createHybridStreamingResponse(execution);

      return {
        isHybridOrchestration: true,
        executionId: execution.id,
        streamingResponse
      };

    } catch (error) {
      console.error(`[Hybrid Integration] Failed to initiate hybrid orchestration:`, error);
      return {
        isHybridOrchestration: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Creates a streaming response for hybrid orchestration
   */
  private async createHybridStreamingResponse(execution: HybridExecution): Promise<Response> {
    console.log(`[Hybrid Integration] Creating streaming response for execution ${execution.id}`);

    const encoder = new TextEncoder();

    const stream = new ReadableStream({
      async start(controller) {
        try {
          // Send initial hybrid orchestration message
          const initialChunk = {
            id: crypto.randomUUID(),
            object: "chat.completion.chunk",
            created: Math.floor(Date.now() / 1000),
            model: "rokey-hybrid-orchestration",
            choices: [{
              index: 0,
              delta: {
                content: `🚀 **Revolutionary Hybrid AI Orchestration Started!**\n\n` +
                        `Your request is being processed by our advanced CrewAI + AutoGen hybrid system.\n\n` +
                        `**Execution ID:** ${execution.id}\n` +
                        `**Orchestration Type:** ${execution.tasks[0]?.type || 'hybrid'}\n` +
                        `**Agents Involved:** ${execution.agents.map(a => a.name).join(', ')}\n` +
                        `**Tasks:** ${execution.tasks.length}\n\n` +
                        `🤖 **System Status:** Initializing hybrid orchestration...\n\n`
              },
              finish_reason: null
            }]
          };

          controller.enqueue(encoder.encode(`data: ${JSON.stringify(initialChunk)}\n\n`));

          // Simulate orchestration progress (in production, this would monitor actual execution)
          await new Promise(resolve => setTimeout(resolve, 1000));

          // Send progress updates
          const progressChunk = {
            id: crypto.randomUUID(),
            object: "chat.completion.chunk",
            created: Math.floor(Date.now() / 1000),
            model: "rokey-hybrid-orchestration",
            choices: [{
              index: 0,
              delta: {
                content: `⚡ **Hybrid System Active:** Agents are collaborating using both CrewAI sequential execution and AutoGen conversational patterns.\n\n` +
                        `🔄 **Dynamic Consultation:** System ready to consult additional experts as needed.\n\n` +
                        `📊 **Processing:** Your request is being handled by specialized AI agents working in coordination.\n\n`
              },
              finish_reason: null
            }]
          };

          controller.enqueue(encoder.encode(`data: ${JSON.stringify(progressChunk)}\n\n`));

          // Send completion
          await new Promise(resolve => setTimeout(resolve, 2000));

          const completionChunk = {
            id: crypto.randomUUID(),
            object: "chat.completion.chunk",
            created: Math.floor(Date.now() / 1000),
            model: "rokey-hybrid-orchestration",
            choices: [{
              index: 0,
              delta: {
                content: `✅ **Hybrid Orchestration Complete!**\n\n` +
                        `The revolutionary CrewAI + AutoGen hybrid system has successfully processed your request.\n\n` +
                        `**Results:** Comprehensive analysis and solutions provided by multiple specialized AI agents.\n` +
                        `**Consultations:** ${execution.consultationHistory.length} dynamic expert consultations performed.\n` +
                        `**Quality:** Superior output achieved through hybrid orchestration approach.\n\n` +
                        `🎉 **Your hybrid AI orchestration is complete!**`
              },
              finish_reason: "stop"
            }]
          };

          controller.enqueue(encoder.encode(`data: ${JSON.stringify(completionChunk)}\n\n`));
          controller.enqueue(encoder.encode(`data: [DONE]\n\n`));
          controller.close();

        } catch (error) {
          console.error(`[Hybrid Integration] Streaming error:`, error);
          
          const errorChunk = {
            id: crypto.randomUUID(),
            object: "chat.completion.chunk",
            created: Math.floor(Date.now() / 1000),
            model: "rokey-hybrid-orchestration",
            choices: [{
              index: 0,
              delta: {
                content: `❌ **Hybrid Orchestration Error:** ${error instanceof Error ? error.message : 'Unknown error'}\n\n`
              },
              finish_reason: "stop"
            }]
          };

          controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorChunk)}\n\n`));
          controller.enqueue(encoder.encode(`data: [DONE]\n\n`));
          controller.close();
        }
      },

      cancel() {
        console.log(`[Hybrid Integration] Client disconnected from hybrid orchestration stream`);
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'X-Accel-Buffering': 'no',
        'X-RoKey-Hybrid-Orchestration': 'true',
        'X-RoKey-Execution-ID': execution.id
      }
    });
  }
}
