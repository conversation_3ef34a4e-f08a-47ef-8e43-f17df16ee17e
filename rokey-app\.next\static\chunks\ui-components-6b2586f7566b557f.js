"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[274],{8413:(e,s,a)=>{a.d(s,{A:()=>n});var r=a(95155),t=a(12115),i=a(57514);let l=new Map;function n(e){let{configId:s,onRetry:a,className:n="",disabled:d=!1}=e,[o,c]=(0,t.useState)(!1),[m,x]=(0,t.useState)([]),[u,g]=(0,t.useState)(!1),[h,p]=(0,t.useState)(!1),b=(0,t.useRef)(null),f=(0,t.useCallback)(async function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(s){if(e){let e=l.get(s);if(e&&Date.now()-e.timestamp<3e5){x(e.keys),p(!0);return}}g(!0);try{let e=await fetch("/api/keys?custom_config_id=".concat(s));if(e.ok){let a=(await e.json()).filter(e=>"active"===e.status);l.set(s,{keys:a,timestamp:Date.now()}),x(a),p(!0)}}catch(e){}finally{g(!1)}}},[s]);(0,t.useEffect)(()=>{s&&!h&&f(!0)},[s,f,h]),(0,t.useEffect)(()=>{let e=e=>{b.current&&!b.current.contains(e.target)&&c(!1)};return o&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[o]);let j=e=>{c(!1),a(e)};return(0,r.jsxs)("div",{className:"relative ".concat(n),ref:b,children:[(0,r.jsxs)("button",{onClick:()=>{o||0!==m.length||h||f(!0),c(!o)},disabled:d,className:"\n          flex items-center space-x-1 p-1.5 rounded transition-all duration-200 cursor-pointer\n          ".concat(d?"text-gray-300 cursor-not-allowed":"text-gray-500 hover:text-gray-700 hover:bg-white/20","\n        "),title:"Retry with different model",children:[(0,r.jsx)(i.E,{className:"w-4 h-4 stroke-2 ".concat(u?"animate-spin":"")}),(0,r.jsx)(i.D,{className:"w-3 h-3 stroke-2"})]}),o&&(0,r.jsx)("div",{className:"absolute bottom-full left-0 mb-1 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50 animate-scale-in origin-bottom-left",children:(0,r.jsxs)("div",{className:"py-1",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between px-3 py-2 border-b border-gray-100",children:[(0,r.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"Retry Options"}),(0,r.jsx)("button",{onClick:e=>{e.stopPropagation(),f(!1)},disabled:u,className:"p-1 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50",title:"Refresh available models",children:(0,r.jsx)(i.E,{className:"w-3 h-3 ".concat(u?"animate-spin":"")})})]}),(0,r.jsxs)("button",{onClick:()=>j(),className:"w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2",children:[(0,r.jsx)(i.E,{className:"w-4 h-4 text-gray-500"}),(0,r.jsx)("span",{children:"Retry with same model"})]}),(m.length>0||u)&&(0,r.jsx)("div",{className:"border-t border-gray-100 my-1"}),u&&(0,r.jsxs)("div",{className:"px-3 py-2 text-sm text-gray-500 flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"}),(0,r.jsx)("span",{children:"Loading models..."})]}),m.map(e=>(0,r.jsxs)("button",{onClick:()=>j(e.id),className:"w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex flex-col disabled:opacity-50",disabled:u,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,r.jsx)("span",{className:"font-medium",children:e.label}),(0,r.jsx)("span",{className:"text-xs text-gray-500 capitalize",children:e.provider})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-0.5",children:["Temperature: ",e.temperature]})]},e.id)),!u&&0===m.length&&h&&(0,r.jsx)("div",{className:"px-3 py-2 text-sm text-gray-500",children:"No alternative models available"}),m.length>0&&!u&&(0,r.jsxs)("div",{className:"px-3 py-1 text-xs text-gray-400 border-t border-gray-100 flex items-center justify-between",children:[(0,r.jsxs)("span",{children:[m.length," model",1!==m.length?"s":""," available"]}),(()=>{let e=l.get(s);return e&&Date.now()-e.timestamp<3e5?(0,r.jsx)("span",{className:"text-green-500 text-xs",children:"●"}):null})()]})]})})]})}},14446:(e,s,a)=>{a.d(s,{Ay:()=>t,CE:()=>i});var r=a(95155);function t(){return(0,r.jsx)("div",{className:"min-h-screen bg-cream animate-fade-in",children:(0,r.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"})}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-64 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-48 animate-pulse"})]})]})}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded-lg animate-pulse"})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-32 mb-6 animate-pulse"}),(0,r.jsx)("div",{className:"space-y-4",children:[1,2,3,4].map(e=>(0,r.jsx)("div",{className:"border border-gray-200 rounded-xl p-4 animate-pulse",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-lg animate-pulse"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"h-5 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-full animate-pulse"})]})]})},e))})]})}),(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsxs)("div",{className:"card p-8 min-h-[600px]",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("div",{className:"h-7 bg-gray-200 rounded w-48 mb-3 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-full animate-pulse"})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded animate-pulse"})]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,r.jsx)("div",{className:"h-5 bg-gray-200 rounded w-40 mb-4 animate-pulse"}),(0,r.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-32 animate-pulse"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-16 animate-pulse"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-12 animate-pulse"})]})]},e))})]}),(0,r.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,r.jsx)("div",{className:"h-5 bg-gray-200 rounded w-48 mb-4 animate-pulse"}),(0,r.jsx)("div",{className:"grid grid-cols-5 gap-3 mb-4",children:[1,2,3,4,5].map(e=>(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 mx-auto mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse"})]},e))}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 pt-6",children:(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-40 animate-pulse"})})]})]})})]})]})})}function i(){return(0,r.jsx)("div",{className:"min-h-screen bg-cream animate-fade-in",children:(0,r.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-48 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 animate-pulse"})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8",children:[1,2,3,4].map(e=>(0,r.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-16 animate-pulse"})]})]})},e))}),(0,r.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-40 mb-4 animate-pulse"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-20 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})]})]})]})})}a(12115)},38050:(e,s,a)=>{a.d(s,{default:()=>n});var r=a(12115),t=a(35695),i=a(5777),l=a(44042);function n(e){let{enableUserBehaviorTracking:s=!0,enableNavigationTracking:a=!0,enableInteractionTracking:n=!0}=e,d=(0,t.usePathname)(),o=(0,r.useRef)(""),c=(0,r.useRef)(0),{exportMetrics:m}=(0,l.D)("PerformanceTracker");return(0,r.useEffect)(()=>{if(!a)return;let e=o.current;e&&e!==d&&(i.zf.trackNavigation(e,d),performance.now(),c.current),o.current=d,c.current=performance.now()},[d,a]),(0,r.useEffect)(()=>{if(!n)return;let e=e=>{let s=e.target;if("mouseenter"===e.type&&"A"===s.tagName){let e=s.getAttribute("href");e&&e.startsWith("/")&&i.zf.schedulePrefetch(e)}if("click"===e.type&&("BUTTON"===s.tagName||s.closest("button"))){var a;let e=(null==(a=s.textContent)?void 0:a.trim())||"Unknown";e.toLowerCase().includes("get started")||e.toLowerCase().includes("sign up")?i.zf.schedulePrefetch("/auth/signup"):e.toLowerCase().includes("pricing")?i.zf.schedulePrefetch("/pricing"):e.toLowerCase().includes("features")&&i.zf.schedulePrefetch("/features")}};return document.addEventListener("mouseenter",e,!0),document.addEventListener("click",e,!0),()=>{document.removeEventListener("mouseenter",e,!0),document.removeEventListener("click",e,!0)}},[n]),(0,r.useEffect)(()=>{let e;if(!s)return;let a=!1,r=0,t=()=>{a||(a=!0,performance.now());let s=window.scrollY/(document.body.scrollHeight-window.innerHeight)*100;r=Math.max(r,s),clearTimeout(e),e=setTimeout(()=>{a=!1,performance.now(),r>80&&("/"===d?(i.zf.schedulePrefetch("/pricing"),i.zf.schedulePrefetch("/features")):"/features"===d&&i.zf.schedulePrefetch("/auth/signup")),r=0},150)},l=performance.now(),n=()=>{performance.now()-l>1e4&&("/"===d?i.zf.schedulePrefetch("/auth/signup"):"/pricing"===d&&i.zf.schedulePrefetch("/auth/signup"))};window.addEventListener("scroll",t,{passive:!0});let o=()=>{document.hidden&&n()};document.addEventListener("visibilitychange",o);let c=()=>{n()};return window.addEventListener("beforeunload",c),()=>{clearTimeout(e),window.removeEventListener("scroll",t),document.removeEventListener("visibilitychange",o),window.removeEventListener("beforeunload",c),n()}},[d,s,m]),(0,r.useEffect)(()=>{if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{let s=e.getEntries();s[s.length-1].startTime});e.observe({entryTypes:["largest-contentful-paint"]});let s=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{e.processingStart&&e.startTime&&(e.processingStart,e.startTime)})});s.observe({entryTypes:["first-input"]});let a=0,r=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{e.hadRecentInput||(a+=e.value)})});return r.observe({entryTypes:["layout-shift"]}),()=>{e.disconnect(),s.disconnect(),r.disconnect()}}},[]),null}},43456:(e,s,a)=>{a.d(s,{A:()=>o});var r=a(95155),t=a(11518),i=a.n(t),l=a(12115),n=a(10747);let d={initializing:{icon:n.P,text:"Initializing",description:"Starting up systems",bgColor:"bg-gradient-to-r from-slate-50 to-gray-50",iconColor:"text-slate-600",borderColor:"border-slate-200/60",glowColor:"shadow-slate-200/50",gradientFrom:"from-slate-400",gradientTo:"to-gray-400",duration:200},analyzing:{icon:n.$p,text:"Analyzing",description:"Understanding your request",bgColor:"bg-gradient-to-r from-cyan-50 to-blue-50",iconColor:"text-cyan-600",borderColor:"border-cyan-200/60",glowColor:"shadow-cyan-200/50",gradientFrom:"from-cyan-400",gradientTo:"to-blue-400",duration:300},routing:{icon:n.EF,text:"Smart routing",description:"Finding optimal path",bgColor:"bg-gradient-to-r from-indigo-50 to-purple-50",iconColor:"text-indigo-600",borderColor:"border-indigo-200/60",glowColor:"shadow-indigo-200/50",gradientFrom:"from-indigo-400",gradientTo:"to-purple-400",duration:400},complexity_analysis:{icon:n.XL,text:"Analyzing complexity",description:"Evaluating request depth",bgColor:"bg-gradient-to-r from-amber-50 to-yellow-50",iconColor:"text-amber-600",borderColor:"border-amber-200/60",glowColor:"shadow-amber-200/50",gradientFrom:"from-amber-400",gradientTo:"to-yellow-400",duration:500},role_classification:{icon:n.Gg,text:"Assembling specialists",description:"Building expert team",bgColor:"bg-gradient-to-r from-violet-50 to-purple-50",iconColor:"text-violet-600",borderColor:"border-violet-200/60",glowColor:"shadow-violet-200/50",gradientFrom:"from-violet-400",gradientTo:"to-purple-400",duration:600},preparing:{icon:n.DQ,text:"Preparing",description:"Setting up processing",bgColor:"bg-gradient-to-r from-orange-50 to-amber-50",iconColor:"text-orange-600",borderColor:"border-orange-200/60",glowColor:"shadow-orange-200/50",gradientFrom:"from-orange-400",gradientTo:"to-amber-400",duration:300},connecting:{icon:n.nr,text:"Connecting",description:"Establishing AI link",bgColor:"bg-gradient-to-r from-rose-50 to-pink-50",iconColor:"text-rose-600",borderColor:"border-rose-200/60",glowColor:"shadow-rose-200/50",gradientFrom:"from-rose-400",gradientTo:"to-pink-400",duration:400},generating:{icon:n.Y3,text:"Thinking deeply",description:"AI processing in progress",bgColor:"bg-gradient-to-r from-emerald-50 to-teal-50",iconColor:"text-emerald-600",borderColor:"border-emerald-200/60",glowColor:"shadow-emerald-200/50",gradientFrom:"from-emerald-400",gradientTo:"to-teal-400",duration:800},typing:{icon:n.R2,text:"Streaming response",description:"Delivering your answer",bgColor:"bg-gradient-to-r from-green-50 to-emerald-50",iconColor:"text-green-600",borderColor:"border-green-200/60",glowColor:"shadow-green-200/50",gradientFrom:"from-green-400",gradientTo:"to-emerald-400"},finalizing:{icon:n.BZ,text:"Finalizing",description:"Adding finishing touches",bgColor:"bg-gradient-to-r from-teal-50 to-cyan-50",iconColor:"text-teal-600",borderColor:"border-teal-200/60",glowColor:"shadow-teal-200/50",gradientFrom:"from-teal-400",gradientTo:"to-cyan-400",duration:200},complete:{icon:n.Zu,text:"Complete",description:"Response delivered",bgColor:"bg-gradient-to-r from-green-50 to-lime-50",iconColor:"text-green-600",borderColor:"border-green-200/60",glowColor:"shadow-green-200/50",gradientFrom:"from-green-400",gradientTo:"to-lime-400",duration:100}};function o(e){let{currentStage:s,isStreaming:a=!1,className:t="",onStageChange:n,orchestrationStatus:o}=e,[c,m]=(0,l.useState)(s),[x,u]=(0,l.useState)(!1),g=d[c],h=g.icon;return(0,l.useEffect)(()=>{s!==c&&(u(!0),setTimeout(()=>{m(s),u(!1),null==n||n(s)},200))},[s,c,n]),(0,r.jsxs)("div",{className:"jsx-f56d70faa8a01b64 "+"flex justify-start ".concat(t),children:[(0,r.jsxs)("div",{className:"jsx-f56d70faa8a01b64 relative w-6 h-6 rounded-full flex items-center justify-center mr-2.5 mt-0.5 flex-shrink-0",children:[(0,r.jsx)("div",{style:{animation:x?"spin 0.6s linear infinite":"spin 1.2s linear infinite",borderImage:"conic-gradient(from 0deg, transparent 0%, ".concat(g.iconColor.replace("text-","")," 25%, transparent 50%, ").concat(g.iconColor.replace("text-","")," 75%, transparent 100%) 1"),filter:"drop-shadow(0 0 4px rgba(59, 130, 246, 0.5))"},className:"jsx-f56d70faa8a01b64 absolute -inset-1.5 w-9 h-9 rounded-full border-[3px] border-transparent animate-spin"}),(0,r.jsx)("div",{style:{animation:x?"spin 0.8s linear infinite reverse":"spin 1.6s linear infinite reverse",borderImage:"conic-gradient(from 180deg, transparent 0%, ".concat(g.iconColor.replace("text-","")," 30%, transparent 60%, ").concat(g.iconColor.replace("text-","")," 90%, transparent 100%) 1"),opacity:.8},className:"jsx-f56d70faa8a01b64 absolute -inset-1 w-8 h-8 rounded-full border-[2px] border-transparent animate-spin"}),(0,r.jsx)("div",{style:{borderColor:g.iconColor.replace("text-",""),opacity:.6,animation:"pulse 2s ease-in-out infinite"},className:"jsx-f56d70faa8a01b64 absolute -inset-0.5 w-7 h-7 rounded-full border border-transparent animate-pulse"}),(0,r.jsx)("div",{style:{boxShadow:"0 0 12px ".concat(g.iconColor.replace("text-",""),"40, 0 0 24px ").concat(g.iconColor.replace("text-",""),"20")},className:"jsx-f56d70faa8a01b64 "+"relative w-full h-full rounded-full flex items-center justify-center transition-all duration-500 ".concat(g.bgColor," border-2 ").concat(g.borderColor," shadow-lg backdrop-blur-sm"),children:(0,r.jsx)(h,{className:"jsx-f56d70faa8a01b64 "+"w-3.5 h-3.5 transition-all duration-500 ".concat(g.iconColor," ").concat(x?"scale-125 rotate-12":"scale-100"," drop-shadow-lg")})})]}),(0,r.jsxs)("div",{className:"jsx-f56d70faa8a01b64 "+"max-w-[65%] rounded-xl px-3 py-2 transition-all duration-500 ".concat(g.bgColor," ").concat(g.borderColor," border ").concat(g.glowColor," shadow-sm backdrop-blur-sm"),children:[(0,r.jsx)("div",{className:"jsx-f56d70faa8a01b64 flex items-center space-x-1.5",children:(0,r.jsxs)("div",{className:"jsx-f56d70faa8a01b64 transition-all duration-500",children:[(0,r.jsx)("span",{className:"jsx-f56d70faa8a01b64 "+"text-xs font-semibold transition-colors duration-500 ".concat(g.iconColor," tracking-wide"),children:o||g.text}),a&&"typing"===c&&!o&&(0,r.jsx)("span",{className:"jsx-f56d70faa8a01b64 "+"ml-1.5 text-[10px] opacity-80 ".concat(g.iconColor," font-medium"),children:"• Live"}),o&&(0,r.jsx)("span",{className:"jsx-f56d70faa8a01b64 "+"ml-1.5 text-[10px] opacity-80 ".concat(g.iconColor," font-medium"),children:"• Orchestrating"})]})}),("generating"===c||"typing"===c)&&(0,r.jsx)("div",{className:"jsx-f56d70faa8a01b64 mt-2",children:(0,r.jsx)("div",{className:"jsx-f56d70faa8a01b64 h-1 rounded-full overflow-hidden bg-white/30 backdrop-blur-sm border border-white/20",children:(0,r.jsx)("div",{style:{width:"typing"===c?"100%":"60%",animation:"typing"===c?"progressPulse 1.5s ease-in-out infinite, progressShimmer 2s linear infinite":"progressShimmer 2s linear infinite, progressGlow 3s ease-in-out infinite"},className:"jsx-f56d70faa8a01b64 "+"h-full rounded-full transition-all duration-1000 bg-gradient-to-r ".concat(g.gradientFrom," ").concat(g.gradientTo," relative overflow-hidden"),children:(0,r.jsx)("div",{style:{animation:"progressShine 2s linear infinite",transform:"skewX(-20deg)"},className:"jsx-f56d70faa8a01b64 absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"})})})})]}),(0,r.jsx)(i(),{id:"f56d70faa8a01b64",children:"@-webkit-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-moz-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-o-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-webkit-keyframes progressShine{0%{-webkit-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-webkit-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-moz-keyframes progressShine{0%{-moz-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-moz-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-o-keyframes progressShine{0%{-o-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-o-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@keyframes progressShine{0%{-webkit-transform:translatex(-100%)skewx(-20deg);-moz-transform:translatex(-100%)skewx(-20deg);-o-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-webkit-transform:translatex(300%)skewx(-20deg);-moz-transform:translatex(300%)skewx(-20deg);-o-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-webkit-keyframes progressPulse{0%,100%{opacity:1;-webkit-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-webkit-transform:scaley(1.1);transform:scaley(1.1)}}@-moz-keyframes progressPulse{0%,100%{opacity:1;-moz-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-moz-transform:scaley(1.1);transform:scaley(1.1)}}@-o-keyframes progressPulse{0%,100%{opacity:1;-o-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-o-transform:scaley(1.1);transform:scaley(1.1)}}@keyframes progressPulse{0%,100%{opacity:1;-webkit-transform:scaley(1);-moz-transform:scaley(1);-o-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-webkit-transform:scaley(1.1);-moz-transform:scaley(1.1);-o-transform:scaley(1.1);transform:scaley(1.1)}}@-webkit-keyframes progressGlow{0%,100%{-webkit-filter:brightness(1)drop-shadow(0 0 2px currentColor);filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{-webkit-filter:brightness(1.2)drop-shadow(0 0 6px currentColor);filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@-moz-keyframes progressGlow{0%,100%{filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@-o-keyframes progressGlow{0%,100%{filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@keyframes progressGlow{0%,100%{-webkit-filter:brightness(1)drop-shadow(0 0 2px currentColor);filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{-webkit-filter:brightness(1.2)drop-shadow(0 0 6px currentColor);filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}"})]})}},50956:(e,s,a)=>{a.d(s,{A:()=>d});var r=a(95155),t=a(12115),i=a(6874),l=a.n(i),n=a(35695);function d(e){let{href:s,children:a,className:i="",prefetch:d=!0}=e,o=(0,n.useRouter)();return(0,r.jsx)(l(),{href:s,className:i,onClick:e=>{e.preventDefault(),(0,t.startTransition)(()=>{o.push(s)})},prefetch:d,children:a})}},52469:(e,s,a)=>{a.d(s,{default:()=>l});var r=a(12115),t=a(35695);let i=["/features","/pricing","/about","/auth/signin","/auth/signup"];function l(){let e=(0,t.useRouter)();return(0,r.useEffect)(()=>{let s=()=>{i.forEach(s=>{e.prefetch(s)})};"requestIdleCallback"in window?window.requestIdleCallback(s,{timeout:2e3}):setTimeout(s,100)},[e]),null}},54547:(e,s,a)=>{a.d(s,{default:()=>F});var r=a(95155),t=a(35695),i=a(22261),l=a(99323),n=a(12115),d=a(6874),o=a.n(d),c=a(41045),m=a(34962),x=a(83298),u=a(52643);function g(){var e,s,a,t,l,d,g;let{isCollapsed:h,isHovered:p,toggleSidebar:b}=(0,i.c)(),{breadcrumb:f}=(0,m.rT)(),{user:j,subscriptionStatus:y}=(0,x.R)(),[v,N]=(0,n.useState)(!1),[w,k]=(0,n.useState)(!1),C=(0,u.u)();(0,n.useEffect)(()=>{let e=()=>{k(window.innerWidth>=1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let S=(null==j||null==(e=j.user_metadata)?void 0:e.first_name)||(null==j||null==(a=j.user_metadata)||null==(s=a.full_name)?void 0:s.split(" ")[0])||"User",_=S.charAt(0).toUpperCase()+((null==j||null==(d=j.user_metadata)||null==(l=d.last_name)||null==(t=l.charAt(0))?void 0:t.toUpperCase())||(null==(g=S.charAt(1))?void 0:g.toUpperCase())||"U"),A=(null==y?void 0:y.hasActiveSubscription)?(null==y?void 0:y.tier)==="starter"?"Starter Plan":(null==y?void 0:y.tier)==="professional"?"Professional Plan":(null==y?void 0:y.tier)==="enterprise"?"Enterprise Plan":"Starter Plan":"Starter Plan",E=async()=>{try{await C.auth.signOut(),window.location.href="/auth/signin"}catch(e){}};return(0,r.jsx)("nav",{className:"header border-b border-gray-200 bg-white/95 backdrop-blur-sm w-full",children:(0,r.jsx)("div",{className:"px-4 sm:px-6 lg:px-8 ".concat(w&&(!h||p)?"max-w-7xl mx-auto":w?"max-w-none":"max-w-7xl mx-auto"),children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("button",{onClick:b,className:"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200",title:"Toggle sidebar",children:(0,r.jsx)(c.tK,{className:"h-6 w-6 text-gray-600"})}),(0,r.jsx)("div",{className:"lg:hidden",children:(0,r.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"RouKey"})}),(0,r.jsxs)("div",{className:"hidden lg:flex items-center space-x-2 text-sm text-gray-600",children:[(0,r.jsx)("span",{children:f.title}),(0,r.jsx)("span",{children:"/"}),(0,r.jsx)("span",{className:"text-gray-900 font-medium",children:f.subtitle})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-4",children:[(0,r.jsx)("div",{className:"hidden xl:block",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:"text",placeholder:"Search...",className:"w-64 pl-10 pr-4 py-2.5 text-sm bg-white border border-gray-200 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-500 transition-all duration-200"}),(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)("svg",{className:"h-4 w-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})]})}),(0,r.jsxs)("button",{className:"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 relative",children:[(0,r.jsx)(c.XF,{className:"h-5 w-5 text-gray-600"}),(0,r.jsx)("span",{className:"absolute -top-1 -right-1 h-3 w-3 bg-orange-500 rounded-full"})]}),(0,r.jsxs)("div",{className:"hidden sm:block relative",children:[(0,r.jsxs)("button",{onClick:()=>N(!v),className:"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 flex items-center space-x-1",children:[(0,r.jsx)(c.Vy,{className:"h-5 w-5 text-gray-600"}),(0,r.jsx)(c.D3,{className:"h-3 w-3 text-gray-600 transition-transform duration-200 ".concat(v?"rotate-180":"")})]}),v&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"fixed inset-0 z-10",onClick:()=>N(!1)}),(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-20",children:[(0,r.jsxs)(o(),{href:"/dashboard/settings",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200",onClick:()=>N(!1),children:[(0,r.jsx)(c.Vy,{className:"h-4 w-4 mr-3 text-gray-500"}),"Settings"]}),(0,r.jsxs)(o(),{href:"/dashboard/billing",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200",onClick:()=>N(!1),children:[(0,r.jsx)(c.rM,{className:"h-4 w-4 mr-3 text-gray-500"}),"Billing"]}),(0,r.jsx)("hr",{className:"my-1 border-gray-200"}),(0,r.jsxs)("button",{onClick:E,className:"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200",children:[(0,r.jsx)(c.Rz,{className:"h-4 w-4 mr-3 text-red-500"}),"Sign Out"]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3 px-2 sm:px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer",children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-gradient-to-br from-orange-400 to-orange-500 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-semibold text-sm",children:_})}),(0,r.jsxs)("div",{className:"hidden md:block",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:S}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:A})]})]})]})]})})})}var h=a(66766),p=a(8652),b=a(14097),f=a(37843),j=a(24403),y=a(42724);let v=[{href:"/dashboard",label:"Dashboard",icon:p.fA,iconSolid:b.fA,description:"Overview & analytics"},{href:"/my-models",label:"My Models",icon:p.RY,iconSolid:b.RY,description:"API key management"},{href:"/playground",label:"Playground",icon:p.cu,iconSolid:b.cu,description:"Test your models"},{href:"/routing-setup",label:"Routing Setup",icon:p.sR,iconSolid:b.sR,description:"Configure routing"},{href:"/logs",label:"Logs",icon:p.AQ,iconSolid:b.AQ,description:"Request history"},{href:"/training",label:"Prompt Engineering",icon:p.tl,iconSolid:b.tl,description:"Custom prompts"},{href:"/analytics",label:"Analytics",icon:p.r9,iconSolid:b.r9,description:"Advanced insights"}];function N(){let e=(0,t.usePathname)(),{isCollapsed:s,isHovered:a,isHoverDisabled:d,setHovered:c}=(0,i.c)(),{navigateOptimistically:m}=(0,l.bu)()||{navigateOptimistically:()=>{}},{prefetchOnHover:x}=(0,f.C)(),{prefetchWhenIdle:u}=(0,f.e)(),{prefetchChatHistory:g}=(0,j.l2)(),{predictions:p,isLearning:b}=(0,y.x)(),N=(0,y.G)();(0,n.useEffect)(()=>{let s=v.map(e=>e.href),a=p.slice(0,2),r=N.filter(e=>"high"===e.priority).map(e=>e.route).slice(0,2);return u([...a,...r,...s.filter(s=>s!==e&&!a.includes(s)&&!r.includes(s)),"/playground","/logs"].slice(0,6))},[e,u,p,N,b]);let w=!s||a;return(0,r.jsx)("aside",{className:"sidebar flex flex-col h-full flex-shrink-0 transition-all duration-200 ease-out bg-gray-900 ".concat(w?"w-64":"w-16"),onMouseEnter:()=>!d&&c(!0),onMouseLeave:()=>!d&&c(!1),children:(0,r.jsx)("div",{className:"flex-1 relative overflow-hidden",children:(0,r.jsxs)("div",{className:"p-6 transition-all duration-200 ease-out ".concat(w?"px-6":"px-3"),children:[(0,r.jsx)("div",{className:"mb-8 pt-4 transition-all duration-200 ease-out ".concat(w?"":"text-center"),children:(0,r.jsxs)("div",{className:"relative overflow-hidden",children:[(0,r.jsx)("div",{className:"transition-all duration-200 ease-out ".concat(w?"opacity-0 scale-75 -translate-y-2":"opacity-100 scale-100 translate-y-0"," ").concat(w?"absolute":"relative"," w-8 h-8 bg-white rounded-lg flex items-center justify-center mx-auto p-0.5"),children:(0,r.jsx)(h.default,{src:"/roukey_logo.png",alt:"RouKey",width:28,height:28,className:"object-cover"})}),(0,r.jsxs)("div",{className:"transition-all duration-200 ease-out ".concat(w?"opacity-100 scale-100 translate-y-0":"opacity-0 scale-75 translate-y-2"," ").concat(w?"relative":"absolute top-0 left-0 w-full"),children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-white tracking-tight whitespace-nowrap",children:"RouKey"}),(0,r.jsx)("p",{className:"text-sm text-gray-400 mt-1 whitespace-nowrap",children:"Smart LLM Router"})]})]})}),(0,r.jsx)("nav",{className:"space-y-2",children:v.map(s=>{let a=e===s.href||e.startsWith(s.href+"/"),t=a?s.iconSolid:s.icon,i=p.includes(s.href),l=N.find(e=>e.route===s.href),n="/playground"===s.href?{onMouseEnter:()=>{if("/playground"===s.href){x(s.href,50).onMouseEnter();let e=new URLSearchParams(window.location.search).get("config");e&&g(e)}}}:x(s.href,50);return(0,r.jsx)(o(),{href:s.href,onClick:e=>{e.preventDefault(),m(s.href)},className:"sidebar-nav-item group flex items-center transition-all duration-200 ease-out w-full text-left ".concat(a?"active":""," ").concat(w?"":"collapsed"),title:w?void 0:s.label,...n,children:(0,r.jsxs)("div",{className:"relative flex items-center w-full overflow-hidden",children:[(0,r.jsxs)("div",{className:"relative flex items-center justify-center transition-all duration-200 ease-out ".concat(w?"w-5 h-5 mr-3":"w-10 h-10 rounded-xl"," ").concat(!w&&a?"bg-white shadow-sm":w?"":"bg-transparent hover:bg-white/10"),children:[(0,r.jsx)(t,{className:"transition-all duration-200 ease-out ".concat("h-5 w-5"," ").concat(a?"text-orange-500":"text-white")}),i&&!a&&(0,r.jsx)("div",{className:"absolute rounded-full bg-blue-400 animate-pulse transition-all duration-200 ease-out ".concat(w?"-top-1 -right-1 w-2 h-2":"-top-1 -right-1 w-3 h-3"),title:"Predicted next destination"})]}),(0,r.jsxs)("div",{className:"flex-1 transition-all duration-200 ease-out ".concat(w?"opacity-100 translate-x-0 max-w-full":"opacity-0 translate-x-4 max-w-0"),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between whitespace-nowrap",children:[(0,r.jsx)("div",{className:"font-medium text-sm",children:s.label}),l&&!a&&(0,r.jsx)("span",{className:"text-xs px-1.5 py-0.5 rounded-full ml-2 ".concat("high"===l.priority?"bg-blue-500/20 text-blue-300":"bg-gray-500/20 text-gray-300"),children:"high"===l.priority?"!":"\xb7"})]}),(0,r.jsx)("div",{className:"text-xs transition-colors duration-200 whitespace-nowrap ".concat(a?"text-orange-400":"text-gray-400"),children:l?l.reason:s.description})]})]})},s.href)})})]})})})}var w=a(89732),k=a(95565);let C={"/dashboard":{title:"Dashboard",subtitle:"Loading overview & analytics...",icon:w.fA,color:"text-blue-500",bgColor:"bg-blue-50"},"/my-models":{title:"My Models",subtitle:"Loading API key management...",icon:w.RY,color:"text-green-500",bgColor:"bg-green-50"},"/playground":{title:"Playground",subtitle:"Loading model testing environment...",icon:w.cu,color:"text-orange-500",bgColor:"bg-orange-50"},"/routing-setup":{title:"Routing Setup",subtitle:"Loading routing configuration...",icon:w.sR,color:"text-purple-500",bgColor:"bg-purple-50"},"/logs":{title:"Logs",subtitle:"Loading request history...",icon:w.AQ,color:"text-gray-500",bgColor:"bg-gray-50"},"/training":{title:"Prompt Engineering",subtitle:"Loading custom prompts...",icon:w.tl,color:"text-indigo-500",bgColor:"bg-indigo-50"},"/analytics":{title:"Analytics",subtitle:"Loading advanced insights...",icon:w.r9,color:"text-pink-500",bgColor:"bg-pink-50"}};function S(e){let{targetRoute:s}=e,{clearNavigation:a}=(0,l.bu)()||{clearNavigation:()=>{}};if(!(s?C[s]:null))return(0,r.jsx)("div",{className:"flex items-center justify-center min-h-[60vh]",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4 animate-pulse",children:(0,r.jsx)(w.cu,{className:"w-8 h-8 text-gray-400"})}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Loading..."}),(0,r.jsx)("p",{className:"text-gray-500",children:"Please wait while we load the page"})]})});let t=()=>(0,r.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-1/4"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-20"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,r.jsx)("div",{className:"lg:col-span-1 space-y-3",children:Array.from({length:5}).map((e,s)=>(0,r.jsx)("div",{className:"h-16 bg-gray-200 rounded-lg animate-pulse"},s))}),(0,r.jsx)("div",{className:"lg:col-span-3",children:(0,r.jsx)("div",{className:"h-96 bg-gray-200 rounded-lg animate-pulse"})})]})]}),i=()=>(0,r.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-1/4"}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-20"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-16"})]})]}),(0,r.jsx)("div",{className:"space-y-3",children:Array.from({length:8}).map((e,s)=>(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded-lg animate-pulse"},s))})]});return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("button",{onClick:a,className:"absolute top-4 right-4 z-10 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",title:"Cancel loading",children:(0,r.jsx)(w.fK,{className:"w-5 h-5"})}),(()=>{switch(s){case"/dashboard":default:return(0,r.jsx)(k.O2,{});case"/my-models":return(0,r.jsx)(k.MyModelsSkeleton,{});case"/playground":return(0,r.jsx)(t,{});case"/routing-setup":return(0,r.jsx)(k.RoutingSetupSkeleton,{});case"/logs":return(0,r.jsx)(i,{});case"/training":return(0,r.jsx)(k.vD,{});case"/analytics":return(0,r.jsx)(k.AnalyticsSkeleton,{})}})()]})}let _=()=>(0,r.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[1,2,3].map(e=>(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-4"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"})]},e))}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,r.jsx)("div",{className:"space-y-3",children:[1,2,3,4].map(e=>(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))})]})]}),A=()=>(0,r.jsxs)("div",{className:"space-y-8 animate-pulse",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded w-1/2 mx-auto mb-4"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[1,2,3].map(e=>(0,r.jsxs)("div",{className:"bg-white p-8 rounded-2xl border-2",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2 mx-auto mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mx-auto mb-4"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-1/3 mx-auto"})]}),(0,r.jsx)("div",{className:"space-y-3 mb-8",children:[1,2,3,4,5].map(e=>(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))}),(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"})]},e))})]}),E=()=>(0,r.jsxs)("div",{className:"space-y-8 animate-pulse",children:[(0,r.jsxs)("div",{className:"text-center py-20 bg-gradient-to-br from-slate-50 to-blue-50",children:[(0,r.jsx)("div",{className:"h-16 bg-gray-200 rounded w-2/3 mx-auto mb-6"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,r.jsx)("div",{className:"py-20",children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-12",children:[1,2,3,4,5,6].map(e=>(0,r.jsx)("div",{className:"bg-white rounded-2xl p-8 border",children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-xl"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2 mb-3"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded mb-4"}),(0,r.jsx)("div",{className:"space-y-2",children:[1,2,3].map(e=>(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-3/4"},e))})]})]})},e))})})]}),P=()=>(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50",children:(0,r.jsxs)("div",{className:"bg-white p-8 rounded-2xl shadow-lg border w-full max-w-md animate-pulse",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2 mx-auto mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"})]})]})}),T=()=>(0,r.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),(0,r.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))}),(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded mt-4"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded mt-4"})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),(0,r.jsx)("div",{className:"h-64 bg-gray-200 rounded"})]})]})]}),z=()=>(0,r.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3"}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg border",children:(0,r.jsx)("div",{className:"space-y-4",children:[1,2,3,4,5].map(e=>(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))})})]});function L(e){let s,{targetRoute:a,children:i}=e,[d,o]=(0,n.useState)(!0),[c,m]=(0,n.useState)(!1),x=(0,t.usePathname)(),u=(0,n.useRef)(),{isPageCached:g}=(0,l.bu)()||{isPageCached:()=>!1};return((0,n.useEffect)(()=>(x===a&&(u.current=setTimeout(()=>{m(!0),setTimeout(()=>o(!1),100)},g(a)?50:200)),()=>{u.current&&clearTimeout(u.current)}),[x,a,g]),(0,n.useEffect)(()=>{o(!0),m(!1)},[a]),x!==a&&d||x===a&&d&&!c)?(0,r.jsx)("div",{className:"optimistic-loading-container",children:(s=a).startsWith("/dashboard")?(0,r.jsx)(_,{}):s.startsWith("/pricing")?(0,r.jsx)(A,{}):s.startsWith("/features")?(0,r.jsx)(E,{}):s.startsWith("/auth/")?(0,r.jsx)(P,{}):s.startsWith("/playground")?(0,r.jsx)(T,{}):(0,r.jsx)(z,{})}):(0,r.jsx)("div",{className:"transition-opacity duration-300 ".concat(c?"opacity-100":"opacity-0"),children:i})}var R=a(42126);function D(e){let{children:s}=e,{isCollapsed:a,isHovered:t,collapseSidebar:d}=(0,i.c)(),{isNavigating:o,targetRoute:c,isPageCached:m}=(0,l.bu)()||{isNavigating:!1,targetRoute:null,isPageCached:()=>!1},[x,u]=(0,n.useState)(!1);(0,n.useEffect)(()=>{let e=()=>{u(window.innerWidth>=1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let h=x?!a||t?256:64:0;return(0,R.v)({maxConcurrent:2,idleTimeout:1500,backgroundDelay:3e3}),(0,r.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,r.jsx)("div",{className:"hidden lg:block fixed left-0 top-0 h-full z-40",children:(0,r.jsx)(N,{})}),(0,r.jsxs)("div",{className:"lg:hidden fixed inset-0 z-50 ".concat(a?"pointer-events-none":""),children:[(0,r.jsx)("div",{onClick:d,className:"absolute inset-0 bg-black transition-opacity duration-200 ease-out cursor-pointer ".concat(a?"opacity-0":"opacity-50")}),(0,r.jsx)("div",{className:"absolute left-0 top-0 h-full transform transition-transform duration-200 ease-out ".concat(a?"-translate-x-full":"translate-x-0"),children:(0,r.jsx)(N,{})})]}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden min-w-0 transition-all duration-200 ease-out",style:{marginLeft:"".concat(h,"px")},children:[(0,r.jsx)("div",{className:"fixed top-0 right-0 z-30 transition-all duration-200 ease-out",style:{left:"".concat(h,"px")},children:(0,r.jsx)(g,{})}),(0,r.jsx)("main",{className:"flex-1 overflow-y-auto content-area mt-16",children:(0,r.jsx)("div",{className:"p-4 sm:p-6 lg:p-8 w-full ".concat(x&&(!a||t)?"max-w-7xl mx-auto":x?"max-w-none px-8":"max-w-7xl mx-auto"),children:(0,r.jsx)("div",{className:"page-transition",children:o&&c?(0,r.jsx)(L,{targetRoute:c,children:s}):s})})})]})]})}function M(e){let{children:s}=e;return(0,r.jsx)(n.Suspense,{fallback:(0,r.jsx)(S,{targetRoute:null}),children:(0,r.jsx)(D,{children:s})})}function F(e){let{children:s}=e,a=(0,t.usePathname)();return"/"===a||a.startsWith("/pricing")||a.startsWith("/features")||a.startsWith("/about")||a.startsWith("/auth/")?(0,r.jsx)(r.Fragment,{children:s}):(0,r.jsx)(i.G,{children:(0,r.jsx)(l.i9,{children:(0,r.jsx)(M,{children:s})})})}},71848:(e,s,a)=>{a.d(s,{A:()=>n});var r=a(95155),t=a(23405);let i=e=>{let{label:s,value:a}=e;return(0,r.jsxs)("div",{className:"py-2 sm:grid sm:grid-cols-3 sm:gap-4",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-700",children:s}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 break-words",children:null!=a&&""!==a?a:"N/A"})]})},l=e=>{let s,{title:a,data:t}=e;if(null==t)s="N/A";else if("string"==typeof t)s=t;else try{s=JSON.stringify(t,null,2)}catch(e){s="Invalid JSON data"}return(0,r.jsxs)("div",{className:"py-2",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-700 mb-1",children:a}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900 bg-gray-50 p-3 rounded-lg border",children:(0,r.jsx)("pre",{className:"whitespace-pre-wrap break-all",children:s})})]})};function n(e){var s;let{log:a,onClose:n,apiConfigNameMap:d}=e;if(!a)return null;let o=a.custom_api_config_id?d[a.custom_api_config_id]||"Unknown Model":"N/A";return(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50 transition-opacity duration-300 ease-in-out",onClick:n,children:(0,r.jsxs)("div",{className:"card max-w-2xl w-full max-h-[90vh] flex flex-col",onClick:e=>e.stopPropagation(),children:[(0,r.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-200",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["Log Details (ID: ",a.id.substring(0,8),"...)"]}),(0,r.jsx)("button",{onClick:n,className:"text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200",children:(0,r.jsx)(t.f,{className:"h-6 w-6"})})]}),(0,r.jsxs)("div",{className:"p-6 space-y-4 overflow-y-auto",children:[(0,r.jsxs)("dl",{className:"divide-y divide-gray-200",children:[(0,r.jsx)(i,{label:"Timestamp",value:new Date(a.request_timestamp).toLocaleString()}),(0,r.jsx)(i,{label:"API Model Used",value:o}),(0,r.jsx)(i,{label:"Role Requested",value:a.role_requested}),(0,r.jsx)(i,{label:"Role Used",value:a.role_used}),(0,r.jsx)(i,{label:"Status",value:null===(s=a.status_code)?(0,r.jsx)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-gray-600 text-gray-100",children:"N/A"}):s>=200&&s<300?(0,r.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-green-600 text-green-100",children:["Success (",s,")"]}):s>=400?(0,r.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-red-600 text-red-100",children:["Error (",s,")"]}):(0,r.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-yellow-500 text-yellow-100",children:["Other (",s,")"]})}),(0,r.jsx)(i,{label:"LLM Provider",value:a.llm_provider_name}),(0,r.jsx)(i,{label:"LLM Model Name",value:a.llm_model_name}),(0,r.jsx)(i,{label:"LLM Latency",value:null!==a.llm_provider_latency_ms?"".concat(a.llm_provider_latency_ms," ms"):"N/A"}),(0,r.jsx)(i,{label:"RoKey Latency",value:null!==a.processing_duration_ms?"".concat(a.processing_duration_ms," ms"):"N/A"}),(0,r.jsx)(i,{label:"Input Tokens",value:null!==a.input_tokens?a.input_tokens:"N/A"}),(0,r.jsx)(i,{label:"Output Tokens",value:null!==a.output_tokens?a.output_tokens:"N/A"}),(0,r.jsx)(i,{label:"Cost",value:null!==a.cost?"$".concat(a.cost.toFixed(6)):"N/A"}),(0,r.jsx)(i,{label:"Multimodal Request",value:a.is_multimodal?"Yes":"No"}),(0,r.jsx)(i,{label:"IP Address",value:a.ip_address}),a.user_id&&(0,r.jsx)(i,{label:"User ID",value:a.user_id}),a.error_message&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i,{label:"Error Message",value:a.error_message}),(0,r.jsx)(i,{label:"Error Source",value:a.error_source})]}),a.llm_provider_status_code&&(0,r.jsx)(i,{label:"LLM Provider Status",value:a.llm_provider_status_code})]}),a.request_payload_summary&&(0,r.jsx)(l,{title:"Request Payload Summary",data:a.request_payload_summary}),a.response_payload_summary&&(0,r.jsx)(l,{title:"Response Payload Summary",data:a.response_payload_summary}),a.error_details_zod&&(0,r.jsx)(l,{title:"Zod Validation Error Details",data:a.error_details_zod})]}),(0,r.jsx)("div",{className:"p-6 border-t border-gray-200 text-right",children:(0,r.jsx)("button",{onClick:n,className:"btn-secondary",children:"Close"})})]})})}},73360:(e,s,a)=>{a.d(s,{A:()=>l});var r=a(95155),t=a(12115),i=a(58637);function l(e){let{configId:s,onDocumentUploaded:a}=e,[l,n]=(0,t.useState)([]),[d,o]=(0,t.useState)(!1),[c,m]=(0,t.useState)(!1),[x,u]=(0,t.useState)(0),[g,h]=(0,t.useState)(!1),[p,b]=(0,t.useState)(null),[f,j]=(0,t.useState)(null),y=(0,t.useRef)(null),v=(0,t.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,a=arguments.length>1?arguments[1]:void 0;if(s){0===e&&m(!0);try{let r=await fetch("/api/documents/list?configId=".concat(s));if(r.ok){let s=(await r.json()).documents||[];if(a&&e<3&&!s.find(e=>e.id===a))return void setTimeout(()=>{v(e+1,a)},(e+1)*500);n(e=>{let a=new Set(e.map(e=>e.id)),r=s.filter(e=>!a.has(e.id));return[...e.map(e=>s.find(s=>s.id===e.id)||e),...r].sort((e,s)=>new Date(s.created_at).getTime()-new Date(e.created_at).getTime())})}}catch(s){e<2&&setTimeout(()=>{v(e+1,a)},1e3)}finally{(0===e||a)&&m(!1)}}},[s]);t.useEffect(()=>{v()},[v]);let N=async e=>{if(!s)return void b("Please select an API configuration first");let r=e[0];if(r){if(!["application/pdf","text/plain","text/markdown"].includes(r.type))return void b("Please upload PDF, TXT, or MD files only");if(r.size>0xa00000)return void b("File size must be less than 10MB");o(!0),b(null),j(null),u(0);try{let e=new FormData;e.append("file",r),e.append("configId",s);let t=setInterval(()=>{u(e=>Math.min(e+10,90))},200),i=await fetch("/api/documents/upload",{method:"POST",body:e});if(clearInterval(t),u(100),!i.ok){let e=await i.json();throw Error(e.error||"Upload failed")}let l=await i.json();j("✨ ".concat(r.name," uploaded successfully! Processing ").concat(l.document.chunks_total," chunks."));let d={id:l.document.id,filename:l.document.filename||r.name,file_type:r.type,file_size:r.size,status:l.document.status||"processing",chunks_count:l.document.chunks_processed||0,created_at:new Date().toISOString()};n(e=>e.find(e=>e.id===d.id)?e.map(e=>e.id===d.id?d:e):[d,...e]),setTimeout(async()=>{await v(0,l.document.id)},200),null==a||a()}catch(e){b("Upload failed: ".concat(e.message)),setTimeout(()=>b(null),8e3)}finally{o(!1),u(0),y.current&&(y.current.value=""),f&&setTimeout(()=>j(null),5e3)}}},w=(0,t.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?h(!0):"dragleave"===e.type&&h(!1)},[]),k=(0,t.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),h(!1),e.dataTransfer.files&&e.dataTransfer.files[0]&&N(e.dataTransfer.files)},[s]),C=async e=>{if(confirm("Are you sure you want to delete this document?")){n(s=>s.filter(s=>s.id!==e));try{if(!(await fetch("/api/documents/".concat(e),{method:"DELETE"})).ok)throw n(l),Error("Failed to delete document");j("Document deleted successfully"),await v(),setTimeout(()=>j(null),3e3)}catch(e){n(l),b("Delete failed: ".concat(e.message)),setTimeout(()=>b(null),8e3)}}},S=e=>{if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["Bytes","KB","MB","GB"][s]},_=e=>e.includes("pdf")?(0,r.jsx)(i.iU,{className:"w-5 h-5 text-red-500"}):e.includes("word")?(0,r.jsx)(i.iU,{className:"w-5 h-5 text-blue-500"}):(0,r.jsx)(i.ZH,{className:"w-5 h-5 text-gray-500"});return(0,r.jsxs)("div",{className:"space-y-6",children:[p&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(i.RI,{className:"w-5 h-5 text-red-600"}),(0,r.jsx)("p",{className:"text-red-800 text-sm font-medium",children:p})]})}),f&&(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-xl p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(i.rA,{className:"w-5 h-5 text-green-600"}),(0,r.jsx)("p",{className:"text-green-800 text-sm font-medium",children:f})]})}),(0,r.jsxs)("div",{className:"relative border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-300 transform ".concat(g?"border-orange-400 bg-orange-50 scale-105 shadow-lg":"border-gray-300 hover:border-orange-400 hover:bg-orange-50 hover:scale-102 hover:shadow-md"," ").concat(s?"cursor-pointer":"opacity-50 cursor-not-allowed"),onDragEnter:w,onDragLeave:w,onDragOver:w,onDrop:k,onClick:()=>{var e;return s&&(null==(e=y.current)?void 0:e.click())},children:[(0,r.jsx)("input",{ref:y,type:"file",className:"hidden",accept:".pdf,.txt,.md",onChange:e=>{e.target.files&&e.target.files[0]&&N(e.target.files)},disabled:!s||d}),d?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(i.kr,{className:"w-12 h-12 text-orange-500 mx-auto animate-spin"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("p",{className:"text-lg font-medium text-gray-900",children:"Processing Document..."}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-orange-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(x,"%")}})}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[x,"% complete"]})]})]}):(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(i._O,{className:"w-12 h-12 text-gray-400 mx-auto"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-lg font-medium text-gray-900",children:s?"Upload Knowledge Documents":"Select a configuration first"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Drag and drop files here, or click to browse"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"Supports PDF, TXT, MD files up to 10MB"})]})]})]}),l.length>0&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Uploaded Documents"}),c&&(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,r.jsx)(i.kr,{className:"w-4 h-4 animate-spin"}),(0,r.jsx)("span",{children:"Refreshing..."})]})]}),(0,r.jsx)("div",{className:"grid gap-4",children:l.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-white border border-gray-200 rounded-xl hover:shadow-md transition-shadow",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[_(e.file_type),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:e.filename}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[S(e.file_size)," • ",e.chunks_count," chunks"]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:["completed"===e.status&&(0,r.jsx)(i.rA,{className:"w-5 h-5 text-green-500"}),"processing"===e.status&&(0,r.jsx)(i.kr,{className:"w-5 h-5 text-orange-500 animate-spin"}),"failed"===e.status&&(0,r.jsx)(i.RI,{className:"w-5 h-5 text-red-500"}),(0,r.jsx)("span",{className:"text-sm font-medium ".concat("completed"===e.status?"text-green-600":"processing"===e.status?"text-orange-600":"text-red-600"),children:"completed"===e.status?"Ready":"processing"===e.status?"Processing":"Failed"})]}),(0,r.jsx)("button",{onClick:()=>C(e.id),className:"p-1 text-gray-400 hover:text-red-500 transition-colors",title:"Delete document",children:(0,r.jsx)(i.X,{className:"w-4 h-4"})})]})]},e.id))})]})]})}},74338:(e,s,a)=>{a.d(s,{B0:()=>t,F6:()=>i});var r=a(95155);function t(e){let{className:s=""}=e;return(0,r.jsx)("div",{className:"glass rounded-2xl p-6 animate-pulse ".concat(s),children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-700 rounded-xl"}),(0,r.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-700 rounded w-3/4"}),(0,r.jsx)("div",{className:"h-3 bg-gray-700 rounded w-1/2"})]})]})})}function i(e){let{rows:s=5,columns:a=4}=e;return(0,r.jsx)("div",{className:"glass rounded-2xl overflow-hidden",children:(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"bg-gray-800 p-4 border-b border-gray-700",children:(0,r.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(a,", 1fr)")},children:Array.from({length:a}).map((e,s)=>(0,r.jsx)("div",{className:"h-4 bg-gray-700 rounded"},s))})}),Array.from({length:s}).map((e,s)=>(0,r.jsx)("div",{className:"p-4 border-b border-gray-700 last:border-b-0",children:(0,r.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(a,", 1fr)")},children:Array.from({length:a}).map((e,s)=>(0,r.jsx)("div",{className:"h-4 bg-gray-700 rounded"},s))})},s))]})})}},79112:(e,s,a)=>{a.d(s,{A:()=>n});var r=a(95155),t=a(12115);let i=(0,t.lazy)(()=>Promise.all([a.e(5006),a.e(5928),a.e(4726),a.e(4280),a.e(2548),a.e(8960),a.e(8961),a.e(703),a.e(3285),a.e(274),a.e(3613),a.e(5260),a.e(7525),a.e(3310),a.e(7096),a.e(7455),a.e(678),a.e(8730)]).then(a.bind(a,90882))),l=()=>(0,r.jsxs)("div",{className:"space-y-2 animate-pulse",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-5/6"})]});function n(e){let{content:s,className:a=""}=e;return(0,r.jsx)(t.Suspense,{fallback:(0,r.jsx)(l,{}),children:(0,r.jsx)(i,{content:s,className:a})})}},79958:(e,s,a)=>{a.d(s,{A:()=>t,_:()=>i});var r=a(95155);function t(){return(0,r.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-48 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-64 animate-pulse"})]})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-6",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-32 mb-6 animate-pulse"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-12 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse"})]})]}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-40 animate-pulse"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-24 animate-pulse"})]}),(0,r.jsx)("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gray-200 rounded-lg animate-pulse"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-5 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-48 animate-pulse"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16 animate-pulse"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16 animate-pulse"})]})]}),(0,r.jsxs)("div",{className:"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-16 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 animate-pulse"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-12 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 animate-pulse"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-20 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 animate-pulse"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-14 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-18 animate-pulse"})]})]}),(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-28 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:[1,2,3].map(e=>(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded-full w-20 animate-pulse"},e))})]})]},e))})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-36 animate-pulse"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-28 animate-pulse"})]}),(0,r.jsx)("div",{className:"space-y-3",children:[1,2].map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-48 animate-pulse"})]}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16 animate-pulse"})]},e))})]})]})}function i(){return(0,r.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-40 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-56 animate-pulse"})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[1,2,3,4].map(e=>(0,r.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-32 animate-pulse"})]})]})},e))})]})}a(12115)},80377:(e,s,a)=>{a.d(s,{A:()=>l});var r=a(95155),t=a(12115),i=a(38152);function l(e){let{isOpen:s,onClose:a,onConfirm:l,title:n,message:d,confirmText:o="Delete",cancelText:c="Cancel",type:m="danger",isLoading:x=!1}=e;(0,t.useEffect)(()=>{let e=e=>{"Escape"===e.key&&s&&!x&&a()};return s&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[s,x,a]);let u=(()=>{switch(m){case"danger":return{iconBg:"bg-red-100",iconColor:"text-red-600",confirmButton:"bg-red-600 hover:bg-red-700 focus:ring-red-500",icon:i.uc};case"warning":return{iconBg:"bg-yellow-100",iconColor:"text-yellow-600",confirmButton:"bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500",icon:i.Pi};default:return{iconBg:"bg-blue-100",iconColor:"text-blue-600",confirmButton:"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500",icon:i.Pi}}})(),g=u.icon;return s?(0,r.jsxs)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300",onClick:x?void 0:a}),(0,r.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"relative w-full max-w-md transform overflow-hidden rounded-2xl bg-white shadow-2xl transition-all duration-300 scale-100 opacity-100",children:[(0,r.jsx)("div",{className:"relative px-6 pt-6",children:(0,r.jsx)("button",{onClick:a,disabled:x,className:"absolute top-4 right-4 p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,r.jsx)(i.fK,{className:"h-5 w-5"})})}),(0,r.jsxs)("div",{className:"px-6 pb-6",children:[(0,r.jsx)("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-full mb-6",children:(0,r.jsx)("div",{className:"".concat(u.iconBg," rounded-full p-3"),children:(0,r.jsx)(g,{className:"h-8 w-8 ".concat(u.iconColor)})})}),(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3 text-center",children:n}),(0,r.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed mb-8 text-center",children:d}),(0,r.jsxs)("div",{className:"flex flex-col-reverse sm:flex-row sm:justify-center gap-3",children:[(0,r.jsx)("button",{type:"button",onClick:a,disabled:x,className:"w-full sm:w-auto px-6 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200",children:c}),(0,r.jsx)("button",{type:"button",onClick:l,disabled:x,className:"w-full sm:w-auto px-6 py-3 text-sm font-medium text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 ".concat(u.confirmButton),children:x?(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Processing..."]}):o})]})]})]})})]}):null}},90882:(e,s,a)=>{a.r(s),a.d(s,{default:()=>o});var r=a(95155),t=a(28831),i=a(70765),l=a(18730),n=a(15478),d=a(95803);function o(e){let{content:s,className:a=""}=e;return(0,r.jsx)("div",{className:"markdown-content ".concat(a),children:(0,r.jsx)(t.Ay,{remarkPlugins:[i.A],components:{h1:e=>{let{children:s}=e;return(0,r.jsx)("h1",{className:"text-xl font-bold mb-3 mt-4 first:mt-0 text-gray-900",children:s})},h2:e=>{let{children:s}=e;return(0,r.jsx)("h2",{className:"text-lg font-bold mb-2 mt-3 first:mt-0 text-gray-900",children:s})},h3:e=>{let{children:s}=e;return(0,r.jsx)("h3",{className:"text-base font-bold mb-2 mt-3 first:mt-0 text-gray-900",children:s})},h4:e=>{let{children:s}=e;return(0,r.jsx)("h4",{className:"text-sm font-bold mb-1 mt-2 first:mt-0 text-gray-900",children:s})},p:e=>{let{children:s}=e;return(0,r.jsx)("p",{className:"mb-3 last:mb-0 leading-relaxed text-gray-900 break-words",children:s})},strong:e=>{let{children:s}=e;return(0,r.jsx)("strong",{className:"font-bold text-gray-900",children:s})},em:e=>{let{children:s}=e;return(0,r.jsx)("em",{className:"italic text-gray-900",children:s})},ul:e=>{let{children:s}=e;return(0,r.jsx)("ul",{className:"list-disc list-inside mb-3 space-y-1 text-gray-900",children:s})},ol:e=>{let{children:s}=e;return(0,r.jsx)("ol",{className:"list-decimal list-inside mb-3 space-y-1 text-gray-900",children:s})},li:e=>{let{children:s}=e;return(0,r.jsx)("li",{className:"leading-relaxed text-gray-900",children:s})},code:e=>{let{node:s,inline:a,className:t,children:i,...o}=e,c=/language-(\w+)/.exec(t||""),m=c?c[1]:"",x=String(i).replace(/\n$/,"");if(!a)if(m)return(0,r.jsxs)("div",{className:"my-3 rounded-lg overflow-hidden relative group",children:[(0,r.jsx)("div",{className:"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200",children:(0,r.jsx)(d.A,{text:x,variant:"code",size:"sm",title:"Copy code",className:"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50"})}),(0,r.jsx)(l.M,{style:n.bM,language:m,PreTag:"div",className:"text-sm",...o,children:x})]});else return(0,r.jsxs)("div",{className:"my-3 rounded-lg overflow-hidden relative group bg-gray-900 text-gray-100",children:[(0,r.jsx)("div",{className:"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200",children:(0,r.jsx)(d.A,{text:x,variant:"code",size:"sm",title:"Copy code",className:"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50"})}),(0,r.jsx)("pre",{className:"p-4 text-sm font-mono overflow-x-auto",children:(0,r.jsx)("code",{children:x})})]});return(0,r.jsx)("code",{className:"bg-gray-100 text-gray-900 px-1.5 py-0.5 rounded text-sm font-mono",...o,children:i})},blockquote:e=>{let{children:s}=e;return(0,r.jsx)("blockquote",{className:"border-l-4 border-orange-500 pl-4 my-3 italic text-gray-700",children:s})},a:e=>{let{children:s,href:a}=e;return(0,r.jsx)("a",{href:a,target:"_blank",rel:"noopener noreferrer",className:"text-orange-600 hover:text-orange-700 underline transition-colors duration-200",children:s})},table:e=>{let{children:s}=e;return(0,r.jsx)("div",{className:"overflow-x-auto my-3",children:(0,r.jsx)("table",{className:"min-w-full border border-gray-200 rounded-lg",children:s})})},thead:e=>{let{children:s}=e;return(0,r.jsx)("thead",{className:"bg-gray-50",children:s})},tbody:e=>{let{children:s}=e;return(0,r.jsx)("tbody",{className:"divide-y divide-gray-200 bg-white",children:s})},tr:e=>{let{children:s}=e;return(0,r.jsx)("tr",{className:"hover:bg-gray-50",children:s})},th:e=>{let{children:s}=e;return(0,r.jsx)("th",{className:"px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border-b border-gray-200",children:s})},td:e=>{let{children:s}=e;return(0,r.jsx)("td",{className:"px-3 py-2 text-sm text-gray-900 border-b border-gray-200",children:s})},hr:()=>(0,r.jsx)("hr",{className:"my-4 border-gray-200"})},children:s})})}},95565:(e,s,a)=>{a.d(s,{AnalyticsSkeleton:()=>m,ConfigSelectorSkeleton:()=>l,MessageSkeleton:()=>i,MyModelsSkeleton:()=>d,O2:()=>n,RoutingSetupSkeleton:()=>o,vD:()=>c});var r=a(95155);a(11518),a(12115);let t=e=>{let{className:s="",variant:a="text",width:t="100%",height:i="1rem",lines:l=1}=e,n="animate-pulse bg-gray-200 rounded",d=()=>{switch(a){case"circular":return"rounded-full";case"rectangular":return"rounded-lg";default:return"rounded"}},o={width:"number"==typeof t?"".concat(t,"px"):t,height:"number"==typeof i?"".concat(i,"px"):i};return l>1?(0,r.jsx)("div",{className:"space-y-2 ".concat(s),children:Array.from({length:l}).map((e,s)=>(0,r.jsx)("div",{className:"".concat(n," ").concat(d()),style:{...o,width:s===l-1?"75%":o.width}},s))}):(0,r.jsx)("div",{className:"".concat(n," ").concat(d()," ").concat(s),style:o})},i=()=>(0,r.jsx)("div",{className:"space-y-6 py-8",children:Array.from({length:3}).map((e,s)=>(0,r.jsx)("div",{className:"flex ".concat(s%2==0?"justify-end":"justify-start"),children:(0,r.jsx)("div",{className:"max-w-3xl p-4 rounded-2xl ".concat(s%2==0?"bg-orange-50":"bg-white border border-gray-200"),children:(0,r.jsx)(t,{lines:3,height:"1rem"})})},s))}),l=()=>(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(t,{variant:"circular",width:32,height:32}),(0,r.jsx)(t,{width:"8rem",height:"1.5rem"})]}),n=()=>(0,r.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(t,{height:"2.5rem",width:"12rem",className:"mb-2"}),(0,r.jsx)(t,{height:"1.25rem",width:"20rem"})]}),(0,r.jsx)(t,{variant:"rectangular",height:"2.5rem",width:"8rem"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:Array.from({length:4}).map((e,s)=>(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)(t,{variant:"circular",width:40,height:40}),(0,r.jsx)(t,{height:"1rem",width:"3rem"})]}),(0,r.jsx)(t,{height:"2rem",width:"4rem",className:"mb-2"}),(0,r.jsx)(t,{height:"0.875rem",width:"6rem"})]},s))}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsx)(t,{height:"1.5rem",width:"8rem",className:"mb-4"}),(0,r.jsx)(t,{variant:"rectangular",height:"20rem"})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsx)(t,{height:"1.5rem",width:"10rem",className:"mb-4"}),(0,r.jsx)(t,{variant:"rectangular",height:"20rem"})]})]})]}),d=()=>(0,r.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(t,{height:"2.5rem",width:"10rem",className:"mb-2"}),(0,r.jsx)(t,{height:"1.25rem",width:"18rem"})]}),(0,r.jsx)(t,{variant:"rectangular",height:"2.5rem",width:"10rem"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((e,s)=>(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(t,{height:"1.5rem",width:"8rem",className:"mb-2"}),(0,r.jsx)(t,{height:"1rem",width:"12rem"})]}),(0,r.jsx)(t,{variant:"circular",width:32,height:32})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)(t,{height:"0.875rem",width:"4rem"}),(0,r.jsx)(t,{height:"0.875rem",width:"2rem"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)(t,{height:"0.875rem",width:"5rem"}),(0,r.jsx)(t,{height:"0.875rem",width:"3rem"})]})]})]},s))})]}),o=()=>(0,r.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(t,{height:"3rem",width:"16rem",className:"mx-auto mb-4"}),(0,r.jsx)(t,{height:"1.25rem",width:"24rem",className:"mx-auto"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:Array.from({length:4}).map((e,s)=>(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)(t,{variant:"circular",width:48,height:48,className:"mr-4"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(t,{height:"1.5rem",width:"10rem",className:"mb-2"}),(0,r.jsx)(t,{height:"1rem",width:"8rem"})]})]}),(0,r.jsx)(t,{lines:3,height:"0.875rem"})]},s))})]}),c=()=>(0,r.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(t,{height:"2.5rem",width:"8rem",className:"mb-2"}),(0,r.jsx)(t,{height:"1.25rem",width:"16rem"})]}),(0,r.jsx)(t,{variant:"rectangular",height:"2.5rem",width:"12rem"})]}),(0,r.jsx)("div",{className:"card p-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(t,{variant:"circular",width:64,height:64,className:"mx-auto mb-4"}),(0,r.jsx)(t,{height:"1.5rem",width:"12rem",className:"mx-auto mb-2"}),(0,r.jsx)(t,{height:"1rem",width:"20rem",className:"mx-auto"})]})}),(0,r.jsxs)("div",{className:"card",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,r.jsx)(t,{height:"1.5rem",width:"10rem"})}),(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:Array.from({length:3}).map((e,s)=>(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(t,{height:"1.25rem",width:"12rem",className:"mb-2"}),(0,r.jsx)(t,{height:"1rem",width:"8rem"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(t,{height:"1.5rem",width:"4rem"}),(0,r.jsx)(t,{variant:"circular",width:32,height:32})]})]})},s))})]})]}),m=()=>(0,r.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(t,{height:"2.5rem",width:"9rem",className:"mb-2"}),(0,r.jsx)(t,{height:"1.25rem",width:"18rem"})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)(t,{variant:"rectangular",height:"2.5rem",width:"8rem"}),(0,r.jsx)(t,{variant:"rectangular",height:"2.5rem",width:"6rem"})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:Array.from({length:3}).map((e,s)=>(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsx)(t,{height:"1.25rem",width:"6rem",className:"mb-4"}),(0,r.jsx)(t,{height:"2.5rem",width:"5rem",className:"mb-2"}),(0,r.jsx)(t,{height:"1rem",width:"8rem"})]},s))}),(0,r.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsx)(t,{height:"1.5rem",width:"12rem",className:"mb-6"}),(0,r.jsx)(t,{variant:"rectangular",height:"24rem"})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsx)(t,{height:"1.5rem",width:"10rem",className:"mb-6"}),(0,r.jsx)(t,{variant:"rectangular",height:"24rem"})]})]})]})},95803:(e,s,a)=>{a.d(s,{A:()=>l});var r=a(95155),t=a(12115),i=a(76032);function l(e){let{text:s,className:a="",size:l="sm",variant:n="default",title:d="Copy to clipboard"}=e,[o,c]=(0,t.useState)(!1),m=async()=>{try{await navigator.clipboard.writeText(s),c(!0),setTimeout(()=>c(!1),2e3)}catch(a){let e=document.createElement("textarea");e.value=s,document.body.appendChild(e),e.focus(),e.select();try{document.execCommand("copy"),c(!0),setTimeout(()=>c(!1),2e3)}catch(e){}document.body.removeChild(e)}},x={sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6"};return(0,r.jsx)("button",{onClick:m,className:"\n        ".concat({sm:"p-1.5",md:"p-2",lg:"p-2.5"}[l],"\n        ").concat({default:"text-gray-500 hover:text-gray-700 hover:bg-gray-100/80",code:"text-gray-300 hover:text-white hover:bg-gray-600/80",message:"text-gray-500 hover:text-gray-700 hover:bg-white/20"}[n],"\n        rounded transition-all duration-200 cursor-pointer\n        ").concat(o?"text-green-600":"","\n        ").concat(a,"\n      "),title:o?"Copied!":d,children:o?(0,r.jsx)(i.S,{className:"".concat(x[l]," stroke-2")}):(0,r.jsx)(i.X,{className:"".concat(x[l]," stroke-2")})})}},99030:(e,s,a)=>{a.d(s,{default:()=>i});var r=a(12115),t=a(34962);function i(){let{pageTitle:e}=(0,t.rT)();return(0,r.useEffect)(()=>{"undefined"!=typeof document&&(document.title=e)},[e]),null}}}]);