(()=>{var e={};e.id=9890,e.ids=[9890],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},22614:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>_,serverHooks:()=>f,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{POST:()=>l});var i=t(96559),o=t(48088),n=t(37719),a=t(32190),u=t(64745),p=t(39398);let c=new u.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2025-02-24.acacia"}),d=(0,p.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function l(e){try{let r,{priceId:t,userId:s,userEmail:i,tier:o,signup:n,pendingUserData:u}=await e.json();if(n){if(!t||!i||!o||!u)return a.NextResponse.json({error:"Missing required fields for signup: priceId, userEmail, tier, pendingUserData"},{status:400})}else if(!t||!s||!i||!o)return a.NextResponse.json({error:"Missing required fields: priceId, userId, userEmail, tier"},{status:400});if(!["starter","professional","enterprise"].includes(o))return a.NextResponse.json({error:"Invalid tier. Must be starter, professional, or enterprise"},{status:400});let p=function(e){switch(e){case"starter":return process.env.STRIPE_STARTER_PRICE_ID;case"professional":return process.env.STRIPE_PROFESSIONAL_PRICE_ID;case"enterprise":return process.env.STRIPE_ENTERPRISE_PRICE_ID;default:throw Error(`Invalid tier: ${e}`)}}(o);if(t!==p)return a.NextResponse.json({error:"Price ID does not match selected tier"},{status:400});if(!n&&s){let{data:e}=await d.from("subscriptions").select("*").eq("user_id",s).eq("status","active").single();if(e)return a.NextResponse.json({error:"User already has an active subscription"},{status:400})}let l=await c.customers.list({email:i,limit:1}),_={customer:(l.data.length>0?l.data[0]:await c.customers.create({email:i,metadata:{user_id:s}})).id,payment_method_types:["card"],line_items:[{price:t,quantity:1}],mode:"subscription",success_url:`https://roukey.online/auth/callback?session_id={CHECKOUT_SESSION_ID}&payment_success=true&redirectTo=${encodeURIComponent("/dashboard")}`,cancel_url:`https://roukey.online/pricing?plan=${o}&payment_cancelled=true`,metadata:{user_id:s||"pending_signup",tier:o,signup:n?"true":"false",pending_user_data:n?JSON.stringify(u):void 0},subscription_data:{metadata:{user_id:s||"pending_signup",tier:o,signup:n?"true":"false",pending_user_data:n?JSON.stringify(u):void 0}},allow_promotion_codes:!0,billing_address_collection:"required",customer_update:{address:"auto",name:"auto"}},x=await c.checkout.sessions.create(_);return a.NextResponse.json({sessionId:x.id,url:x.url})}catch(e){if(e instanceof u.A.errors.StripeError)return a.NextResponse.json({error:`Stripe error: ${e.message}`},{status:400});return a.NextResponse.json({error:"Internal server error"},{status:500})}}let _=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/stripe/create-checkout-session/route",pathname:"/api/stripe/create-checkout-session",filename:"route",bundlePath:"app/api/stripe/create-checkout-session/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\stripe\\create-checkout-session\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:m,serverHooks:f}=_;function h(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:m})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,580,9398,4745],()=>t(22614));module.exports=s})();