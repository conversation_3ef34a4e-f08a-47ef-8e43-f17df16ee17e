(()=>{var e={};e.id=3084,e.ids=[1489,3084,8108],e.modules={339:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function i(){}function a(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function n(e,t,i,n,s){if("function"!=typeof i)throw TypeError("The listener must be a function");var o=new a(i,n||e,s),l=r?r+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],o]:e._events[l].push(o):(e._events[l]=o,e._eventsCount++),e}function s(e,t){0==--e._eventsCount?e._events=new i:delete e._events[t]}function o(){this._events=new i,this._eventsCount=0}Object.create&&(i.prototype=Object.create(null),new i().__proto__||(r=!1)),o.prototype.eventNames=function(){var e,i,a=[];if(0===this._eventsCount)return a;for(i in e=this._events)t.call(e,i)&&a.push(r?i.slice(1):i);return Object.getOwnPropertySymbols?a.concat(Object.getOwnPropertySymbols(e)):a},o.prototype.listeners=function(e){var t=r?r+e:e,i=this._events[t];if(!i)return[];if(i.fn)return[i.fn];for(var a=0,n=i.length,s=Array(n);a<n;a++)s[a]=i[a].fn;return s},o.prototype.listenerCount=function(e){var t=r?r+e:e,i=this._events[t];return i?i.fn?1:i.length:0},o.prototype.emit=function(e,t,i,a,n,s){var o=r?r+e:e;if(!this._events[o])return!1;var l,u,c=this._events[o],d=arguments.length;if(c.fn){switch(c.once&&this.removeListener(e,c.fn,void 0,!0),d){case 1:return c.fn.call(c.context),!0;case 2:return c.fn.call(c.context,t),!0;case 3:return c.fn.call(c.context,t,i),!0;case 4:return c.fn.call(c.context,t,i,a),!0;case 5:return c.fn.call(c.context,t,i,a,n),!0;case 6:return c.fn.call(c.context,t,i,a,n,s),!0}for(u=1,l=Array(d-1);u<d;u++)l[u-1]=arguments[u];c.fn.apply(c.context,l)}else{var h,p=c.length;for(u=0;u<p;u++)switch(c[u].once&&this.removeListener(e,c[u].fn,void 0,!0),d){case 1:c[u].fn.call(c[u].context);break;case 2:c[u].fn.call(c[u].context,t);break;case 3:c[u].fn.call(c[u].context,t,i);break;case 4:c[u].fn.call(c[u].context,t,i,a);break;default:if(!l)for(h=1,l=Array(d-1);h<d;h++)l[h-1]=arguments[h];c[u].fn.apply(c[u].context,l)}}return!0},o.prototype.on=function(e,t,r){return n(this,e,t,r,!1)},o.prototype.once=function(e,t,r){return n(this,e,t,r,!0)},o.prototype.removeListener=function(e,t,i,a){var n=r?r+e:e;if(!this._events[n])return this;if(!t)return s(this,n),this;var o=this._events[n];if(o.fn)o.fn!==t||a&&!o.once||i&&o.context!==i||s(this,n);else{for(var l=0,u=[],c=o.length;l<c;l++)(o[l].fn!==t||a&&!o[l].once||i&&o[l].context!==i)&&u.push(o[l]);u.length?this._events[n]=1===u.length?u[0]:u:s(this,n)}return this},o.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&s(this,t)):(this._events=new i,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=r,o.EventEmitter=o,e.exports=o},2507:(e,t,r)=>{"use strict";r.d(t,{Q:()=>s,createSupabaseServerClientOnRequest:()=>n});var i=r(34386),a=r(44999);async function n(){let e=await (0,a.UL)();return(0,i.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,i){try{e.set({name:t,value:r,...i})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}function s(e){return(0,i.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}},2843:e=>{"use strict";class t{constructor(){this.max=1e3,this.map=new Map}get(e){let t=this.map.get(e);if(void 0!==t)return this.map.delete(e),this.map.set(e,t),t}delete(e){return this.map.delete(e)}set(e,t){if(!this.delete(e)&&void 0!==t){if(this.map.size>=this.max){let e=this.map.keys().next().value;this.delete(e)}this.map.set(e,t)}return this}}e.exports=t},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3706:(e,t,r)=>{"use strict";let i=/\s+/g;class a{constructor(e,t){if(t=s(t),e instanceof a)if(!!t.loose===e.loose&&!!t.includePrerelease===e.includePrerelease)return e;else return new a(e.raw,t);if(e instanceof o)return this.raw=e.value,this.set=[[e]],this.formatted=void 0,this;if(this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease,this.raw=e.trim().replace(i," "),this.set=this.raw.split("||").map(e=>this.parseRange(e.trim())).filter(e=>e.length),!this.set.length)throw TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){let e=this.set[0];if(this.set=this.set.filter(e=>!b(e[0])),0===this.set.length)this.set=[e];else if(this.set.length>1){for(let e of this.set)if(1===e.length&&y(e[0])){this.set=[e];break}}}this.formatted=void 0}get range(){if(void 0===this.formatted){this.formatted="";for(let e=0;e<this.set.length;e++){e>0&&(this.formatted+="||");let t=this.set[e];for(let e=0;e<t.length;e++)e>0&&(this.formatted+=" "),this.formatted+=t[e].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(e){let t=((this.options.includePrerelease&&m)|(this.options.loose&&g))+":"+e,r=n.get(t);if(r)return r;let i=this.options.loose,a=i?c[d.HYPHENRANGELOOSE]:c[d.HYPHENRANGE];l("hyphen replace",e=e.replace(a,R(this.options.includePrerelease))),l("comparator trim",e=e.replace(c[d.COMPARATORTRIM],h)),l("tilde trim",e=e.replace(c[d.TILDETRIM],p)),l("caret trim",e=e.replace(c[d.CARETTRIM],f));let s=e.split(" ").map(e=>w(e,this.options)).join(" ").split(/\s+/).map(e=>A(e,this.options));i&&(s=s.filter(e=>(l("loose invalid filter",e,this.options),!!e.match(c[d.COMPARATORLOOSE])))),l("range list",s);let u=new Map;for(let e of s.map(e=>new o(e,this.options))){if(b(e))return[e];u.set(e.value,e)}u.size>1&&u.has("")&&u.delete("");let y=[...u.values()];return n.set(t,y),y}intersects(e,t){if(!(e instanceof a))throw TypeError("a Range is required");return this.set.some(r=>v(r,t)&&e.set.some(e=>v(e,t)&&r.every(r=>e.every(e=>r.intersects(e,t)))))}test(e){if(!e)return!1;if("string"==typeof e)try{e=new u(e,this.options)}catch(e){return!1}for(let t=0;t<this.set.length;t++)if(j(this.set[t],e,this.options))return!0;return!1}}e.exports=a;let n=new(r(2843)),s=r(98300),o=r(14239),l=r(38267),u=r(64487),{safeRe:c,t:d,comparatorTrimReplace:h,tildeTrimReplace:p,caretTrimReplace:f}=r(26515),{FLAG_INCLUDE_PRERELEASE:m,FLAG_LOOSE:g}=r(32397),b=e=>"<0.0.0-0"===e.value,y=e=>""===e.value,v=(e,t)=>{let r=!0,i=e.slice(),a=i.pop();for(;r&&i.length;)r=i.every(e=>a.intersects(e,t)),a=i.pop();return r},w=(e,t)=>(l("comp",e,t),l("caret",e=I(e,t)),l("tildes",e=E(e,t)),l("xrange",e=T(e,t)),l("stars",e=$(e,t)),e),_=e=>!e||"x"===e.toLowerCase()||"*"===e,E=(e,t)=>e.trim().split(/\s+/).map(e=>O(e,t)).join(" "),O=(e,t)=>{let r=t.loose?c[d.TILDELOOSE]:c[d.TILDE];return e.replace(r,(t,r,i,a,n)=>{let s;return l("tilde",e,t,r,i,a,n),_(r)?s="":_(i)?s=`>=${r}.0.0 <${+r+1}.0.0-0`:_(a)?s=`>=${r}.${i}.0 <${r}.${+i+1}.0-0`:n?(l("replaceTilde pr",n),s=`>=${r}.${i}.${a}-${n} <${r}.${+i+1}.0-0`):s=`>=${r}.${i}.${a} <${r}.${+i+1}.0-0`,l("tilde return",s),s})},I=(e,t)=>e.trim().split(/\s+/).map(e=>S(e,t)).join(" "),S=(e,t)=>{l("caret",e,t);let r=t.loose?c[d.CARETLOOSE]:c[d.CARET],i=t.includePrerelease?"-0":"";return e.replace(r,(t,r,a,n,s)=>{let o;return l("caret",e,t,r,a,n,s),_(r)?o="":_(a)?o=`>=${r}.0.0${i} <${+r+1}.0.0-0`:_(n)?o="0"===r?`>=${r}.${a}.0${i} <${r}.${+a+1}.0-0`:`>=${r}.${a}.0${i} <${+r+1}.0.0-0`:s?(l("replaceCaret pr",s),o="0"===r?"0"===a?`>=${r}.${a}.${n}-${s} <${r}.${a}.${+n+1}-0`:`>=${r}.${a}.${n}-${s} <${r}.${+a+1}.0-0`:`>=${r}.${a}.${n}-${s} <${+r+1}.0.0-0`):(l("no pr"),o="0"===r?"0"===a?`>=${r}.${a}.${n}${i} <${r}.${a}.${+n+1}-0`:`>=${r}.${a}.${n}${i} <${r}.${+a+1}.0-0`:`>=${r}.${a}.${n} <${+r+1}.0.0-0`),l("caret return",o),o})},T=(e,t)=>(l("replaceXRanges",e,t),e.split(/\s+/).map(e=>P(e,t)).join(" ")),P=(e,t)=>{e=e.trim();let r=t.loose?c[d.XRANGELOOSE]:c[d.XRANGE];return e.replace(r,(r,i,a,n,s,o)=>{l("xRange",e,r,i,a,n,s,o);let u=_(a),c=u||_(n),d=c||_(s);return"="===i&&d&&(i=""),o=t.includePrerelease?"-0":"",u?r=">"===i||"<"===i?"<0.0.0-0":"*":i&&d?(c&&(n=0),s=0,">"===i?(i=">=",c?(a=+a+1,n=0):n=+n+1,s=0):"<="===i&&(i="<",c?a=+a+1:n=+n+1),"<"===i&&(o="-0"),r=`${i+a}.${n}.${s}${o}`):c?r=`>=${a}.0.0${o} <${+a+1}.0.0-0`:d&&(r=`>=${a}.${n}.0${o} <${a}.${+n+1}.0-0`),l("xRange return",r),r})},$=(e,t)=>(l("replaceStars",e,t),e.trim().replace(c[d.STAR],"")),A=(e,t)=>(l("replaceGTE0",e,t),e.trim().replace(c[t.includePrerelease?d.GTE0PRE:d.GTE0],"")),R=e=>(t,r,i,a,n,s,o,l,u,c,d,h)=>(r=_(i)?"":_(a)?`>=${i}.0.0${e?"-0":""}`:_(n)?`>=${i}.${a}.0${e?"-0":""}`:s?`>=${r}`:`>=${r}${e?"-0":""}`,l=_(u)?"":_(c)?`<${+u+1}.0.0-0`:_(d)?`<${u}.${+c+1}.0-0`:h?`<=${u}.${c}.${d}-${h}`:e?`<${u}.${c}.${+d+1}-0`:`<=${l}`,`${r} ${l}`.trim()),j=(e,t,r)=>{for(let r=0;r<e.length;r++)if(!e[r].test(t))return!1;if(t.prerelease.length&&!r.includePrerelease){for(let r=0;r<e.length;r++)if(l(e[r].semver),e[r].semver!==o.ANY&&e[r].semver.prerelease.length>0){let i=e[r].semver;if(i.major===t.major&&i.minor===t.minor&&i.patch===t.patch)return!0}return!1}return!0}},7110:(e,t,r)=>{"use strict";let i=r(58361);e.exports=(e,t)=>{let r=i(e,t);return r&&r.prerelease.length?r.prerelease:null}},8536:(e,t,r)=>{"use strict";let i=r(24800);e.exports=(e,t)=>e.sort((e,r)=>i(r,e,t))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11337:(e,t,r)=>{"use strict";let i=r(3706),a=r(14239),{ANY:n}=a,s=r(42679),o=r(33877),l=[new a(">=0.0.0-0")],u=[new a(">=0.0.0")],c=(e,t,r)=>{let i,a,c,p,f,m,g;if(e===t)return!0;if(1===e.length&&e[0].semver===n)if(1===t.length&&t[0].semver===n)return!0;else e=r.includePrerelease?l:u;if(1===t.length&&t[0].semver===n)if(r.includePrerelease)return!0;else t=u;let b=new Set;for(let t of e)">"===t.operator||">="===t.operator?i=d(i,t,r):"<"===t.operator||"<="===t.operator?a=h(a,t,r):b.add(t.semver);if(b.size>1)return null;if(i&&a&&((c=o(i.semver,a.semver,r))>0||0===c&&(">="!==i.operator||"<="!==a.operator)))return null;for(let e of b){if(i&&!s(e,String(i),r)||a&&!s(e,String(a),r))return null;for(let i of t)if(!s(e,String(i),r))return!1;return!0}let y=!!a&&!r.includePrerelease&&!!a.semver.prerelease.length&&a.semver,v=!!i&&!r.includePrerelease&&!!i.semver.prerelease.length&&i.semver;for(let e of(y&&1===y.prerelease.length&&"<"===a.operator&&0===y.prerelease[0]&&(y=!1),t)){if(g=g||">"===e.operator||">="===e.operator,m=m||"<"===e.operator||"<="===e.operator,i){if(v&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===v.major&&e.semver.minor===v.minor&&e.semver.patch===v.patch&&(v=!1),">"===e.operator||">="===e.operator){if((p=d(i,e,r))===e&&p!==i)return!1}else if(">="===i.operator&&!s(i.semver,String(e),r))return!1}if(a){if(y&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===y.major&&e.semver.minor===y.minor&&e.semver.patch===y.patch&&(y=!1),"<"===e.operator||"<="===e.operator){if((f=h(a,e,r))===e&&f!==a)return!1}else if("<="===a.operator&&!s(a.semver,String(e),r))return!1}if(!e.operator&&(a||i)&&0!==c)return!1}return(!i||!m||!!a||0===c)&&(!a||!g||!!i||0===c)&&!v&&!y&&!0},d=(e,t,r)=>{if(!e)return t;let i=o(e.semver,t.semver,r);return i>0?e:i<0||">"===t.operator&&">="===e.operator?t:e},h=(e,t,r)=>{if(!e)return t;let i=o(e.semver,t.semver,r);return i<0?e:i>0||"<"===t.operator&&"<="===e.operator?t:e};e.exports=(e,t,r={})=>{if(e===t)return!0;e=new i(e,r),t=new i(t,r);let a=!1;e:for(let i of e.set){for(let e of t.set){let t=c(i,e,r);if(a=a||null!==t,t)continue e}if(a)return!1}return!0}},11997:e=>{"use strict";e.exports=require("punycode")},12441:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let i=r(71611);class a{constructor(){this._queue=[]}enqueue(e,t){let r={priority:(t=Object.assign({priority:0},t)).priority,run:e};if(this.size&&this._queue[this.size-1].priority>=t.priority)return void this._queue.push(r);let a=i.default(this._queue,r,(e,t)=>t.priority-e.priority);this._queue.splice(a,0,r)}dequeue(){let e=this._queue.shift();return null==e?void 0:e.run}filter(e){return this._queue.filter(t=>t.priority===e.priority).map(e=>e.run)}get size(){return this._queue.length}}t.default=a},14239:(e,t,r)=>{"use strict";let i=Symbol("SemVer ANY");class a{static get ANY(){return i}constructor(e,t){if(t=n(t),e instanceof a)if(!!t.loose===e.loose)return e;else e=e.value;u("comparator",e=e.trim().split(/\s+/).join(" "),t),this.options=t,this.loose=!!t.loose,this.parse(e),this.semver===i?this.value="":this.value=this.operator+this.semver.version,u("comp",this)}parse(e){let t=this.options.loose?s[o.COMPARATORLOOSE]:s[o.COMPARATOR],r=e.match(t);if(!r)throw TypeError(`Invalid comparator: ${e}`);this.operator=void 0!==r[1]?r[1]:"","="===this.operator&&(this.operator=""),r[2]?this.semver=new c(r[2],this.options.loose):this.semver=i}toString(){return this.value}test(e){if(u("Comparator.test",e,this.options.loose),this.semver===i||e===i)return!0;if("string"==typeof e)try{e=new c(e,this.options)}catch(e){return!1}return l(e,this.operator,this.semver,this.options)}intersects(e,t){if(!(e instanceof a))throw TypeError("a Comparator is required");return""===this.operator?""===this.value||new d(e.value,t).test(this.value):""===e.operator?""===e.value||new d(this.value,t).test(e.semver):!((t=n(t)).includePrerelease&&("<0.0.0-0"===this.value||"<0.0.0-0"===e.value)||!t.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0")))&&!!(this.operator.startsWith(">")&&e.operator.startsWith(">")||this.operator.startsWith("<")&&e.operator.startsWith("<")||this.semver.version===e.semver.version&&this.operator.includes("=")&&e.operator.includes("=")||l(this.semver,"<",e.semver,t)&&this.operator.startsWith(">")&&e.operator.startsWith("<")||l(this.semver,">",e.semver,t)&&this.operator.startsWith("<")&&e.operator.startsWith(">"))}}e.exports=a;let n=r(98300),{safeRe:s,t:o}=r(26515),l=r(84450),u=r(38267),c=r(64487),d=r(3706)},17950:(e,t,r)=>{"use strict";let i=r(33877);e.exports=(e,t)=>i(e,t,!0)},18929:(e,t)=>{"use strict";t.byteLength=function(e){var t=l(e),r=t[0],i=t[1];return(r+i)*3/4-i},t.toByteArray=function(e){var t,r,n=l(e),s=n[0],o=n[1],u=new a((s+o)*3/4-o),c=0,d=o>0?s-4:s;for(r=0;r<d;r+=4)t=i[e.charCodeAt(r)]<<18|i[e.charCodeAt(r+1)]<<12|i[e.charCodeAt(r+2)]<<6|i[e.charCodeAt(r+3)],u[c++]=t>>16&255,u[c++]=t>>8&255,u[c++]=255&t;return 2===o&&(t=i[e.charCodeAt(r)]<<2|i[e.charCodeAt(r+1)]>>4,u[c++]=255&t),1===o&&(t=i[e.charCodeAt(r)]<<10|i[e.charCodeAt(r+1)]<<4|i[e.charCodeAt(r+2)]>>2,u[c++]=t>>8&255,u[c++]=255&t),u},t.fromByteArray=function(e){for(var t,i=e.length,a=i%3,n=[],s=0,o=i-a;s<o;s+=16383)n.push(function(e,t,i){for(var a,n=[],s=t;s<i;s+=3)a=(e[s]<<16&0xff0000)+(e[s+1]<<8&65280)+(255&e[s+2]),n.push(r[a>>18&63]+r[a>>12&63]+r[a>>6&63]+r[63&a]);return n.join("")}(e,s,s+16383>o?o:s+16383));return 1===a?n.push(r[(t=e[i-1])>>2]+r[t<<4&63]+"=="):2===a&&n.push(r[(t=(e[i-2]<<8)+e[i-1])>>10]+r[t>>4&63]+r[t<<2&63]+"="),n.join("")};for(var r=[],i=[],a="undefined"!=typeof Uint8Array?Uint8Array:Array,n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,o=n.length;s<o;++s)r[s]=n[s],i[n.charCodeAt(s)]=s;function l(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var i=r===t?0:4-r%4;return[r,i]}i[45]=62,i[95]=63},20938:(e,t,r)=>{"use strict";let i=r(3706);e.exports=(e,t)=>new i(e,t).set.map(e=>e.map(e=>e.value).join(" ").trim().split(" "))},22893:(e,t,r)=>{"use strict";let i=r(43528);e.exports=(e,t,r)=>i(e,t,"<",r)},23518:(e,t,r)=>{e.exports=r(55332)},24303:(e,t,r)=>{"use strict";let i=r(64487),a=r(3706);e.exports=(e,t,r)=>{let n=null,s=null,o=null;try{o=new a(t,r)}catch(e){return null}return e.forEach(e=>{o.test(e)&&(!n||1===s.compare(e))&&(s=new i(n=e,r))}),n}},24800:(e,t,r)=>{"use strict";let i=r(64487);e.exports=(e,t,r)=>{let a=new i(e,r),n=new i(t,r);return a.compare(n)||a.compareBuild(n)}},25706:e=>{"use strict";e.exports=(e,t)=>(t=t||(()=>{}),e.then(e=>new Promise(e=>{e(t())}).then(()=>e),e=>new Promise(e=>{e(t())}).then(()=>{throw e})))},26515:(e,t,r)=>{"use strict";let{MAX_SAFE_COMPONENT_LENGTH:i,MAX_SAFE_BUILD_LENGTH:a,MAX_LENGTH:n}=r(32397),s=r(38267),o=(t=e.exports={}).re=[],l=t.safeRe=[],u=t.src=[],c=t.safeSrc=[],d=t.t={},h=0,p="[a-zA-Z0-9-]",f=[["\\s",1],["\\d",n],[p,a]],m=e=>{for(let[t,r]of f)e=e.split(`${t}*`).join(`${t}{0,${r}}`).split(`${t}+`).join(`${t}{1,${r}}`);return e},g=(e,t,r)=>{let i=m(t),a=h++;s(e,a,t),d[e]=a,u[a]=t,c[a]=i,o[a]=new RegExp(t,r?"g":void 0),l[a]=new RegExp(i,r?"g":void 0)};g("NUMERICIDENTIFIER","0|[1-9]\\d*"),g("NUMERICIDENTIFIERLOOSE","\\d+"),g("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${p}*`),g("MAINVERSION",`(${u[d.NUMERICIDENTIFIER]})\\.(${u[d.NUMERICIDENTIFIER]})\\.(${u[d.NUMERICIDENTIFIER]})`),g("MAINVERSIONLOOSE",`(${u[d.NUMERICIDENTIFIERLOOSE]})\\.(${u[d.NUMERICIDENTIFIERLOOSE]})\\.(${u[d.NUMERICIDENTIFIERLOOSE]})`),g("PRERELEASEIDENTIFIER",`(?:${u[d.NONNUMERICIDENTIFIER]}|${u[d.NUMERICIDENTIFIER]})`),g("PRERELEASEIDENTIFIERLOOSE",`(?:${u[d.NONNUMERICIDENTIFIER]}|${u[d.NUMERICIDENTIFIERLOOSE]})`),g("PRERELEASE",`(?:-(${u[d.PRERELEASEIDENTIFIER]}(?:\\.${u[d.PRERELEASEIDENTIFIER]})*))`),g("PRERELEASELOOSE",`(?:-?(${u[d.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${u[d.PRERELEASEIDENTIFIERLOOSE]})*))`),g("BUILDIDENTIFIER",`${p}+`),g("BUILD",`(?:\\+(${u[d.BUILDIDENTIFIER]}(?:\\.${u[d.BUILDIDENTIFIER]})*))`),g("FULLPLAIN",`v?${u[d.MAINVERSION]}${u[d.PRERELEASE]}?${u[d.BUILD]}?`),g("FULL",`^${u[d.FULLPLAIN]}$`),g("LOOSEPLAIN",`[v=\\s]*${u[d.MAINVERSIONLOOSE]}${u[d.PRERELEASELOOSE]}?${u[d.BUILD]}?`),g("LOOSE",`^${u[d.LOOSEPLAIN]}$`),g("GTLT","((?:<|>)?=?)"),g("XRANGEIDENTIFIERLOOSE",`${u[d.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),g("XRANGEIDENTIFIER",`${u[d.NUMERICIDENTIFIER]}|x|X|\\*`),g("XRANGEPLAIN",`[v=\\s]*(${u[d.XRANGEIDENTIFIER]})(?:\\.(${u[d.XRANGEIDENTIFIER]})(?:\\.(${u[d.XRANGEIDENTIFIER]})(?:${u[d.PRERELEASE]})?${u[d.BUILD]}?)?)?`),g("XRANGEPLAINLOOSE",`[v=\\s]*(${u[d.XRANGEIDENTIFIERLOOSE]})(?:\\.(${u[d.XRANGEIDENTIFIERLOOSE]})(?:\\.(${u[d.XRANGEIDENTIFIERLOOSE]})(?:${u[d.PRERELEASELOOSE]})?${u[d.BUILD]}?)?)?`),g("XRANGE",`^${u[d.GTLT]}\\s*${u[d.XRANGEPLAIN]}$`),g("XRANGELOOSE",`^${u[d.GTLT]}\\s*${u[d.XRANGEPLAINLOOSE]}$`),g("COERCEPLAIN",`(^|[^\\d])(\\d{1,${i}})(?:\\.(\\d{1,${i}}))?(?:\\.(\\d{1,${i}}))?`),g("COERCE",`${u[d.COERCEPLAIN]}(?:$|[^\\d])`),g("COERCEFULL",u[d.COERCEPLAIN]+`(?:${u[d.PRERELEASE]})?`+`(?:${u[d.BUILD]})?`+"(?:$|[^\\d])"),g("COERCERTL",u[d.COERCE],!0),g("COERCERTLFULL",u[d.COERCEFULL],!0),g("LONETILDE","(?:~>?)"),g("TILDETRIM",`(\\s*)${u[d.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",g("TILDE",`^${u[d.LONETILDE]}${u[d.XRANGEPLAIN]}$`),g("TILDELOOSE",`^${u[d.LONETILDE]}${u[d.XRANGEPLAINLOOSE]}$`),g("LONECARET","(?:\\^)"),g("CARETTRIM",`(\\s*)${u[d.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",g("CARET",`^${u[d.LONECARET]}${u[d.XRANGEPLAIN]}$`),g("CARETLOOSE",`^${u[d.LONECARET]}${u[d.XRANGEPLAINLOOSE]}$`),g("COMPARATORLOOSE",`^${u[d.GTLT]}\\s*(${u[d.LOOSEPLAIN]})$|^$`),g("COMPARATOR",`^${u[d.GTLT]}\\s*(${u[d.FULLPLAIN]})$|^$`),g("COMPARATORTRIM",`(\\s*)${u[d.GTLT]}\\s*(${u[d.LOOSEPLAIN]}|${u[d.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",g("HYPHENRANGE",`^\\s*(${u[d.XRANGEPLAIN]})\\s+-\\s+(${u[d.XRANGEPLAIN]})\\s*$`),g("HYPHENRANGELOOSE",`^\\s*(${u[d.XRANGEPLAINLOOSE]})\\s+-\\s+(${u[d.XRANGEPLAINLOOSE]})\\s*$`),g("STAR","(<|>)?=?\\s*\\*"),g("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),g("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")},27290:(e,t,r)=>{"use strict";let i=r(33877);e.exports=(e,t,r)=>0!==i(e,t,r)},27910:e=>{"use strict";e.exports=require("stream")},28584:(e,t,r)=>{"use strict";let i=r(26515),a=r(32397),n=r(64487),s=r(78668),o=r(58361),l=r(35444),u=r(73051),c=r(90726),d=r(93419),h=r(42467),p=r(40999),f=r(78172),m=r(7110),g=r(33877),b=r(86605),y=r(17950),v=r(24800),w=r(31904),_=r(8536),E=r(42699),O=r(40720),I=r(73438),S=r(27290),T=r(44156),P=r(60301),$=r(84450),A=r(44449),R=r(14239),j=r(3706),x=r(42679),k=r(20938),N=r(43441),C=r(24303),L=r(36686),M=r(31385),D=r(43528),U=r(43900),F=r(22893),H=r(71505);e.exports={parse:o,valid:l,clean:u,inc:c,diff:d,major:h,minor:p,patch:f,prerelease:m,compare:g,rcompare:b,compareLoose:y,compareBuild:v,sort:w,rsort:_,gt:E,lt:O,eq:I,neq:S,gte:T,lte:P,cmp:$,coerce:A,Comparator:R,Range:j,satisfies:x,toComparators:k,maxSatisfying:N,minSatisfying:C,minVersion:L,validRange:M,outside:D,gtr:U,ltr:F,intersects:H,simplifyRange:r(77860),subset:r(11337),SemVer:n,re:i.re,src:i.src,tokens:i.t,SEMVER_SPEC_VERSION:a.SEMVER_SPEC_VERSION,RELEASE_TYPES:a.RELEASE_TYPES,compareIdentifiers:s.compareIdentifiers,rcompareIdentifiers:s.rcompareIdentifiers}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31385:(e,t,r)=>{"use strict";let i=r(3706);e.exports=(e,t)=>{try{return new i(e,t).range||"*"}catch(e){return null}}},31904:(e,t,r)=>{"use strict";let i=r(24800);e.exports=(e,t)=>e.sort((e,r)=>i(e,r,t))},32397:e=>{"use strict";e.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:16,MAX_SAFE_BUILD_LENGTH:250,MAX_SAFE_INTEGER:Number.MAX_SAFE_INTEGER||0x1fffffffffffff,RELEASE_TYPES:["major","premajor","minor","preminor","patch","prepatch","prerelease"],SEMVER_SPEC_VERSION:"2.0.0",FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}},33873:e=>{"use strict";e.exports=require("path")},33877:(e,t,r)=>{"use strict";let i=r(64487);e.exports=(e,t,r)=>new i(e,r).compare(new i(t,r))},34631:e=>{"use strict";e.exports=require("tls")},35444:(e,t,r)=>{"use strict";let i=r(58361);e.exports=(e,t)=>{let r=i(e,t);return r?r.version:null}},36686:(e,t,r)=>{"use strict";let i=r(64487),a=r(3706),n=r(42699);e.exports=(e,t)=>{e=new a(e,t);let r=new i("0.0.0");if(e.test(r)||(r=new i("0.0.0-0"),e.test(r)))return r;r=null;for(let t=0;t<e.set.length;++t){let a=e.set[t],s=null;a.forEach(e=>{let t=new i(e.semver.version);switch(e.operator){case">":0===t.prerelease.length?t.patch++:t.prerelease.push(0),t.raw=t.format();case"":case">=":(!s||n(t,s))&&(s=t);break;case"<":case"<=":break;default:throw Error(`Unexpected operation: ${e.operator}`)}}),s&&(!r||n(r,s))&&(r=s)}return r&&e.test(r)?r:null}},38267:e=>{"use strict";e.exports="object"==typeof process&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{}},39727:()=>{},40720:(e,t,r)=>{"use strict";let i=r(33877);e.exports=(e,t,r)=>0>i(e,t,r)},40999:(e,t,r)=>{"use strict";let i=r(64487);e.exports=(e,t)=>new i(e,t).minor},42467:(e,t,r)=>{"use strict";let i=r(64487);e.exports=(e,t)=>new i(e,t).major},42679:(e,t,r)=>{"use strict";let i=r(3706);e.exports=(e,t,r)=>{try{t=new i(t,r)}catch(e){return!1}return t.test(e)}},42699:(e,t,r)=>{"use strict";let i=r(33877);e.exports=(e,t,r)=>i(e,t,r)>0},43441:(e,t,r)=>{"use strict";let i=r(64487),a=r(3706);e.exports=(e,t,r)=>{let n=null,s=null,o=null;try{o=new a(t,r)}catch(e){return null}return e.forEach(e=>{o.test(e)&&(!n||-1===s.compare(e))&&(s=new i(n=e,r))}),n}},43528:(e,t,r)=>{"use strict";let i=r(64487),a=r(14239),{ANY:n}=a,s=r(3706),o=r(42679),l=r(42699),u=r(40720),c=r(60301),d=r(44156);e.exports=(e,t,r,h)=>{let p,f,m,g,b;switch(e=new i(e,h),t=new s(t,h),r){case">":p=l,f=c,m=u,g=">",b=">=";break;case"<":p=u,f=d,m=l,g="<",b="<=";break;default:throw TypeError('Must provide a hilo val of "<" or ">"')}if(o(e,t,h))return!1;for(let r=0;r<t.set.length;++r){let i=t.set[r],s=null,o=null;if(i.forEach(e=>{e.semver===n&&(e=new a(">=0.0.0")),s=s||e,o=o||e,p(e.semver,s.semver,h)?s=e:m(e.semver,o.semver,h)&&(o=e)}),s.operator===g||s.operator===b||(!o.operator||o.operator===g)&&f(e,o.semver)||o.operator===b&&m(e,o.semver))return!1}return!0}},43900:(e,t,r)=>{"use strict";let i=r(43528);e.exports=(e,t,r)=>i(e,t,">",r)},44156:(e,t,r)=>{"use strict";let i=r(33877);e.exports=(e,t,r)=>i(e,t,r)>=0},44449:(e,t,r)=>{"use strict";let i=r(64487),a=r(58361),{safeRe:n,t:s}=r(26515);e.exports=(e,t)=>{if(e instanceof i)return e;if("number"==typeof e&&(e=String(e)),"string"!=typeof e)return null;let r=null;if((t=t||{}).rtl){let i,a=t.includePrerelease?n[s.COERCERTLFULL]:n[s.COERCERTL];for(;(i=a.exec(e))&&(!r||r.index+r[0].length!==e.length);)r&&i.index+i[0].length===r.index+r[0].length||(r=i),a.lastIndex=i.index+i[1].length+i[2].length;a.lastIndex=-1}else r=e.match(t.includePrerelease?n[s.COERCEFULL]:n[s.COERCE]);if(null===r)return null;let o=r[2],l=r[3]||"0",u=r[4]||"0",c=t.includePrerelease&&r[5]?`-${r[5]}`:"",d=t.includePrerelease&&r[6]?`+${r[6]}`:"";return a(`${o}.${l}.${u}${c}${d}`,t)}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51191:(e,t,r)=>{"use strict";let i,a,n,s,o,l;r.r(t),r.d(t,{patchFetch:()=>ir,routeModule:()=>r8,serverHooks:()=>it,workAsyncStorage:()=>r7,workUnitAsyncStorage:()=>ie});var u={};r.r(u),r.d(u,{JsonPatchError:()=>ek,_areEquals:()=>ez,applyOperation:()=>eD,applyPatch:()=>eU,applyReducer:()=>eF,deepClone:()=>eN,getValueByPointer:()=>eM,validate:()=>eB,validator:()=>eH});var c={};r.r(c),r.d(c,{POST:()=>r5});var d=r(96559),h=r(48088),p=r(37719),f=r(32190),m=r(2507);class g{constructor(e){Object.defineProperty(this,"pageContent",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.pageContent=void 0!==e.pageContent?e.pageContent.toString():"",this.metadata=e.metadata??{},this.id=e.id}}var b=r(45697),y=r(63611);let v=require("node:crypto");var w=r.n(v);let _={randomUUID:w().randomUUID},E=new Uint8Array(256),O=E.length,I=[];for(let e=0;e<256;++e)I.push((e+256).toString(16).slice(1));let S=function(e,t,r){if(_.randomUUID&&!t&&!e)return _.randomUUID();let i=(e=e||{}).random||(e.rng||function(){return O>E.length-16&&(w().randomFillSync(E),O=0),E.slice(O,O+=16)})();if(i[6]=15&i[6]|64,i[8]=63&i[8]|128,t){r=r||0;for(let e=0;e<16;++e)t[r+e]=i[e];return t}return function(e,t=0){return(I[e[t+0]]+I[e[t+1]]+I[e[t+2]]+I[e[t+3]]+"-"+I[e[t+4]]+I[e[t+5]]+"-"+I[e[t+6]]+I[e[t+7]]+"-"+I[e[t+8]]+I[e[t+9]]+"-"+I[e[t+10]]+I[e[t+11]]+I[e[t+12]]+I[e[t+13]]+I[e[t+14]]+I[e[t+15]]).toLowerCase()}(i)};var T=r(71719);let P=(...e)=>fetch(...e),$=Symbol.for("ls:fetch_implementation"),A=()=>globalThis[$]??P,R=[400,401,403,404,405,406,407,408],j=[409];class x{constructor(e){Object.defineProperty(this,"maxConcurrency",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"maxRetries",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"queue",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"onFailedResponseHook",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.maxConcurrency=e.maxConcurrency??1/0,this.maxRetries=e.maxRetries??6,this.queue=new T.default({concurrency:this.maxConcurrency}),this.onFailedResponseHook=e?.onFailedResponseHook}call(e,...t){let r=this.onFailedResponseHook;return this.queue.add(()=>y(()=>e(...t).catch(e=>{if(e instanceof Error)throw e;throw Error(e)}),{async onFailedAttempt(e){if(e.message.startsWith("Cancel")||e.message.startsWith("TimeoutError")||e.message.startsWith("AbortError")||e?.code==="ECONNABORTED")throw e;let t=e?.response,i=t?.status;if(i){if(R.includes(+i))throw e;if(j.includes(+i))return;r&&await r(t)}},retries:this.maxRetries,randomize:!0}),{throwOnTimeout:!0})}callWithOptions(e,t,...r){return e.signal?Promise.race([this.call(t,...r),new Promise((t,r)=>{e.signal?.addEventListener("abort",()=>{r(Error("AbortError"))})})]):this.call(t,...r)}fetch(...e){return this.call(()=>A()(...e).then(e=>e.ok?e:Promise.reject(e)))}}function k(e){return"function"==typeof e?._getType}function N(e){let t={type:e._getType(),data:{content:e.content}};return e?.additional_kwargs&&Object.keys(e.additional_kwargs).length>0&&(t.data.additional_kwargs={...e.additional_kwargs}),t}let C=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i,L=function(e){return"string"==typeof e&&C.test(e)};function M(e,t){if(!L(e))throw Error(void 0!==t?`Invalid UUID for ${t}: ${e}`:`Invalid UUID: ${e}`);return e}let D={};function U(e){D[e]||(console.warn(e),D[e]=!0)}var F=r(28584);function H(e){if(!e||e.split("/").length>2||e.startsWith("/")||e.endsWith("/")||e.split(":").length>2)throw Error(`Invalid identifier format: ${e}`);let[t,r]=e.split(":"),i=r||"latest";if(t.includes("/")){let[r,a]=t.split("/",2);if(!r||!a)throw Error(`Invalid identifier format: ${e}`);return[r,a,i]}if(!t)throw Error(`Invalid identifier format: ${e}`);return["-",t,i]}class B extends Error{constructor(e){super(e),this.name="LangSmithConflictError"}}async function z(e,t,r){let i;if(e.ok){r&&(i=await e.text());return}i=await e.text();let a=`Failed to ${t}. Received status [${e.status}]: ${e.statusText}. Server response: ${i}`;if(409===e.status)throw new B(a);throw Error(a)}var G={result:"[Circular]"},q=[],J=[];function Z(e,t,r,i){try{return JSON.stringify(e,t,r)}catch(s){if(!s.message?.includes("Converting circular structure to JSON"))return console.warn("[WARNING]: LangSmith received unserializable value."),"[Unserializable]";console.warn("[WARNING]: LangSmith received circular JSON. This will decrease tracer performance."),void 0===i&&(i={depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}),function e(t,r,i,a,n,s,o){if(s+=1,"object"==typeof t&&null!==t){for(l=0;l<a.length;l++)if(a[l]===t)return void Y(G,t,r,n);if(void 0!==o.depthLimit&&s>o.depthLimit||void 0!==o.edgesLimit&&i+1>o.edgesLimit)return void Y("[...]",t,r,n);if(a.push(t),Array.isArray(t))for(l=0;l<t.length;l++)e(t[l],l,l,a,t,s,o);else{var l,u=Object.keys(t);for(l=0;l<u.length;l++){var c=u[l];e(t[c],c,l,a,t,s,o)}}a.pop()}}(e,"",0,[],void 0,0,i);try{a=0===J.length?JSON.stringify(e,t,r):JSON.stringify(e,function(e){return e=void 0!==e?e:function(e,t){return t},function(t,r){if(J.length>0)for(var i=0;i<J.length;i++){var a=J[i];if(a[1]===t&&a[0]===r){r=a[2],J.splice(i,1);break}}return e.call(this,t,r)}}(t),r)}catch(e){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==q.length;){var a,n=q.pop();4===n.length?Object.defineProperty(n[0],n[1],n[3]):n[0][n[1]]=n[2]}}return a}}function Y(e,t,r,i){var a=Object.getOwnPropertyDescriptor(i,r);void 0!==a.get?a.configurable?(Object.defineProperty(i,r,{value:e}),q.push([i,r,t,a])):J.push([t,r,e]):(i[r]=e,q.push([i,r,t]))}function W(e){let t=ec(),r=function(){let e=function(){try{if("undefined"!=typeof process&&process.env)return Object.entries(process.env).reduce((e,[t,r])=>(e[t]=String(r),e),{});return}catch(e){return}}()||{},t={},r=["LANGCHAIN_API_KEY","LANGCHAIN_ENDPOINT","LANGCHAIN_TRACING_V2","LANGCHAIN_PROJECT","LANGCHAIN_SESSION"];for(let[i,a]of Object.entries(e))!i.startsWith("LANGCHAIN_")||"string"!=typeof a||r.includes(i)||i.toLowerCase().includes("key")||i.toLowerCase().includes("secret")||i.toLowerCase().includes("token")||("LANGCHAIN_REVISION_ID"===i?t.revision_id=a:t[i]=a);return t}(),i=e.extra??{},a=i.metadata;return e.extra={...i,runtime:{...t,...i?.runtime},metadata:{...r,...r.revision_id||e.revision_id?{revision_id:e.revision_id??r.revision_id}:{},...a}},e}let V=()=>{let e=eh("TRACING_SAMPLING_RATE");if(void 0===e)return;let t=parseFloat(e);if(t<0||t>1)throw Error(`LANGSMITH_TRACING_SAMPLING_RATE must be between 0 and 1 if set. Got: ${t}`);return t},K=e=>{let t=e.replace("http://","").replace("https://","").split("/")[0].split(":")[0];return"localhost"===t||"127.0.0.1"===t||"::1"===t};async function X(e){let t=[];for await(let r of e)t.push(r);return t}function Q(e){if(void 0!==e)return e.trim().replace(/^"(.*)"$/,"$1").replace(/^'(.*)'$/,"$1")}let ee=async e=>{if(e?.status===429){let t=1e3*parseInt(e.headers.get("retry-after")??"30",10);if(t>0)return await new Promise(e=>setTimeout(e,t)),!0}return!1};class et{constructor(){Object.defineProperty(this,"items",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"sizeBytes",{enumerable:!0,configurable:!0,writable:!0,value:0})}peek(){return this.items[0]}push(e){let t,r=new Promise(e=>{t=e}),i=Z(e.item).length;return this.items.push({action:e.action,payload:e.item,itemPromiseResolve:t,itemPromise:r,size:i}),this.sizeBytes+=i,r}pop(e){if(e<1)throw Error("Number of bytes to pop off may not be less than 1.");let t=[],r=0;for(;r+(this.peek()?.size??0)<e&&this.items.length>0;){let e=this.items.shift();e&&(t.push(e),r+=e.size,this.sizeBytes-=e.size)}if(0===t.length&&this.items.length>0){let e=this.items.shift();t.push(e),r+=e.size,this.sizeBytes-=e.size}return[t.map(e=>({action:e.action,item:e.payload})),()=>t.forEach(e=>e.itemPromiseResolve())]}}class er{constructor(e={}){Object.defineProperty(this,"apiKey",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"apiUrl",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"webUrl",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"caller",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"batchIngestCaller",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"timeout_ms",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_tenantId",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"hideInputs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"hideOutputs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tracingSampleRate",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"filteredPostUuids",{enumerable:!0,configurable:!0,writable:!0,value:new Set}),Object.defineProperty(this,"autoBatchTracing",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"autoBatchQueue",{enumerable:!0,configurable:!0,writable:!0,value:new et}),Object.defineProperty(this,"autoBatchTimeout",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"autoBatchInitialDelayMs",{enumerable:!0,configurable:!0,writable:!0,value:250}),Object.defineProperty(this,"autoBatchAggregationDelayMs",{enumerable:!0,configurable:!0,writable:!0,value:50}),Object.defineProperty(this,"batchSizeBytesLimit",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"fetchOptions",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"settings",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"blockOnRootRunFinalization",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"_serverInfo",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_getServerInfoPromise",{enumerable:!0,configurable:!0,writable:!0,value:void 0});let t=er.getDefaultClientConfig();this.tracingSampleRate=V(),this.apiUrl=Q(e.apiUrl??t.apiUrl)??"",this.apiUrl.endsWith("/")&&(this.apiUrl=this.apiUrl.slice(0,-1)),this.apiKey=Q(e.apiKey??t.apiKey),this.webUrl=Q(e.webUrl??t.webUrl),this.webUrl?.endsWith("/")&&(this.webUrl=this.webUrl.slice(0,-1)),this.timeout_ms=e.timeout_ms??12e3,this.caller=new x(e.callerOptions??{}),this.batchIngestCaller=new x({...e.callerOptions??{},onFailedResponseHook:ee}),this.hideInputs=e.hideInputs??e.anonymizer??t.hideInputs,this.hideOutputs=e.hideOutputs??e.anonymizer??t.hideOutputs,this.autoBatchTracing=e.autoBatchTracing??this.autoBatchTracing,this.blockOnRootRunFinalization=e.blockOnRootRunFinalization??this.blockOnRootRunFinalization,this.batchSizeBytesLimit=e.batchSizeBytesLimit,this.fetchOptions=e.fetchOptions||{}}static getDefaultClientConfig(){let e=eh("API_KEY"),t=eh("ENDPOINT")??"https://api.smith.langchain.com";return{apiUrl:t,apiKey:e,webUrl:void 0,hideInputs:"true"===eh("HIDE_INPUTS"),hideOutputs:"true"===eh("HIDE_OUTPUTS")}}getHostUrl(){if(this.webUrl)return this.webUrl;if(K(this.apiUrl))return this.webUrl="http://localhost:3000",this.webUrl;if(this.apiUrl.includes("/api")&&!this.apiUrl.split(".",1)[0].endsWith("api"))return this.webUrl=this.apiUrl.replace("/api",""),this.webUrl;if(this.apiUrl.split(".",1)[0].includes("dev"))return this.webUrl="https://dev.smith.langchain.com",this.webUrl;if(this.apiUrl.split(".",1)[0].includes("eu"))return this.webUrl="https://eu.smith.langchain.com",this.webUrl;else return this.webUrl="https://smith.langchain.com",this.webUrl}get headers(){let e={"User-Agent":`langsmith-js/${ei}`};return this.apiKey&&(e["x-api-key"]=`${this.apiKey}`),e}processInputs(e){return!1===this.hideInputs?e:!0===this.hideInputs?{}:"function"==typeof this.hideInputs?this.hideInputs(e):e}processOutputs(e){return!1===this.hideOutputs?e:!0===this.hideOutputs?{}:"function"==typeof this.hideOutputs?this.hideOutputs(e):e}prepareRunCreateOrUpdateInputs(e){let t={...e};return void 0!==t.inputs&&(t.inputs=this.processInputs(t.inputs)),void 0!==t.outputs&&(t.outputs=this.processOutputs(t.outputs)),t}async _getResponse(e,t){let r=t?.toString()??"",i=`${this.apiUrl}${e}?${r}`,a=await this.caller.call(A(),i,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await z(a,`Failed to fetch ${e}`),a}async _get(e,t){return(await this._getResponse(e,t)).json()}async *_getPaginated(e,t=new URLSearchParams,r){let i=Number(t.get("offset"))||0,a=Number(t.get("limit"))||100;for(;;){t.set("offset",String(i)),t.set("limit",String(a));let n=`${this.apiUrl}${e}?${t}`,s=await this.caller.call(A(),n,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await z(s,`Failed to fetch ${e}`);let o=r?r(await s.json()):await s.json();if(0===o.length||(yield o,o.length<a))break;i+=o.length}}async *_getCursorPaginatedList(e,t=null,r="POST",i="runs"){let a=t?{...t}:{};for(;;){let t=await this.caller.call(A(),`${this.apiUrl}${e}`,{method:r,headers:{...this.headers,"Content-Type":"application/json"},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions,body:JSON.stringify(a)}),n=await t.json();if(!n||!n[i])break;yield n[i];let s=n.cursors;if(!s||!s.next)break;a.cursor=s.next}}_filterForSampling(e,t=!1){if(void 0===this.tracingSampleRate)return e;if(t){let t=[];for(let r of e)this.filteredPostUuids.has(r.id)?this.filteredPostUuids.delete(r.id):t.push(r);return t}{let t=[];for(let r of e)r.id!==r.trace_id&&!this.filteredPostUuids.has(r.trace_id)||Math.random()<this.tracingSampleRate?t.push(r):this.filteredPostUuids.add(r.id);return t}}async _getBatchSizeLimitBytes(){let e=await this._ensureServerInfo();return this.batchSizeBytesLimit??e.batch_ingest_config?.size_limit_bytes??0x1400000}async drainAutoBatchQueue(){for(;this.autoBatchQueue.items.length>=0;){let[e,t]=this.autoBatchQueue.pop(await this._getBatchSizeLimitBytes());if(!e.length)return void t();try{let t={runCreates:e.filter(e=>"create"===e.action).map(e=>e.item),runUpdates:e.filter(e=>"update"===e.action).map(e=>e.item)},r=await this._ensureServerInfo();r?.batch_ingest_config?.use_multipart_endpoint?await this.multipartIngestRuns(t):await this.batchIngestRuns(t)}finally{t()}}}async processRunOperation(e,t){let r=this.autoBatchTimeout;clearTimeout(this.autoBatchTimeout),this.autoBatchTimeout=void 0,"create"===e.action&&(e.item=W(e.item));let i=this.autoBatchQueue.push(e),a=await this._getBatchSizeLimitBytes();return(t||this.autoBatchQueue.sizeBytes>a)&&await this.drainAutoBatchQueue().catch(console.error),this.autoBatchQueue.items.length>0&&(this.autoBatchTimeout=setTimeout(()=>{this.autoBatchTimeout=void 0,this.drainAutoBatchQueue().catch(console.error)},r?this.autoBatchAggregationDelayMs:this.autoBatchInitialDelayMs)),i}async _getServerInfo(){let e=await A()(`${this.apiUrl}/info`,{method:"GET",headers:{Accept:"application/json"},signal:AbortSignal.timeout(1e3),...this.fetchOptions});return await z(e,"get server info"),e.json()}async _ensureServerInfo(){return void 0===this._getServerInfoPromise&&(this._getServerInfoPromise=(async()=>{if(void 0===this._serverInfo)try{this._serverInfo=await this._getServerInfo()}catch(e){console.warn("[WARNING]: LangSmith failed to fetch info on supported operations. Falling back to single calls and default limits.")}return this._serverInfo??{}})()),this._getServerInfoPromise.then(e=>(void 0===this._serverInfo&&(this._getServerInfoPromise=void 0),e))}async _getSettings(){return this.settings||(this.settings=this._get("/settings")),await this.settings}async createRun(e){if(!this._filterForSampling([e]).length)return;let t={...this.headers,"Content-Type":"application/json"},r=e.project_name;delete e.project_name;let i=this.prepareRunCreateOrUpdateInputs({session_name:r,...e,start_time:e.start_time??Date.now()});if(this.autoBatchTracing&&void 0!==i.trace_id&&void 0!==i.dotted_order)return void this.processRunOperation({action:"create",item:i}).catch(console.error);let a=W(i),n=await this.caller.call(A(),`${this.apiUrl}/runs`,{method:"POST",headers:t,body:Z(a),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await z(n,"create run",!0)}async batchIngestRuns({runCreates:e,runUpdates:t}){if(void 0===e&&void 0===t)return;let r=e?.map(e=>this.prepareRunCreateOrUpdateInputs(e))??[],i=t?.map(e=>this.prepareRunCreateOrUpdateInputs(e))??[];if(r.length>0&&i.length>0){let e=r.reduce((e,t)=>(t.id&&(e[t.id]=t),e),{}),t=[];for(let r of i)void 0!==r.id&&e[r.id]?e[r.id]={...e[r.id],...r}:t.push(r);r=Object.values(e),i=t}let a={post:this._filterForSampling(r),patch:this._filterForSampling(i,!0)};if(!a.post.length&&!a.patch.length)return;if(void 0===(await this._ensureServerInfo()).version){for(let e of(this.autoBatchTracing=!1,a.post))await this.createRun(e);for(let e of a.patch)void 0!==e.id&&await this.updateRun(e.id,e);return}let n={post:[],patch:[]};for(let e of["post","patch"]){let t=a[e].reverse(),r=t.pop();for(;void 0!==r;)n[e].push(r),r=t.pop()}(n.post.length>0||n.patch.length>0)&&await this._postBatchIngestRuns(Z(n))}async _postBatchIngestRuns(e){let t={...this.headers,"Content-Type":"application/json",Accept:"application/json"},r=await this.batchIngestCaller.call(A(),`${this.apiUrl}/runs/batch`,{method:"POST",headers:t,body:e,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await z(r,"batch create run",!0)}async multipartIngestRuns({runCreates:e,runUpdates:t}){if(void 0===e&&void 0===t)return;let r={},i=[];for(let t of e??[]){let e=this.prepareRunCreateOrUpdateInputs(t);void 0!==e.id&&void 0!==e.attachments&&(r[e.id]=e.attachments),delete e.attachments,i.push(e)}let a=[];for(let e of t??[])a.push(this.prepareRunCreateOrUpdateInputs(e));if(void 0!==i.find(e=>void 0===e.trace_id||void 0===e.dotted_order))throw Error('Multipart ingest requires "trace_id" and "dotted_order" to be set when creating a run');if(void 0!==a.find(e=>void 0===e.trace_id||void 0===e.dotted_order))throw Error('Multipart ingest requires "trace_id" and "dotted_order" to be set when updating a run');if(i.length>0&&a.length>0){let e=i.reduce((e,t)=>(t.id&&(e[t.id]=t),e),{}),t=[];for(let r of a)void 0!==r.id&&e[r.id]?e[r.id]={...e[r.id],...r}:t.push(r);i=Object.values(e),a=t}if(0===i.length&&0===a.length)return;let n=[],s=[];for(let[e,t]of[["post",i],["patch",a]])for(let i of t){let{inputs:t,outputs:a,events:o,...l}=i,u={inputs:t,outputs:a,events:o},c=Z(l);for(let[t,r]of(s.push({name:`${e}.${l.id}`,payload:new Blob([c],{type:`application/json; length=${c.length}`})}),Object.entries(u))){if(void 0===r)continue;let i=Z(r);s.push({name:`${e}.${l.id}.${t}`,payload:new Blob([i],{type:`application/json; length=${i.length}`})})}if(void 0!==l.id){let e=r[l.id];if(e)for(let[t,[i,a]]of(delete r[l.id],Object.entries(e)))s.push({name:`attachment.${l.id}.${t}`,payload:new Blob([a],{type:`${i}; length=${a.length}`})})}n.push(`trace=${l.trace_id},id=${l.id}`)}await this._sendMultipartRequest(s,n.join("; "))}async _sendMultipartRequest(e,t){try{let t=new FormData;for(let r of e)t.append(r.name,r.payload);await this.batchIngestCaller.call(A(),`${this.apiUrl}/runs/multipart`,{method:"POST",headers:{...this.headers},body:t,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions})}catch(r){let e="Failed to multipart ingest runs";r instanceof Error?e+=`: ${r.stack||r.message}`:e+=`: ${String(r)}`,console.warn(`${e.trim()}

Context: ${t}`)}}async updateRun(e,t){M(e),t.inputs&&(t.inputs=this.processInputs(t.inputs)),t.outputs&&(t.outputs=this.processOutputs(t.outputs));let r={...t,id:e};if(!this._filterForSampling([r],!0).length)return;if(this.autoBatchTracing&&void 0!==r.trace_id&&void 0!==r.dotted_order)return void 0!==t.end_time&&void 0===r.parent_run_id&&this.blockOnRootRunFinalization?void await this.processRunOperation({action:"update",item:r},!0):void this.processRunOperation({action:"update",item:r}).catch(console.error);let i={...this.headers,"Content-Type":"application/json"},a=await this.caller.call(A(),`${this.apiUrl}/runs/${e}`,{method:"PATCH",headers:i,body:Z(t),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await z(a,"update run",!0)}async readRun(e,{loadChildRuns:t}={loadChildRuns:!1}){M(e);let r=await this._get(`/runs/${e}`);return t&&r.child_run_ids&&(r=await this._loadChildRuns(r)),r}async getRunUrl({runId:e,run:t,projectOpts:r}){if(void 0!==t){let e;e=t.session_id?t.session_id:r?.projectName?(await this.readProject({projectName:r?.projectName})).id:r?.projectId?r?.projectId:(await this.readProject({projectName:eh("PROJECT")||"default"})).id;let i=await this._getTenantId();return`${this.getHostUrl()}/o/${i}/projects/p/${e}/r/${t.id}?poll=true`}if(void 0!==e){let t=await this.readRun(e);if(!t.app_path)throw Error(`Run ${e} has no app_path`);let r=this.getHostUrl();return`${r}${t.app_path}`}throw Error("Must provide either runId or run")}async _loadChildRuns(e){let t=await X(this.listRuns({id:e.child_run_ids})),r={},i={};for(let e of(t.sort((e,t)=>(e?.dotted_order??"").localeCompare(t?.dotted_order??"")),t)){if(null===e.parent_run_id||void 0===e.parent_run_id)throw Error(`Child run ${e.id} has no parent`);e.parent_run_id in r||(r[e.parent_run_id]=[]),r[e.parent_run_id].push(e),i[e.id]=e}for(let t in e.child_runs=r[e.id]||[],r)t!==e.id&&(i[t].child_runs=r[t]);return e}async *listRuns(e){let{projectId:t,projectName:r,parentRunId:i,traceId:a,referenceExampleId:n,startTime:s,executionOrder:o,isRoot:l,runType:u,error:c,id:d,query:h,filter:p,traceFilter:f,treeFilter:m,limit:g,select:b}=e,y=[];if(t&&(y=Array.isArray(t)?t:[t]),r){let e=Array.isArray(r)?r:[r],t=await Promise.all(e.map(e=>this.readProject({projectName:e}).then(e=>e.id)));y.push(...t)}let v={session:y.length?y:null,run_type:u,reference_example:n,query:h,filter:p,trace_filter:f,tree_filter:m,execution_order:o,parent_run:i,start_time:s?s.toISOString():null,error:c,id:d,limit:g,trace:a,select:b||["app_path","child_run_ids","completion_cost","completion_tokens","dotted_order","end_time","error","events","extra","feedback_stats","first_token_time","id","inputs","name","outputs","parent_run_id","parent_run_ids","prompt_cost","prompt_tokens","reference_example_id","run_type","session_id","start_time","status","tags","total_cost","total_tokens","trace_id"],is_root:l},w=0;for await(let e of this._getCursorPaginatedList("/runs/query",v))if(g){if(w>=g)break;if(e.length+w>g){let t=e.slice(0,g-w);yield*t;break}w+=e.length,yield*e}else yield*e}async getRunStats({id:e,trace:t,parentRun:r,runType:i,projectNames:a,projectIds:n,referenceExampleIds:s,startTime:o,endTime:l,error:u,query:c,filter:d,traceFilter:h,treeFilter:p,isRoot:f,dataSourceType:m}){let g=n||[];a&&(g=[...n||[],...await Promise.all(a.map(e=>this.readProject({projectName:e}).then(e=>e.id)))]);let b=Object.fromEntries(Object.entries({id:e,trace:t,parent_run:r,run_type:i,session:g,reference_example:s,start_time:o,end_time:l,error:u,query:c,filter:d,trace_filter:h,tree_filter:p,is_root:f,data_source_type:m}).filter(([e,t])=>void 0!==t)),y=await this.caller.call(A(),`${this.apiUrl}/runs/stats`,{method:"POST",headers:this.headers,body:JSON.stringify(b),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await y.json()}async shareRun(e,{shareId:t}={}){let r={run_id:e,share_token:t||S()};M(e);let i=await this.caller.call(A(),`${this.apiUrl}/runs/${e}/share`,{method:"PUT",headers:this.headers,body:JSON.stringify(r),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),a=await i.json();if(null===a||!("share_token"in a))throw Error("Invalid response from server");return`${this.getHostUrl()}/public/${a.share_token}/r`}async unshareRun(e){M(e);let t=await this.caller.call(A(),`${this.apiUrl}/runs/${e}/share`,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await z(t,"unshare run",!0)}async readRunSharedLink(e){M(e);let t=await this.caller.call(A(),`${this.apiUrl}/runs/${e}/share`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),r=await t.json();if(null!==r&&"share_token"in r)return`${this.getHostUrl()}/public/${r.share_token}/r`}async listSharedRuns(e,{runIds:t}={}){let r=new URLSearchParams({share_token:e});if(void 0!==t)for(let e of t)r.append("id",e);M(e);let i=await this.caller.call(A(),`${this.apiUrl}/public/${e}/runs${r}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await i.json()}async readDatasetSharedSchema(e,t){if(!e&&!t)throw Error("Either datasetId or datasetName must be given");e||(e=(await this.readDataset({datasetName:t})).id),M(e);let r=await this.caller.call(A(),`${this.apiUrl}/datasets/${e}/share`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),i=await r.json();return i.url=`${this.getHostUrl()}/public/${i.share_token}/d`,i}async shareDataset(e,t){if(!e&&!t)throw Error("Either datasetId or datasetName must be given");e||(e=(await this.readDataset({datasetName:t})).id);let r={dataset_id:e};M(e);let i=await this.caller.call(A(),`${this.apiUrl}/datasets/${e}/share`,{method:"PUT",headers:this.headers,body:JSON.stringify(r),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),a=await i.json();return a.url=`${this.getHostUrl()}/public/${a.share_token}/d`,a}async unshareDataset(e){M(e);let t=await this.caller.call(A(),`${this.apiUrl}/datasets/${e}/share`,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await z(t,"unshare dataset",!0)}async readSharedDataset(e){M(e);let t=await this.caller.call(A(),`${this.apiUrl}/public/${e}/datasets`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await t.json()}async listSharedExamples(e,t){let r={};t?.exampleIds&&(r.id=t.exampleIds);let i=new URLSearchParams;Object.entries(r).forEach(([e,t])=>{Array.isArray(t)?t.forEach(t=>i.append(e,t)):i.append(e,t)});let a=await this.caller.call(A(),`${this.apiUrl}/public/${e}/examples?${i.toString()}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),n=await a.json();if(!a.ok){if("detail"in n)throw Error(`Failed to list shared examples.
Status: ${a.status}
Message: ${n.detail.join("\n")}`);throw Error(`Failed to list shared examples: ${a.status} ${a.statusText}`)}return n.map(e=>({...e,_hostUrl:this.getHostUrl()}))}async createProject({projectName:e,description:t=null,metadata:r=null,upsert:i=!1,projectExtra:a=null,referenceDatasetId:n=null}){let s=`${this.apiUrl}/sessions${i?"?upsert=true":""}`,o=a||{};r&&(o.metadata=r);let l={name:e,extra:o,description:t};null!==n&&(l.reference_dataset_id=n);let u=await this.caller.call(A(),s,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(l),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await z(u,"create project"),await u.json()}async updateProject(e,{name:t=null,description:r=null,metadata:i=null,projectExtra:a=null,endTime:n=null}){let s=`${this.apiUrl}/sessions/${e}`,o=a;i&&(o={...o||{},metadata:i});let l={name:t,extra:o,description:r,end_time:n?new Date(n).toISOString():null},u=await this.caller.call(A(),s,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(l),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await z(u,"update project"),await u.json()}async hasProject({projectId:e,projectName:t}){let r="/sessions",i=new URLSearchParams;if(void 0!==e&&void 0!==t)throw Error("Must provide either projectName or projectId, not both");if(void 0!==e)M(e),r+=`/${e}`;else if(void 0!==t)i.append("name",t);else throw Error("Must provide projectName or projectId");let a=await this.caller.call(A(),`${this.apiUrl}${r}?${i}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});try{let e=await a.json();if(!a.ok)return!1;if(Array.isArray(e))return e.length>0;return!0}catch(e){return!1}}async readProject({projectId:e,projectName:t,includeStats:r}){let i,a="/sessions",n=new URLSearchParams;if(void 0!==e&&void 0!==t)throw Error("Must provide either projectName or projectId, not both");if(void 0!==e)M(e),a+=`/${e}`;else if(void 0!==t)n.append("name",t);else throw Error("Must provide projectName or projectId");void 0!==r&&n.append("include_stats",r.toString());let s=await this._get(a,n);if(Array.isArray(s)){if(0===s.length)throw Error(`Project[id=${e}, name=${t}] not found`);i=s[0]}else i=s;return i}async getProjectUrl({projectId:e,projectName:t}){if(void 0===e&&void 0===t)throw Error("Must provide either projectName or projectId");let r=await this.readProject({projectId:e,projectName:t}),i=await this._getTenantId();return`${this.getHostUrl()}/o/${i}/projects/p/${r.id}`}async getDatasetUrl({datasetId:e,datasetName:t}){if(void 0===e&&void 0===t)throw Error("Must provide either datasetName or datasetId");let r=await this.readDataset({datasetId:e,datasetName:t}),i=await this._getTenantId();return`${this.getHostUrl()}/o/${i}/datasets/${r.id}`}async _getTenantId(){if(null!==this._tenantId)return this._tenantId;let e=new URLSearchParams({limit:"1"});for await(let t of this._getPaginated("/sessions",e))return this._tenantId=t[0].tenant_id,t[0].tenant_id;throw Error("No projects found to resolve tenant.")}async *listProjects({projectIds:e,name:t,nameContains:r,referenceDatasetId:i,referenceDatasetName:a,referenceFree:n,metadata:s}={}){let o=new URLSearchParams;if(void 0!==e)for(let t of e)o.append("id",t);if(void 0!==t&&o.append("name",t),void 0!==r&&o.append("name_contains",r),void 0!==i)o.append("reference_dataset",i);else if(void 0!==a){let e=await this.readDataset({datasetName:a});o.append("reference_dataset",e.id)}for await(let e of(void 0!==n&&o.append("reference_free",n.toString()),void 0!==s&&o.append("metadata",JSON.stringify(s)),this._getPaginated("/sessions",o)))yield*e}async deleteProject({projectId:e,projectName:t}){let r;if(void 0===e&&void 0===t)throw Error("Must provide projectName or projectId");if(void 0!==e&&void 0!==t)throw Error("Must provide either projectName or projectId, not both");M(r=void 0===e?(await this.readProject({projectName:t})).id:e);let i=await this.caller.call(A(),`${this.apiUrl}/sessions/${r}`,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await z(i,`delete session ${r} (${t})`,!0)}async uploadCsv({csvFile:e,fileName:t,inputKeys:r,outputKeys:i,description:a,dataType:n,name:s}){let o=`${this.apiUrl}/datasets/upload`,l=new FormData;l.append("file",e,t),r.forEach(e=>{l.append("input_keys",e)}),i.forEach(e=>{l.append("output_keys",e)}),a&&l.append("description",a),n&&l.append("data_type",n),s&&l.append("name",s);let u=await this.caller.call(A(),o,{method:"POST",headers:this.headers,body:l,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await z(u,"upload CSV"),await u.json()}async createDataset(e,{description:t,dataType:r,inputsSchema:i,outputsSchema:a,metadata:n}={}){let s={name:e,description:t,extra:n?{metadata:n}:void 0};r&&(s.data_type=r),i&&(s.inputs_schema_definition=i),a&&(s.outputs_schema_definition=a);let o=await this.caller.call(A(),`${this.apiUrl}/datasets`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(s),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await z(o,"create dataset"),await o.json()}async readDataset({datasetId:e,datasetName:t}){let r,i="/datasets",a=new URLSearchParams({limit:"1"});if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");if(void 0!==e)M(e),i+=`/${e}`;else if(void 0!==t)a.append("name",t);else throw Error("Must provide datasetName or datasetId");let n=await this._get(i,a);if(Array.isArray(n)){if(0===n.length)throw Error(`Dataset[id=${e}, name=${t}] not found`);r=n[0]}else r=n;return r}async hasDataset({datasetId:e,datasetName:t}){try{return await this.readDataset({datasetId:e,datasetName:t}),!0}catch(e){if(e instanceof Error&&e.message.toLocaleLowerCase().includes("not found"))return!1;throw e}}async diffDatasetVersions({datasetId:e,datasetName:t,fromVersion:r,toVersion:i}){let a=e;if(void 0===a&&void 0===t)throw Error("Must provide either datasetName or datasetId");if(void 0!==a&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");void 0===a&&(a=(await this.readDataset({datasetName:t})).id);let n=new URLSearchParams({from_version:"string"==typeof r?r:r.toISOString(),to_version:"string"==typeof i?i:i.toISOString()});return await this._get(`/datasets/${a}/versions/diff`,n)}async readDatasetOpenaiFinetuning({datasetId:e,datasetName:t}){if(void 0!==e);else if(void 0!==t)e=(await this.readDataset({datasetName:t})).id;else throw Error("Must provide datasetName or datasetId");let r=await this._getResponse(`/datasets/${e}/openai_ft`);return(await r.text()).trim().split("\n").map(e=>JSON.parse(e))}async *listDatasets({limit:e=100,offset:t=0,datasetIds:r,datasetName:i,datasetNameContains:a,metadata:n}={}){let s=new URLSearchParams({limit:e.toString(),offset:t.toString()});if(void 0!==r)for(let e of r)s.append("id",e);for await(let e of(void 0!==i&&s.append("name",i),void 0!==a&&s.append("name_contains",a),void 0!==n&&s.append("metadata",JSON.stringify(n)),this._getPaginated("/datasets",s)))yield*e}async updateDataset(e){let{datasetId:t,datasetName:r,...i}=e;if(!t&&!r)throw Error("Must provide either datasetName or datasetId");let a=t??(await this.readDataset({datasetName:r})).id;M(a);let n=await this.caller.call(A(),`${this.apiUrl}/datasets/${a}`,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(i),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await z(n,"update dataset"),await n.json()}async deleteDataset({datasetId:e,datasetName:t}){let r="/datasets",i=e;if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");if(void 0!==t&&(i=(await this.readDataset({datasetName:t})).id),void 0!==i)M(i),r+=`/${i}`;else throw Error("Must provide datasetName or datasetId");let a=await this.caller.call(A(),this.apiUrl+r,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await z(a,`delete ${r}`),await a.json()}async indexDataset({datasetId:e,datasetName:t,tag:r}){let i=e;if(i||t)if(i&&t)throw Error("Must provide either datasetName or datasetId, not both");else i||(i=(await this.readDataset({datasetName:t})).id);else throw Error("Must provide either datasetName or datasetId");M(i);let a=await this.caller.call(A(),`${this.apiUrl}/datasets/${i}/index`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify({tag:r}),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await z(a,"index dataset"),await a.json()}async similarExamples(e,t,r,{filter:i}={}){let a={limit:r,inputs:e};void 0!==i&&(a.filter=i),M(t);let n=await this.caller.call(A(),`${this.apiUrl}/datasets/${t}/search`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(a),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await z(n,"fetch similar examples"),(await n.json()).examples}async createExample(e,t,{datasetId:r,datasetName:i,createdAt:a,exampleId:n,metadata:s,split:o,sourceRunId:l}){let u=r;if(void 0===u&&void 0===i)throw Error("Must provide either datasetName or datasetId");if(void 0!==u&&void 0!==i)throw Error("Must provide either datasetName or datasetId, not both");void 0===u&&(u=(await this.readDataset({datasetName:i})).id);let c=a||new Date,d={dataset_id:u,inputs:e,outputs:t,created_at:c?.toISOString(),id:n,metadata:s,split:o,source_run_id:l},h=await this.caller.call(A(),`${this.apiUrl}/examples`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(d),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await z(h,"create example"),await h.json()}async createExamples(e){let{inputs:t,outputs:r,metadata:i,sourceRunIds:a,exampleIds:n,datasetId:s,datasetName:o}=e,l=s;if(void 0===l&&void 0===o)throw Error("Must provide either datasetName or datasetId");if(void 0!==l&&void 0!==o)throw Error("Must provide either datasetName or datasetId, not both");void 0===l&&(l=(await this.readDataset({datasetName:o})).id);let u=t.map((t,s)=>({dataset_id:l,inputs:t,outputs:r?r[s]:void 0,metadata:i?i[s]:void 0,split:e.splits?e.splits[s]:void 0,id:n?n[s]:void 0,source_run_id:a?a[s]:void 0})),c=await this.caller.call(A(),`${this.apiUrl}/examples/bulk`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(u),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await z(c,"create examples"),await c.json()}async createLLMExample(e,t,r){return this.createExample({input:e},{output:t},r)}async createChatExample(e,t,r){let i=e.map(e=>k(e)?N(e):e),a=k(t)?N(t):t;return this.createExample({input:i},{output:a},r)}async readExample(e){M(e);let t=`/examples/${e}`;return await this._get(t)}async *listExamples({datasetId:e,datasetName:t,exampleIds:r,asOf:i,splits:a,inlineS3Urls:n,metadata:s,limit:o,offset:l,filter:u}={}){let c;if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");if(void 0!==e)c=e;else if(void 0!==t)c=(await this.readDataset({datasetName:t})).id;else throw Error("Must provide a datasetName or datasetId");let d=new URLSearchParams({dataset:c}),h=i?"string"==typeof i?i:i?.toISOString():void 0;if(h&&d.append("as_of",h),d.append("inline_s3_urls",(n??!0).toString()),void 0!==r)for(let e of r)d.append("id",e);if(void 0!==a)for(let e of a)d.append("splits",e);if(void 0!==s){let e=JSON.stringify(s);d.append("metadata",e)}void 0!==o&&d.append("limit",o.toString()),void 0!==l&&d.append("offset",l.toString()),void 0!==u&&d.append("filter",u);let p=0;for await(let e of this._getPaginated("/examples",d)){for(let t of e)yield t,p++;if(void 0!==o&&p>=o)break}}async deleteExample(e){M(e);let t=`/examples/${e}`,r=await this.caller.call(A(),this.apiUrl+t,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await z(r,`delete ${t}`),await r.json()}async updateExample(e,t){M(e);let r=await this.caller.call(A(),`${this.apiUrl}/examples/${e}`,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(t),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await z(r,"update example"),await r.json()}async updateExamples(e){let t=await this.caller.call(A(),`${this.apiUrl}/examples/bulk`,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(e),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await z(t,"update examples"),await t.json()}async listDatasetSplits({datasetId:e,datasetName:t,asOf:r}){let i;if(void 0===e&&void 0===t)throw Error("Must provide dataset name or ID");if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");M(i=void 0===e?(await this.readDataset({datasetName:t})).id:e);let a=new URLSearchParams,n=r?"string"==typeof r?r:r?.toISOString():void 0;return n&&a.append("as_of",n),await this._get(`/datasets/${i}/splits`,a)}async updateDatasetSplits({datasetId:e,datasetName:t,splitName:r,exampleIds:i,remove:a=!1}){let n;if(void 0===e&&void 0===t)throw Error("Must provide dataset name or ID");if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");M(n=void 0===e?(await this.readDataset({datasetName:t})).id:e);let s={split_name:r,examples:i.map(e=>(M(e),e)),remove:a},o=await this.caller.call(A(),`${this.apiUrl}/datasets/${n}/splits`,{method:"PUT",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(s),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await z(o,"update dataset splits",!0)}async evaluateRun(e,t,{sourceInfo:r,loadChildRuns:i,referenceExample:a}={loadChildRuns:!1}){let n;if(U("This method is deprecated and will be removed in future LangSmith versions, use `evaluate` from `langsmith/evaluation` instead."),"string"==typeof e)n=await this.readRun(e,{loadChildRuns:i});else if("object"==typeof e&&"id"in e)n=e;else throw Error(`Invalid run type: ${typeof e}`);null!==n.reference_example_id&&void 0!==n.reference_example_id&&(a=await this.readExample(n.reference_example_id));let s=await t.evaluateRun(n,a),[o,l]=await this._logEvaluationFeedback(s,n,r);return l[0]}async createFeedback(e,t,{score:r,value:i,correction:a,comment:n,sourceInfo:s,feedbackSourceType:o="api",sourceRunId:l,feedbackId:u,feedbackConfig:c,projectId:d,comparativeExperimentId:h}){if(!e&&!d)throw Error("One of runId or projectId must be provided");if(e&&d)throw Error("Only one of runId or projectId can be provided");let p={type:o??"api",metadata:s??{}};void 0===l||p?.metadata===void 0||p.metadata.__run||(p.metadata.__run={run_id:l}),p?.metadata!==void 0&&p.metadata.__run?.run_id!==void 0&&M(p.metadata.__run.run_id);let f={id:u??S(),run_id:e,key:t,score:r,value:i,correction:a,comment:n,feedback_source:p,comparative_experiment_id:h,feedbackConfig:c,session_id:d},m=`${this.apiUrl}/feedback`,g=await this.caller.call(A(),m,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(f),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await z(g,"create feedback",!0),f}async updateFeedback(e,{score:t,value:r,correction:i,comment:a}){let n={};null!=t&&(n.score=t),null!=r&&(n.value=r),null!=i&&(n.correction=i),null!=a&&(n.comment=a),M(e);let s=await this.caller.call(A(),`${this.apiUrl}/feedback/${e}`,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(n),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await z(s,"update feedback",!0)}async readFeedback(e){M(e);let t=`/feedback/${e}`;return await this._get(t)}async deleteFeedback(e){M(e);let t=`/feedback/${e}`,r=await this.caller.call(A(),this.apiUrl+t,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await z(r,`delete ${t}`),await r.json()}async *listFeedback({runIds:e,feedbackKeys:t,feedbackSourceTypes:r}={}){let i=new URLSearchParams;if(e&&i.append("run",e.join(",")),t)for(let e of t)i.append("key",e);if(r)for(let e of r)i.append("source",e);for await(let e of this._getPaginated("/feedback",i))yield*e}async createPresignedFeedbackToken(e,t,{expiration:r,feedbackConfig:i}={}){let a={run_id:e,feedback_key:t,feedback_config:i};r?"string"==typeof r?a.expires_at=r:(r?.hours||r?.minutes||r?.days)&&(a.expires_in=r):a.expires_in={hours:3};let n=await this.caller.call(A(),`${this.apiUrl}/feedback/tokens`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(a),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await n.json()}async createComparativeExperiment({name:e,experimentIds:t,referenceDatasetId:r,createdAt:i,description:a,metadata:n,id:s}){if(0===t.length)throw Error("At least one experiment is required");if(r||(r=(await this.readProject({projectId:t[0]})).reference_dataset_id),null==!r)throw Error("A reference dataset is required");let o={id:s,name:e,experiment_ids:t,reference_dataset_id:r,description:a,created_at:(i??new Date)?.toISOString(),extra:{}};n&&(o.extra.metadata=n);let l=await this.caller.call(A(),`${this.apiUrl}/datasets/comparative`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(o),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await l.json()}async *listPresignedFeedbackTokens(e){M(e);let t=new URLSearchParams({run_id:e});for await(let e of this._getPaginated("/feedback/tokens",t))yield*e}_selectEvalResults(e){let t;return"results"in e?e.results:[e]}async _logEvaluationFeedback(e,t,r){let i=this._selectEvalResults(e),a=[];for(let e of i){let i=r||{};e.evaluatorInfo&&(i={...e.evaluatorInfo,...i});let n=null;e.targetRunId?n=e.targetRunId:t&&(n=t.id),a.push(await this.createFeedback(n,e.key,{score:e.score,value:e.value,comment:e.comment,correction:e.correction,sourceInfo:i,sourceRunId:e.sourceRunId,feedbackConfig:e.feedbackConfig,feedbackSourceType:"model"}))}return[i,a]}async logEvaluationFeedback(e,t,r){let[i]=await this._logEvaluationFeedback(e,t,r);return i}async *listAnnotationQueues(e={}){let{queueIds:t,name:r,nameContains:i,limit:a}=e,n=new URLSearchParams;t&&t.forEach((e,t)=>{M(e,`queueIds[${t}]`),n.append("ids",e)}),r&&n.append("name",r),i&&n.append("name_contains",i),n.append("limit",(void 0!==a?Math.min(a,100):100).toString());let s=0;for await(let e of this._getPaginated("/annotation-queues",n))if(yield*e,s++,void 0!==a&&s>=a)break}async createAnnotationQueue(e){let{name:t,description:r,queueId:i}=e,a={name:t,description:r,id:i||S()},n=await this.caller.call(A(),`${this.apiUrl}/annotation-queues`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(Object.fromEntries(Object.entries(a).filter(([e,t])=>void 0!==t))),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await z(n,"create annotation queue"),await n.json()}async readAnnotationQueue(e){let t=await this.listAnnotationQueues({queueIds:[e]}).next();if(t.done)throw Error(`Annotation queue with ID ${e} not found`);return t.value}async updateAnnotationQueue(e,t){let{name:r,description:i}=t,a=await this.caller.call(A(),`${this.apiUrl}/annotation-queues/${M(e,"queueId")}`,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify({name:r,description:i}),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await z(a,"update annotation queue")}async deleteAnnotationQueue(e){let t=await this.caller.call(A(),`${this.apiUrl}/annotation-queues/${M(e,"queueId")}`,{method:"DELETE",headers:{...this.headers,Accept:"application/json"},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await z(t,"delete annotation queue")}async addRunsToAnnotationQueue(e,t){let r=await this.caller.call(A(),`${this.apiUrl}/annotation-queues/${M(e,"queueId")}/runs`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(t.map((e,t)=>M(e,`runIds[${t}]`).toString())),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await z(r,"add runs to annotation queue")}async getRunFromAnnotationQueue(e,t){let r=`/annotation-queues/${M(e,"queueId")}/run`,i=await this.caller.call(A(),`${this.apiUrl}${r}/${t}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await z(i,"get run from annotation queue"),await i.json()}async _currentTenantIsOwner(e){let t=await this._getSettings();return"-"==e||t.tenant_handle===e}async _ownerConflictError(e,t){let r=await this._getSettings();return Error(`Cannot ${e} for another tenant.

      Current tenant: ${r.tenant_handle}

      Requested tenant: ${t}`)}async _getLatestCommitHash(e){let t=await this.caller.call(A(),`${this.apiUrl}/commits/${e}/?limit=1&offset=0`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),r=await t.json();if(!t.ok){let e="string"==typeof r.detail?r.detail:JSON.stringify(r.detail),i=Error(`Error ${t.status}: ${t.statusText}
${e}`);throw i.statusCode=t.status,i}if(0!==r.commits.length)return r.commits[0].commit_hash}async _likeOrUnlikePrompt(e,t){let[r,i,a]=H(e),n=await this.caller.call(A(),`${this.apiUrl}/likes/${r}/${i}`,{method:"POST",body:JSON.stringify({like:t}),headers:{...this.headers,"Content-Type":"application/json"},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await z(n,`${t?"like":"unlike"} prompt`),await n.json()}async _getPromptUrl(e){let[t,r,i]=H(e);if(await this._currentTenantIsOwner(t)){let e=await this._getSettings();return"latest"!==i?`${this.getHostUrl()}/prompts/${r}/${i.substring(0,8)}?organizationId=${e.id}`:`${this.getHostUrl()}/prompts/${r}?organizationId=${e.id}`}return"latest"!==i?`${this.getHostUrl()}/hub/${t}/${r}/${i.substring(0,8)}`:`${this.getHostUrl()}/hub/${t}/${r}`}async promptExists(e){return!!await this.getPrompt(e)}async likePrompt(e){return this._likeOrUnlikePrompt(e,!0)}async unlikePrompt(e){return this._likeOrUnlikePrompt(e,!1)}async *listCommits(e){for await(let t of this._getPaginated(`/commits/${e}/`,new URLSearchParams,e=>e.commits))yield*t}async *listPrompts(e){let t=new URLSearchParams;for await(let r of(t.append("sort_field",e?.sortField??"updated_at"),t.append("sort_direction","desc"),t.append("is_archived",(!!e?.isArchived).toString()),e?.isPublic!==void 0&&t.append("is_public",e.isPublic.toString()),e?.query&&t.append("query",e.query),this._getPaginated("/repos",t,e=>e.repos)))yield*r}async getPrompt(e){let[t,r,i]=H(e),a=await this.caller.call(A(),`${this.apiUrl}/repos/${t}/${r}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});if(404===a.status)return null;await z(a,"get prompt");let n=await a.json();return n.repo?n.repo:null}async createPrompt(e,t){let r=await this._getSettings();if(t?.isPublic&&!r.tenant_handle)throw Error(`Cannot create a public prompt without first

        creating a LangChain Hub handle. 
        You can add a handle by creating a public prompt at:

        https://smith.langchain.com/prompts`);let[i,a,n]=H(e);if(!await this._currentTenantIsOwner(i))throw await this._ownerConflictError("create a prompt",i);let s={repo_handle:a,...t?.description&&{description:t.description},...t?.readme&&{readme:t.readme},...t?.tags&&{tags:t.tags},is_public:!!t?.isPublic},o=await this.caller.call(A(),`${this.apiUrl}/repos/`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(s),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await z(o,"create prompt");let{repo:l}=await o.json();return l}async createCommit(e,t,r){if(!await this.promptExists(e))throw Error("Prompt does not exist, you must create it first.");let[i,a,n]=H(e),s=r?.parentCommitHash!=="latest"&&r?.parentCommitHash?r?.parentCommitHash:await this._getLatestCommitHash(`${i}/${a}`),o={manifest:JSON.parse(JSON.stringify(t)),parent_commit:s},l=await this.caller.call(A(),`${this.apiUrl}/commits/${i}/${a}`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(o),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await z(l,"create commit");let u=await l.json();return this._getPromptUrl(`${i}/${a}${u.commit_hash?`:${u.commit_hash}`:""}`)}async updatePrompt(e,t){if(!await this.promptExists(e))throw Error("Prompt does not exist, you must create it first.");let[r,i]=H(e);if(!await this._currentTenantIsOwner(r))throw await this._ownerConflictError("update a prompt",r);let a={};if(t?.description!==void 0&&(a.description=t.description),t?.readme!==void 0&&(a.readme=t.readme),t?.tags!==void 0&&(a.tags=t.tags),t?.isPublic!==void 0&&(a.is_public=t.isPublic),t?.isArchived!==void 0&&(a.is_archived=t.isArchived),0===Object.keys(a).length)throw Error("No valid update options provided");let n=await this.caller.call(A(),`${this.apiUrl}/repos/${r}/${i}`,{method:"PATCH",body:JSON.stringify(a),headers:{...this.headers,"Content-Type":"application/json"},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await z(n,"update prompt"),n.json()}async deletePrompt(e){if(!await this.promptExists(e))throw Error("Prompt does not exist, you must create it first.");let[t,r,i]=H(e);if(!await this._currentTenantIsOwner(t))throw await this._ownerConflictError("delete a prompt",t);let a=await this.caller.call(A(),`${this.apiUrl}/repos/${t}/${r}`,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await a.json()}async pullPromptCommit(e,t){let[r,i,a]=H(e),n=function(e,t){let r=(0,F.parse)(e),i=(0,F.parse)(t);if(!r||!i)throw Error("Invalid version format.");return r.compare(i)>=0}((await this._getServerInfo()).version,"0.5.23"),s=a;if(!n&&"latest"===a){let e=await this._getLatestCommitHash(`${r}/${i}`);if(e)s=e;else throw Error("No commits found")}let o=await this.caller.call(A(),`${this.apiUrl}/commits/${r}/${i}/${s}${t?.includeModel?"?include_model=true":""}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await z(o,"pull prompt commit");let l=await o.json();return{owner:r,repo:i,commit_hash:l.commit_hash,manifest:l.manifest,examples:l.examples}}async _pullPrompt(e,t){return JSON.stringify((await this.pullPromptCommit(e,{includeModel:t?.includeModel})).manifest)}async pushPrompt(e,t){return(await this.promptExists(e)?t&&Object.keys(t).some(e=>"object"!==e)&&await this.updatePrompt(e,{description:t?.description,readme:t?.readme,tags:t?.tags,isPublic:t?.isPublic}):await this.createPrompt(e,{description:t?.description,readme:t?.readme,tags:t?.tags,isPublic:t?.isPublic}),t?.object)?await this.createCommit(e,t?.object,{parentCommitHash:t?.parentCommitHash}):await this._getPromptUrl(e)}async clonePublicDataset(e,t={}){let{sourceApiUrl:r=this.apiUrl,datasetName:i}=t,[a,n]=this.parseTokenOrUrl(e,r),s=new er({apiUrl:a,apiKey:"placeholder"}),o=await s.readSharedDataset(n),l=i||o.name;try{if(await this.hasDataset({datasetId:l}))return void console.log(`Dataset ${l} already exists in your tenant. Skipping.`)}catch(e){}let u=await s.listSharedExamples(n),c=await this.createDataset(l,{description:o.description,dataType:o.data_type||"kv",inputsSchema:o.inputs_schema_definition??void 0,outputsSchema:o.outputs_schema_definition??void 0});try{await this.createExamples({inputs:u.map(e=>e.inputs),outputs:u.flatMap(e=>e.outputs?[e.outputs]:[]),datasetId:c.id})}catch(e){throw console.error(`An error occurred while creating dataset ${l}. You should delete it manually.`),e}}parseTokenOrUrl(e,t,r=2,i="dataset"){try{return M(e),[t,e]}catch(e){}try{let a=new URL(e).pathname.split("/").filter(e=>""!==e);if(a.length>=r){let e=a[a.length-r];return[t,e]}throw Error(`Invalid public ${i} URL: ${e}`)}catch(t){throw Error(`Invalid public ${i} URL or token: ${e}`)}}awaitPendingTraceBatches(){return Promise.all(this.autoBatchQueue.items.map(({itemPromise:e})=>e))}}let ei="0.1.68",ea=()=>"undefined"!=typeof window&&void 0!==window.document,en=()=>"object"==typeof globalThis&&globalThis.constructor&&"DedicatedWorkerGlobalScope"===globalThis.constructor.name,es=()=>"undefined"!=typeof window&&"nodejs"===window.name||"undefined"!=typeof navigator&&(navigator.userAgent.includes("Node.js")||navigator.userAgent.includes("jsdom")),eo=()=>"undefined"!=typeof Deno,el=()=>"undefined"!=typeof process&&void 0!==process.versions&&void 0!==process.versions.node&&!eo(),eu=()=>i||(i=ea()?"browser":el()?"node":en()?"webworker":es()?"jsdom":eo()?"deno":"other");function ec(){return void 0===a&&(a={library:"langsmith",runtime:eu(),sdk:"langsmith-js",sdk_version:ei,...function(){if(void 0!==n)return n;let e={};for(let t of["VERCEL_GIT_COMMIT_SHA","NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA","COMMIT_REF","RENDER_GIT_COMMIT","CI_COMMIT_SHA","CIRCLE_SHA1","CF_PAGES_COMMIT_SHA","REACT_APP_GIT_SHA","SOURCE_VERSION","GITHUB_SHA","TRAVIS_COMMIT","GIT_COMMIT","BUILD_VCS_NUMBER","bamboo_planRepository_revision","Build.SourceVersion","BITBUCKET_COMMIT","DRONE_COMMIT_SHA","SEMAPHORE_GIT_SHA","BUILDKITE_COMMIT"]){let r=ed(t);void 0!==r&&(e[t]=r)}return n=e,e}()}),a}function ed(e){try{return"undefined"!=typeof process?process.env?.[e]:void 0}catch(e){return}}function eh(e){return ed(`LANGSMITH_${e}`)||ed(`LANGCHAIN_${e}`)}let ep=e=>void 0!==e?e:!!["TRACING_V2","TRACING"].find(e=>"true"===eh(e)),ef=Symbol.for("lc:context_variables");class em{constructor(e,t){Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.metadata=e,this.tags=t}static fromHeader(e){let t=e.split(","),r={},i=[];for(let e of t){let[t,a]=e.split("="),n=decodeURIComponent(a);"langsmith-metadata"===t?r=JSON.parse(n):"langsmith-tags"===t&&(i=n.split(","))}return new em(r,i)}toHeader(){let e=[];return this.metadata&&Object.keys(this.metadata).length>0&&e.push(`langsmith-metadata=${encodeURIComponent(JSON.stringify(this.metadata))}`),this.tags&&this.tags.length>0&&e.push(`langsmith-tags=${encodeURIComponent(this.tags.join(","))}`),e.join(",")}}class eg{constructor(e){if(Object.defineProperty(this,"id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"run_type",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"project_name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"parent_run",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"child_runs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"start_time",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"end_time",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"extra",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"error",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"serialized",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"inputs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"outputs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"reference_example_id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"client",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"events",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"trace_id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"dotted_order",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tracingEnabled",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"execution_order",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"child_execution_order",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),eb(e))return void Object.assign(this,{...e});let t=eg.getDefaultConfig(),{metadata:r,...i}=e,a=i.client??eg.getSharedClient(),n={...r,...i?.extra?.metadata};if(i.extra={...i.extra,metadata:n},Object.assign(this,{...t,...i,client:a}),this.trace_id||(this.parent_run?this.trace_id=this.parent_run.trace_id??this.id:this.trace_id=this.id),this.execution_order??=1,this.child_execution_order??=1,!this.dotted_order){let e=function(e,t,r=1){let i=r.toFixed(0).slice(0,3).padStart(3,"0");return`${new Date(e).toISOString().slice(0,-1)}${i}Z`.replace(/[-:.]/g,"")+t}(this.start_time,this.id,this.execution_order);this.parent_run?this.dotted_order=this.parent_run.dotted_order+"."+e:this.dotted_order=e}}static getDefaultConfig(){return{id:S(),run_type:"chain",project_name:ed("LANGCHAIN_PROJECT")??ed("LANGCHAIN_SESSION")??"default",child_runs:[],api_url:ed("LANGCHAIN_ENDPOINT")??"http://localhost:1984",api_key:ed("LANGCHAIN_API_KEY"),caller_options:{},start_time:Date.now(),serialized:{},inputs:{},extra:{}}}static getSharedClient(){return eg.sharedClient||(eg.sharedClient=new er),eg.sharedClient}createChild(e){var t,r;let i=this.child_execution_order+1,a=new eg({...e,parent_run:this,project_name:this.project_name,client:this.client,tracingEnabled:this.tracingEnabled,execution_order:i,child_execution_order:i});ef in this&&(a[ef]=this[ef]);let n=Symbol.for("lc:child_config"),s=e.extra?.[n]??this.extra[n];if(void 0!==(t=s)&&"object"==typeof t.callbacks&&(ev(t.callbacks?.handlers)||ev(t.callbacks))){let e={...s},t="object"==typeof(r=e.callbacks)&&null!=r&&Array.isArray(r.handlers)?e.callbacks.copy?.():void 0;t&&(Object.assign(t,{_parentRunId:a.id}),t.handlers?.find(ey)?.updateFromRunTree?.(a),e.callbacks=t),a.extra[n]=e}let o=new Set,l=this;for(;null!=l&&!o.has(l.id);)o.add(l.id),l.child_execution_order=Math.max(l.child_execution_order,i),l=l.parent_run;return this.child_runs.push(a),a}async end(e,t,r=Date.now(),i){this.outputs=this.outputs??e,this.error=this.error??t,this.end_time=this.end_time??r,i&&Object.keys(i).length>0&&(this.extra=this.extra?{...this.extra,metadata:{...this.extra.metadata,...i}}:{metadata:i})}_convertToCreate(e,t,r=!0){let i,a,n=e.extra??{};if(n.runtime||(n.runtime={}),t)for(let[e,r]of Object.entries(t))n.runtime[e]||(n.runtime[e]=r);return r?(a=e.parent_run?.id,i=[]):(i=e.child_runs.map(e=>this._convertToCreate(e,t,r)),a=void 0),{id:e.id,name:e.name,start_time:e.start_time,end_time:e.end_time,run_type:e.run_type,reference_example_id:e.reference_example_id,extra:n,serialized:e.serialized,error:e.error,inputs:e.inputs,outputs:e.outputs,session_name:e.project_name,child_runs:i,parent_run_id:a,trace_id:e.trace_id,dotted_order:e.dotted_order,tags:e.tags}}async postRun(e=!0){try{let t=ec(),r=await this._convertToCreate(this,t,!0);if(await this.client.createRun(r),!e)for(let e of(U("Posting with excludeChildRuns=false is deprecated and will be removed in a future version."),this.child_runs))await e.postRun(!1)}catch(e){console.error(`Error in postRun for run ${this.id}:`,e)}}async patchRun(){try{let e={end_time:this.end_time,error:this.error,inputs:this.inputs,outputs:this.outputs,parent_run_id:this.parent_run?.id,reference_example_id:this.reference_example_id,extra:this.extra,events:this.events,dotted_order:this.dotted_order,trace_id:this.trace_id,tags:this.tags};await this.client.updateRun(this.id,e)}catch(e){console.error(`Error in patchRun for run ${this.id}`,e)}}toJSON(){return this._convertToCreate(this,void 0,!1)}static fromRunnableConfig(e,t){let r,i,a,n=e?.callbacks,s=ep();if(n){let e=n?.getParentRunId?.()??"",t=n?.handlers?.find(e=>e?.name=="langchain_tracer");r=t?.getRun?.(e),i=t?.projectName,a=t?.client,s=s||!!t}return r?new eg({name:r.name,id:r.id,trace_id:r.trace_id,dotted_order:r.dotted_order,client:a,tracingEnabled:s,project_name:i,tags:[...new Set((r?.tags??[]).concat(e?.tags??[]))],extra:{metadata:{...r?.extra?.metadata,...e?.metadata}}}).createChild(t):new eg({...t,client:a,tracingEnabled:s,project_name:i})}static fromDottedOrder(e){return this.fromHeaders({"langsmith-trace":e})}static fromHeaders(e,t){let r="get"in e&&"function"==typeof e.get?{"langsmith-trace":e.get("langsmith-trace"),baggage:e.get("baggage")}:e,i=r["langsmith-trace"];if(!i||"string"!=typeof i)return;let a=i.trim(),n=a.split(".").map(e=>{let[t,r]=e.split("Z");return{strTime:t,time:Date.parse(t+"Z"),uuid:r}}),s=n[0].uuid,o={...t,name:t?.name??"parent",run_type:t?.run_type??"chain",start_time:t?.start_time??Date.now(),id:n.at(-1)?.uuid,trace_id:s,dotted_order:a};if(r.baggage&&"string"==typeof r.baggage){let e=em.fromHeader(r.baggage);o.metadata=e.metadata,o.tags=e.tags}return new eg(o)}toHeaders(e){let t={"langsmith-trace":this.dotted_order,baggage:new em(this.extra?.metadata,this.tags).toHeader()};if(e)for(let[r,i]of Object.entries(t))e.set(r,i);return t}}function eb(e){return void 0!==e&&"function"==typeof e.createChild&&"function"==typeof e.postRun}function ey(e){return"object"==typeof e&&null!=e&&"string"==typeof e.name&&"langchain_tracer"===e.name}function ev(e){return Array.isArray(e)&&e.some(e=>ey(e))}Object.defineProperty(eg,"sharedClient",{enumerable:!0,configurable:!0,writable:!0,value:null});class ew{getStore(){}run(e,t){return t()}}let e_=Symbol.for("ls:tracing_async_local_storage"),eE=new ew;class eO{getInstance(){return globalThis[e_]??eE}initializeGlobalInstance(e){void 0===globalThis[e_]&&(globalThis[e_]=e)}}let eI=new eO,eS=()=>{let e=eI.getInstance().getStore();if(!eb(e))throw Error("Could not get the current run tree.\n\nPlease make sure you are calling this method within a traceable function or the tracing is enabled.");return e};function eT(e){return"function"==typeof e&&"langsmith:traceable"in e}Symbol.for("langsmith:traceable:root");let eP=Object.prototype.hasOwnProperty;function e$(e){switch(typeof e){case"object":return JSON.parse(JSON.stringify(e));case"undefined":return null;default:return e}}function eA(e){let t,r=0,i=e.length;for(;r<i;){if((t=e.charCodeAt(r))>=48&&t<=57){r++;continue}return!1}return!0}function eR(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}function ej(e,t){let r=[e];for(let e in t){let i="object"==typeof t[e]?JSON.stringify(t[e],null,2):t[e];void 0!==i&&r.push(`${e}: ${i}`)}return r.join("\n")}class ex extends Error{constructor(e,t,r,i,a){super(ej(e,{name:t,index:r,operation:i,tree:a})),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:t}),Object.defineProperty(this,"index",{enumerable:!0,configurable:!0,writable:!0,value:r}),Object.defineProperty(this,"operation",{enumerable:!0,configurable:!0,writable:!0,value:i}),Object.defineProperty(this,"tree",{enumerable:!0,configurable:!0,writable:!0,value:a}),Object.setPrototypeOf(this,new.target.prototype),this.message=ej(e,{name:t,index:r,operation:i,tree:a})}}let ek=ex,eN=e$,eC={add:function(e,t,r){return e[t]=this.value,{newDocument:r}},remove:function(e,t,r){var i=e[t];return delete e[t],{newDocument:r,removed:i}},replace:function(e,t,r){var i=e[t];return e[t]=this.value,{newDocument:r,removed:i}},move:function(e,t,r){let i=eM(r,this.path);i&&(i=e$(i));let a=eD(r,{op:"remove",path:this.from}).removed;return eD(r,{op:"add",path:this.path,value:a}),{newDocument:r,removed:i}},copy:function(e,t,r){let i=eM(r,this.from);return eD(r,{op:"add",path:this.path,value:e$(i)}),{newDocument:r}},test:function(e,t,r){return{newDocument:r,test:ez(e[t],this.value)}},_get:function(e,t,r){return this.value=e[t],{newDocument:r}}};var eL={add:function(e,t,r){return eA(t)?e.splice(t,0,this.value):e[t]=this.value,{newDocument:r,index:t}},remove:function(e,t,r){return{newDocument:r,removed:e.splice(t,1)[0]}},replace:function(e,t,r){var i=e[t];return e[t]=this.value,{newDocument:r,removed:i}},move:eC.move,copy:eC.copy,test:eC.test,_get:eC._get};function eM(e,t){if(""==t)return e;var r={op:"_get",path:t};return eD(e,r),r.value}function eD(e,t,r=!1,i=!0,a=!0,n=0){if(r&&("function"==typeof r?r(t,0,e,t.path):eH(t,0)),""===t.path){let i={newDocument:e};if("add"===t.op)return i.newDocument=t.value,i;if("replace"===t.op)return i.newDocument=t.value,i.removed=e,i;if("move"===t.op||"copy"===t.op)return i.newDocument=eM(e,t.from),"move"===t.op&&(i.removed=e),i;else if("test"===t.op){if(i.test=ez(e,t.value),!1===i.test)throw new ek("Test operation failed","TEST_OPERATION_FAILED",n,t,e);return i.newDocument=e,i}else if("remove"===t.op)return i.removed=e,i.newDocument=null,i;else if("_get"===t.op)return t.value=e,i;else if(!r)return i;else throw new ek("Operation `op` property is not one of operations defined in RFC-6902","OPERATION_OP_INVALID",n,t,e)}{let s,o,l;i||(e=e$(e));let u=(t.path||"").split("/"),c=e,d=1,h=u.length;for(o="function"==typeof r?r:eH;;){if((s=u[d])&&-1!=s.indexOf("~")&&(s=eR(s)),a&&("__proto__"==s||"prototype"==s&&d>0&&"constructor"==u[d-1]))throw TypeError("JSON-Patch: modifying `__proto__` or `constructor/prototype` prop is banned for security reasons, if this was on purpose, please set `banPrototypeModifications` flag false and pass it to this function. More info in fast-json-patch README");if(r&&void 0===l&&(void 0===c[s]?l=u.slice(0,d).join("/"):d==h-1&&(l=t.path),void 0!==l&&o(t,0,e,l)),d++,Array.isArray(c)){if("-"===s)s=c.length;else if(r&&!eA(s))throw new ek("Expected an unsigned base-10 integer value, making the new referenced value the array element with the zero-based index","OPERATION_PATH_ILLEGAL_ARRAY_INDEX",n,t,e);else eA(s)&&(s=~~s);if(d>=h){if(r&&"add"===t.op&&s>c.length)throw new ek("The specified index MUST NOT be greater than the number of elements in the array","OPERATION_VALUE_OUT_OF_BOUNDS",n,t,e);let i=eL[t.op].call(t,c,s,e);if(!1===i.test)throw new ek("Test operation failed","TEST_OPERATION_FAILED",n,t,e);return i}}else if(d>=h){let r=eC[t.op].call(t,c,s,e);if(!1===r.test)throw new ek("Test operation failed","TEST_OPERATION_FAILED",n,t,e);return r}if(c=c[s],r&&d<h&&(!c||"object"!=typeof c))throw new ek("Cannot perform operation at the desired path","OPERATION_PATH_UNRESOLVABLE",n,t,e)}}}function eU(e,t,r,i=!0,a=!0){if(r&&!Array.isArray(t))throw new ek("Patch sequence must be an array","SEQUENCE_NOT_AN_ARRAY");i||(e=e$(e));let n=Array(t.length);for(let i=0,s=t.length;i<s;i++)n[i]=eD(e,t[i],r,!0,a,i),e=n[i].newDocument;return n.newDocument=e,n}function eF(e,t,r){let i=eD(e,t);if(!1===i.test)throw new ek("Test operation failed","TEST_OPERATION_FAILED",r,t,e);return i.newDocument}function eH(e,t,r,i){if("object"!=typeof e||null===e||Array.isArray(e))throw new ek("Operation is not an object","OPERATION_NOT_AN_OBJECT",t,e,r);if(eC[e.op]){if("string"!=typeof e.path)throw new ek("Operation `path` property is not a string","OPERATION_PATH_INVALID",t,e,r);else if(0!==e.path.indexOf("/")&&e.path.length>0)throw new ek('Operation `path` property must start with "/"',"OPERATION_PATH_INVALID",t,e,r);else if(("move"===e.op||"copy"===e.op)&&"string"!=typeof e.from)throw new ek("Operation `from` property is not present (applicable in `move` and `copy` operations)","OPERATION_FROM_REQUIRED",t,e,r);else if(("add"===e.op||"replace"===e.op||"test"===e.op)&&void 0===e.value)throw new ek("Operation `value` property is not present (applicable in `add`, `replace` and `test` operations)","OPERATION_VALUE_REQUIRED",t,e,r);else if(("add"===e.op||"replace"===e.op||"test"===e.op)&&function e(t){if(void 0===t)return!0;if(t){if(Array.isArray(t)){for(let r=0,i=t.length;r<i;r++)if(e(t[r]))return!0}else if("object"==typeof t){let i=function(e){if(Array.isArray(e)){let t=Array(e.length);for(let e=0;e<t.length;e++)t[e]=""+e;return t}if(Object.keys)return Object.keys(e);let t=[];for(let r in e)eP.call(e,r)&&t.push(r);return t}(t),a=i.length;for(var r=0;r<a;r++)if(e(t[i[r]]))return!0}}return!1}(e.value))throw new ek("Operation `value` property is not present (applicable in `add`, `replace` and `test` operations)","OPERATION_VALUE_CANNOT_CONTAIN_UNDEFINED",t,e,r);else if(r){if("add"==e.op){var a=e.path.split("/").length,n=i.split("/").length;if(a!==n+1&&a!==n)throw new ek("Cannot perform an `add` operation at the desired path","OPERATION_PATH_CANNOT_ADD",t,e,r)}else if("replace"===e.op||"remove"===e.op||"_get"===e.op){if(e.path!==i)throw new ek("Cannot perform the operation at a path that does not exist","OPERATION_PATH_UNRESOLVABLE",t,e,r)}else if("move"===e.op||"copy"===e.op){var s=eB([{op:"_get",path:e.from,value:void 0}],r);if(s&&"OPERATION_PATH_UNRESOLVABLE"===s.name)throw new ek("Cannot perform the operation from a path that does not exist","OPERATION_FROM_UNRESOLVABLE",t,e,r)}}}else throw new ek("Operation `op` property is not one of operations defined in RFC-6902","OPERATION_OP_INVALID",t,e,r)}function eB(e,t,r){try{if(!Array.isArray(e))throw new ek("Patch sequence must be an array","SEQUENCE_NOT_AN_ARRAY");if(t)eU(e$(t),e$(e),r||!0);else{r=r||eH;for(var i=0;i<e.length;i++)r(e[i],i,t,void 0)}}catch(e){if(e instanceof ek)return e;throw e}}function ez(e,t){if(e===t)return!0;if(e&&t&&"object"==typeof e&&"object"==typeof t){var r,i,a,n=Array.isArray(e),s=Array.isArray(t);if(n&&s){if((i=e.length)!=t.length)return!1;for(r=i;0!=r--;)if(!ez(e[r],t[r]))return!1;return!0}if(n!=s)return!1;var o=Object.keys(e);if((i=o.length)!==Object.keys(t).length)return!1;for(r=i;0!=r--;)if(!t.hasOwnProperty(o[r]))return!1;for(r=i;0!=r--;)if(!ez(e[a=o[r]],t[a]))return!1;return!0}return e!=e&&t!=t}var eG=new WeakMap;({...u,JsonPatchError:ex,deepClone:e$,escapePathComponent:function(e){return -1===e.indexOf("/")&&-1===e.indexOf("~")?e:e.replace(/~/g,"~0").replace(/\//g,"~1")},unescapePathComponent:eR});var eq=r(57543);function eJ(e,t){return t?.[e]||eq(e)}function eZ(e){return Array.isArray(e)?[...e]:{...e}}function eY(e){let t=Object.getPrototypeOf(e);return"function"==typeof e.lc_name&&("function"!=typeof t.lc_name||e.lc_name()!==t.lc_name())?e.lc_name():e.name}r(51862);class eW{static lc_name(){return this.name}get lc_id(){return[...this.lc_namespace,eY(this.constructor)]}get lc_secrets(){}get lc_attributes(){}get lc_aliases(){}constructor(e,...t){Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"lc_kwargs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.lc_kwargs=e||{}}toJSON(){if(!this.lc_serializable||this.lc_kwargs instanceof eW||"object"!=typeof this.lc_kwargs||Array.isArray(this.lc_kwargs))return this.toJSONNotImplemented();let e={},t={},r=Object.keys(this.lc_kwargs).reduce((e,t)=>(e[t]=t in this?this[t]:this.lc_kwargs[t],e),{});for(let i=Object.getPrototypeOf(this);i;i=Object.getPrototypeOf(i))Object.assign(e,Reflect.get(i,"lc_aliases",this)),Object.assign(t,Reflect.get(i,"lc_secrets",this)),Object.assign(r,Reflect.get(i,"lc_attributes",this));return Object.keys(t).forEach(e=>{let t=this,i=r,[a,...n]=e.split(".").reverse();for(let e of n.reverse()){if(!(e in t)||void 0===t[e])return;e in i&&void 0!==i[e]||("object"==typeof t[e]&&null!=t[e]?i[e]={}:Array.isArray(t[e])&&(i[e]=[])),t=t[e],i=i[e]}a in t&&void 0!==t[a]&&(i[a]=i[a]||t[a])}),{lc:1,type:"constructor",id:this.lc_id,kwargs:function(e,t,r){let i={};for(let a in e)Object.hasOwn(e,a)&&(i[t(a,r)]=e[a]);return i}(Object.keys(t).length?function(e,t){let r=eZ(e);for(let[e,i]of Object.entries(t)){let[t,...a]=e.split(".").reverse(),n=r;for(let e of a.reverse()){if(void 0===n[e])break;n[e]=eZ(n[e]),n=n[e]}void 0!==n[t]&&(n[t]={lc:1,type:"secret",id:[i]})}return r}(r,t):r,eJ,e)}}toJSONNotImplemented(){return{lc:1,type:"not_implemented",id:this.lc_id}}}let eV=()=>"undefined"!=typeof window&&void 0!==window.document,eK=()=>"object"==typeof globalThis&&globalThis.constructor&&"DedicatedWorkerGlobalScope"===globalThis.constructor.name,eX=()=>"undefined"!=typeof window&&"nodejs"===window.name||"undefined"!=typeof navigator&&(navigator.userAgent.includes("Node.js")||navigator.userAgent.includes("jsdom")),eQ=()=>"undefined"!=typeof Deno,e0=()=>"undefined"!=typeof process&&void 0!==process.versions&&void 0!==process.versions.node&&!eQ(),e1=()=>{let e;return eV()?"browser":e0()?"node":eK()?"webworker":eX()?"jsdom":eQ()?"deno":"other"};async function e4(){return void 0===s&&(s={library:"langchain-js",runtime:e1()}),s}function e3(e){try{return"undefined"!=typeof process?process.env?.[e]:void 0}catch(e){return}}class e2{}class e9 extends e2{get lc_namespace(){return["langchain_core","callbacks",this.name]}get lc_secrets(){}get lc_attributes(){}get lc_aliases(){}static lc_name(){return this.name}get lc_id(){return[...this.lc_namespace,eY(this.constructor)]}constructor(e){super(),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"lc_kwargs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"ignoreLLM",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"ignoreChain",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"ignoreAgent",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"ignoreRetriever",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"ignoreCustomEvent",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"raiseError",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"awaitHandlers",{enumerable:!0,configurable:!0,writable:!0,value:"true"!==e3("LANGCHAIN_CALLBACKS_BACKGROUND")}),this.lc_kwargs=e||{},e&&(this.ignoreLLM=e.ignoreLLM??this.ignoreLLM,this.ignoreChain=e.ignoreChain??this.ignoreChain,this.ignoreAgent=e.ignoreAgent??this.ignoreAgent,this.ignoreRetriever=e.ignoreRetriever??this.ignoreRetriever,this.ignoreCustomEvent=e.ignoreCustomEvent??this.ignoreCustomEvent,this.raiseError=e.raiseError??this.raiseError,this.awaitHandlers=this.raiseError||(e._awaitHandler??this.awaitHandlers))}copy(){return new this.constructor(this)}toJSON(){return eW.prototype.toJSON.call(this)}toJSONNotImplemented(){return eW.prototype.toJSONNotImplemented.call(this)}static fromMethods(e){class t extends e9{constructor(){super(),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:S()}),Object.assign(this,e)}}return new t}}function e6(e,t){return e&&!Array.isArray(e)&&"object"==typeof e?e:{[t]:e}}function e5(e){return"function"==typeof e._addRunToRunMap}class e8 extends e9{constructor(e){super(...arguments),Object.defineProperty(this,"runMap",{enumerable:!0,configurable:!0,writable:!0,value:new Map})}copy(){return this}stringifyError(e){return e instanceof Error?e.message+(e?.stack?`

${e.stack}`:""):"string"==typeof e?e:`${e}`}_addChildRun(e,t){e.child_runs.push(t)}_addRunToRunMap(e){let t=function(e,t,r){let i=r.toFixed(0).slice(0,3).padStart(3,"0");return`${new Date(e).toISOString().slice(0,-1)}${i}Z`.replace(/[-:.]/g,"")+t}(e.start_time,e.id,e.execution_order),r={...e};if(void 0!==r.parent_run_id){let e=this.runMap.get(r.parent_run_id);e&&(this._addChildRun(e,r),e.child_execution_order=Math.max(e.child_execution_order,r.child_execution_order),r.trace_id=e.trace_id,void 0!==e.dotted_order&&(r.dotted_order=[e.dotted_order,t].join(".")))}else r.trace_id=r.id,r.dotted_order=t;return this.runMap.set(r.id,r),r}async _endTrace(e){let t=void 0!==e.parent_run_id&&this.runMap.get(e.parent_run_id);t?t.child_execution_order=Math.max(t.child_execution_order,e.child_execution_order):await this.persistRun(e),this.runMap.delete(e.id),await this.onRunUpdate?.(e)}_getExecutionOrder(e){let t=void 0!==e&&this.runMap.get(e);return t?t.child_execution_order+1:1}_createRunForLLMStart(e,t,r,i,a,n,s,o){let l=this._getExecutionOrder(i),u=Date.now(),c=s?{...a,metadata:s}:a,d={id:r,name:o??e.id[e.id.length-1],parent_run_id:i,start_time:u,serialized:e,events:[{name:"start",time:new Date(u).toISOString()}],inputs:{prompts:t},execution_order:l,child_runs:[],child_execution_order:l,run_type:"llm",extra:c??{},tags:n||[]};return this._addRunToRunMap(d)}async handleLLMStart(e,t,r,i,a,n,s,o){let l=this.runMap.get(r)??this._createRunForLLMStart(e,t,r,i,a,n,s,o);return await this.onRunCreate?.(l),await this.onLLMStart?.(l),l}_createRunForChatModelStart(e,t,r,i,a,n,s,o){let l=this._getExecutionOrder(i),u=Date.now(),c=s?{...a,metadata:s}:a,d={id:r,name:o??e.id[e.id.length-1],parent_run_id:i,start_time:u,serialized:e,events:[{name:"start",time:new Date(u).toISOString()}],inputs:{messages:t},execution_order:l,child_runs:[],child_execution_order:l,run_type:"llm",extra:c??{},tags:n||[]};return this._addRunToRunMap(d)}async handleChatModelStart(e,t,r,i,a,n,s,o){let l=this.runMap.get(r)??this._createRunForChatModelStart(e,t,r,i,a,n,s,o);return await this.onRunCreate?.(l),await this.onLLMStart?.(l),l}async handleLLMEnd(e,t){let r=this.runMap.get(t);if(!r||r?.run_type!=="llm")throw Error("No LLM run to end.");return r.end_time=Date.now(),r.outputs=e,r.events.push({name:"end",time:new Date(r.end_time).toISOString()}),await this.onLLMEnd?.(r),await this._endTrace(r),r}async handleLLMError(e,t){let r=this.runMap.get(t);if(!r||r?.run_type!=="llm")throw Error("No LLM run to end.");return r.end_time=Date.now(),r.error=this.stringifyError(e),r.events.push({name:"error",time:new Date(r.end_time).toISOString()}),await this.onLLMError?.(r),await this._endTrace(r),r}_createRunForChainStart(e,t,r,i,a,n,s,o){let l=this._getExecutionOrder(i),u=Date.now(),c={id:r,name:o??e.id[e.id.length-1],parent_run_id:i,start_time:u,serialized:e,events:[{name:"start",time:new Date(u).toISOString()}],inputs:t,execution_order:l,child_execution_order:l,run_type:s??"chain",child_runs:[],extra:n?{metadata:n}:{},tags:a||[]};return this._addRunToRunMap(c)}async handleChainStart(e,t,r,i,a,n,s,o){let l=this.runMap.get(r)??this._createRunForChainStart(e,t,r,i,a,n,s,o);return await this.onRunCreate?.(l),await this.onChainStart?.(l),l}async handleChainEnd(e,t,r,i,a){let n=this.runMap.get(t);if(!n)throw Error("No chain run to end.");return n.end_time=Date.now(),n.outputs=e6(e,"output"),n.events.push({name:"end",time:new Date(n.end_time).toISOString()}),a?.inputs!==void 0&&(n.inputs=e6(a.inputs,"input")),await this.onChainEnd?.(n),await this._endTrace(n),n}async handleChainError(e,t,r,i,a){let n=this.runMap.get(t);if(!n)throw Error("No chain run to end.");return n.end_time=Date.now(),n.error=this.stringifyError(e),n.events.push({name:"error",time:new Date(n.end_time).toISOString()}),a?.inputs!==void 0&&(n.inputs=e6(a.inputs,"input")),await this.onChainError?.(n),await this._endTrace(n),n}_createRunForToolStart(e,t,r,i,a,n,s){let o=this._getExecutionOrder(i),l=Date.now(),u={id:r,name:s??e.id[e.id.length-1],parent_run_id:i,start_time:l,serialized:e,events:[{name:"start",time:new Date(l).toISOString()}],inputs:{input:t},execution_order:o,child_execution_order:o,run_type:"tool",child_runs:[],extra:n?{metadata:n}:{},tags:a||[]};return this._addRunToRunMap(u)}async handleToolStart(e,t,r,i,a,n,s){let o=this.runMap.get(r)??this._createRunForToolStart(e,t,r,i,a,n,s);return await this.onRunCreate?.(o),await this.onToolStart?.(o),o}async handleToolEnd(e,t){let r=this.runMap.get(t);if(!r||r?.run_type!=="tool")throw Error("No tool run to end");return r.end_time=Date.now(),r.outputs={output:e},r.events.push({name:"end",time:new Date(r.end_time).toISOString()}),await this.onToolEnd?.(r),await this._endTrace(r),r}async handleToolError(e,t){let r=this.runMap.get(t);if(!r||r?.run_type!=="tool")throw Error("No tool run to end");return r.end_time=Date.now(),r.error=this.stringifyError(e),r.events.push({name:"error",time:new Date(r.end_time).toISOString()}),await this.onToolError?.(r),await this._endTrace(r),r}async handleAgentAction(e,t){let r=this.runMap.get(t);r&&r?.run_type==="chain"&&(r.actions=r.actions||[],r.actions.push(e),r.events.push({name:"agent_action",time:new Date().toISOString(),kwargs:{action:e}}),await this.onAgentAction?.(r))}async handleAgentEnd(e,t){let r=this.runMap.get(t);r&&r?.run_type==="chain"&&(r.events.push({name:"agent_end",time:new Date().toISOString(),kwargs:{action:e}}),await this.onAgentEnd?.(r))}_createRunForRetrieverStart(e,t,r,i,a,n,s){let o=this._getExecutionOrder(i),l=Date.now(),u={id:r,name:s??e.id[e.id.length-1],parent_run_id:i,start_time:l,serialized:e,events:[{name:"start",time:new Date(l).toISOString()}],inputs:{query:t},execution_order:o,child_execution_order:o,run_type:"retriever",child_runs:[],extra:n?{metadata:n}:{},tags:a||[]};return this._addRunToRunMap(u)}async handleRetrieverStart(e,t,r,i,a,n,s){let o=this.runMap.get(r)??this._createRunForRetrieverStart(e,t,r,i,a,n,s);return await this.onRunCreate?.(o),await this.onRetrieverStart?.(o),o}async handleRetrieverEnd(e,t){let r=this.runMap.get(t);if(!r||r?.run_type!=="retriever")throw Error("No retriever run to end");return r.end_time=Date.now(),r.outputs={documents:e},r.events.push({name:"end",time:new Date(r.end_time).toISOString()}),await this.onRetrieverEnd?.(r),await this._endTrace(r),r}async handleRetrieverError(e,t){let r=this.runMap.get(t);if(!r||r?.run_type!=="retriever")throw Error("No retriever run to end");return r.end_time=Date.now(),r.error=this.stringifyError(e),r.events.push({name:"error",time:new Date(r.end_time).toISOString()}),await this.onRetrieverError?.(r),await this._endTrace(r),r}async handleText(e,t){let r=this.runMap.get(t);r&&r?.run_type==="chain"&&(r.events.push({name:"text",time:new Date().toISOString(),kwargs:{text:e}}),await this.onText?.(r))}async handleLLMNewToken(e,t,r,i,a,n){let s=this.runMap.get(r);if(!s||s?.run_type!=="llm")throw Error('Invalid "runId" provided to "handleLLMNewToken" callback.');return s.events.push({name:"new_token",time:new Date().toISOString(),kwargs:{token:e,idx:t,chunk:n?.chunk}}),await this.onLLMNewToken?.(s,e,{chunk:n?.chunk}),s}}var e7=r(63391);function te(e,t){return`${e.open}${t}${e.close}`}function tt(e,t){try{return JSON.stringify(e,null,2)}catch(e){return t}}function tr(e){return"string"==typeof e?e.trim():null==e?e:tt(e,e.toString())}function ti(e){if(!e.end_time)return"";let t=e.end_time-e.start_time;return t<1e3?`${t}ms`:`${(t/1e3).toFixed(2)}s`}let{color:ta}=e7;class tn extends e8{constructor(){super(...arguments),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"console_callback_handler"})}persistRun(e){return Promise.resolve()}getParents(e){let t=[],r=e;for(;r.parent_run_id;){let e=this.runMap.get(r.parent_run_id);if(e)t.push(e),r=e;else break}return t}getBreadcrumbs(e){let t=[...this.getParents(e).reverse(),e].map((e,t,r)=>{let i=`${e.execution_order}:${e.run_type}:${e.name}`;return t===r.length-1?te(e7.bold,i):i}).join(" > ");return te(ta.grey,t)}onChainStart(e){let t=this.getBreadcrumbs(e);console.log(`${te(ta.green,"[chain/start]")} [${t}] Entering Chain run with input: ${tt(e.inputs,"[inputs]")}`)}onChainEnd(e){let t=this.getBreadcrumbs(e);console.log(`${te(ta.cyan,"[chain/end]")} [${t}] [${ti(e)}] Exiting Chain run with output: ${tt(e.outputs,"[outputs]")}`)}onChainError(e){let t=this.getBreadcrumbs(e);console.log(`${te(ta.red,"[chain/error]")} [${t}] [${ti(e)}] Chain run errored with error: ${tt(e.error,"[error]")}`)}onLLMStart(e){let t=this.getBreadcrumbs(e),r="prompts"in e.inputs?{prompts:e.inputs.prompts.map(e=>e.trim())}:e.inputs;console.log(`${te(ta.green,"[llm/start]")} [${t}] Entering LLM run with input: ${tt(r,"[inputs]")}`)}onLLMEnd(e){let t=this.getBreadcrumbs(e);console.log(`${te(ta.cyan,"[llm/end]")} [${t}] [${ti(e)}] Exiting LLM run with output: ${tt(e.outputs,"[response]")}`)}onLLMError(e){let t=this.getBreadcrumbs(e);console.log(`${te(ta.red,"[llm/error]")} [${t}] [${ti(e)}] LLM run errored with error: ${tt(e.error,"[error]")}`)}onToolStart(e){let t=this.getBreadcrumbs(e);console.log(`${te(ta.green,"[tool/start]")} [${t}] Entering Tool run with input: "${tr(e.inputs.input)}"`)}onToolEnd(e){let t=this.getBreadcrumbs(e);console.log(`${te(ta.cyan,"[tool/end]")} [${t}] [${ti(e)}] Exiting Tool run with output: "${tr(e.outputs?.output)}"`)}onToolError(e){let t=this.getBreadcrumbs(e);console.log(`${te(ta.red,"[tool/error]")} [${t}] [${ti(e)}] Tool run errored with error: ${tt(e.error,"[error]")}`)}onRetrieverStart(e){let t=this.getBreadcrumbs(e);console.log(`${te(ta.green,"[retriever/start]")} [${t}] Entering Retriever run with input: ${tt(e.inputs,"[inputs]")}`)}onRetrieverEnd(e){let t=this.getBreadcrumbs(e);console.log(`${te(ta.cyan,"[retriever/end]")} [${t}] [${ti(e)}] Exiting Retriever run with output: ${tt(e.outputs,"[outputs]")}`)}onRetrieverError(e){let t=this.getBreadcrumbs(e);console.log(`${te(ta.red,"[retriever/error]")} [${t}] [${ti(e)}] Retriever run errored with error: ${tt(e.error,"[error]")}`)}onAgentAction(e){let t=this.getBreadcrumbs(e);console.log(`${te(ta.blue,"[agent/action]")} [${t}] Agent selected action: ${tt(e.actions[e.actions.length-1],"[action]")}`)}}class ts extends Error{constructor(e,t){super(e),Object.defineProperty(this,"output",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.output=t}}class to extends eW{get lc_aliases(){return{additional_kwargs:"additional_kwargs",response_metadata:"response_metadata"}}get text(){return"string"==typeof this.content?this.content:""}constructor(e,t){"string"==typeof e&&(e={content:e,additional_kwargs:t,response_metadata:{}}),e.additional_kwargs||(e.additional_kwargs={}),e.response_metadata||(e.response_metadata={}),super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","messages"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"content",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"additional_kwargs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"response_metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name=e.name,this.content=e.content,this.additional_kwargs=e.additional_kwargs,this.response_metadata=e.response_metadata,this.id=e.id}toDict(){return{type:this._getType(),data:this.toJSON().kwargs}}static lc_name(){return"BaseMessage"}get _printableFields(){return{id:this.id,content:this.content,name:this.name,additional_kwargs:this.additional_kwargs,response_metadata:this.response_metadata}}_updateId(e){this.id=e,this.lc_kwargs.id=e}get[Symbol.toStringTag](){return this.constructor.lc_name()}[Symbol.for("nodejs.util.inspect.custom")](e){var t,r;if(null===e)return this;let i=(t=this._printableFields,r=Math.max(4,e),JSON.stringify(function e(t,i){if("object"!=typeof t||null==t)return t;if(i>=r)return Array.isArray(t)?"[Array]":"[Object]";if(Array.isArray(t))return t.map(t=>e(t,i+1));let a={};for(let r of Object.keys(t))a[r]=e(t[r],i+1);return a}(t,0),null,2));return`${this.constructor.lc_name()} ${i}`}}function tl(e,t){let r={...e};for(let[e,i]of Object.entries(t))if(null==r[e])r[e]=i;else if(null==i)continue;else if(typeof r[e]!=typeof i||Array.isArray(r[e])!==Array.isArray(i))throw Error(`field[${e}] already exists in the message chunk, but with a different type.`);else if("string"==typeof r[e]){if("type"===e)continue;r[e]+=i}else if("object"!=typeof r[e]||Array.isArray(r[e]))if(Array.isArray(r[e]))r[e]=tu(r[e],i);else{if(r[e]===i)continue;console.warn(`field[${e}] already exists in this message chunk and value has unsupported type.`)}else r[e]=tl(r[e],i);return r}function tu(e,t){if(void 0!==e||void 0!==t){if(void 0===e||void 0===t)return e||t;let r=[...e];for(let e of t)if("object"==typeof e&&"index"in e&&"number"==typeof e.index){let t=r.findIndex(t=>t.index===e.index);-1!==t?r[t]=tl(r[t],e):r.push(e)}else{if("object"==typeof e&&"text"in e&&""===e.text)continue;r.push(e)}return r}}class tc extends to{}class td extends tc{constructor(e){let t;if("string"==typeof e)t={content:e,tool_calls:[],invalid_tool_calls:[],tool_call_chunks:[]};else if(void 0===e.tool_call_chunks)t={...e,tool_calls:e.tool_calls??[],invalid_tool_calls:[],tool_call_chunks:[]};else{let r=[],i=[];for(let t of e.tool_call_chunks){let e={};try{if(e=function(e){if(void 0===e)return null;try{return JSON.parse(e)}catch(e){}let t="",r=[],i=!1,a=!1;for(let n of e){if(i)'"'!==n||a?"\n"!==n||a?a="\\"===n&&!a:n="\\n":i=!1;else if('"'===n)i=!0,a=!1;else if("{"===n)r.push("}");else if("["===n)r.push("]");else if("}"===n||"]"===n)if(!r||r[r.length-1]!==n)return null;else r.pop();t+=n}i&&(t+='"');for(let e=r.length-1;e>=0;e-=1)t+=r[e];try{return JSON.parse(t)}catch(e){return null}}(t.args||"{}"),null===e||"object"!=typeof e||Array.isArray(e))throw Error("Malformed tool call chunk args.");r.push({name:t.name??"",args:e,id:t.id,type:"tool_call"})}catch(e){i.push({name:t.name,args:t.args,id:t.id,error:"Malformed args.",type:"invalid_tool_call"})}}t={...e,tool_calls:r,invalid_tool_calls:i}}super(t),Object.defineProperty(this,"tool_calls",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"invalid_tool_calls",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"tool_call_chunks",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"usage_metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.tool_call_chunks=t.tool_call_chunks??this.tool_call_chunks,this.tool_calls=t.tool_calls??this.tool_calls,this.invalid_tool_calls=t.invalid_tool_calls??this.invalid_tool_calls,this.usage_metadata=t.usage_metadata}get lc_aliases(){return{...super.lc_aliases,tool_calls:"tool_calls",invalid_tool_calls:"invalid_tool_calls",tool_call_chunks:"tool_call_chunks"}}static lc_name(){return"AIMessageChunk"}_getType(){return"ai"}get _printableFields(){return{...super._printableFields,tool_calls:this.tool_calls,tool_call_chunks:this.tool_call_chunks,invalid_tool_calls:this.invalid_tool_calls,usage_metadata:this.usage_metadata}}concat(e){let t={content:function(e,t){if("string"==typeof e)if("string"==typeof t)return e+t;else return[{type:"text",text:e},...t];return Array.isArray(t)?tu(e,t)??[...e,...t]:[...e,{type:"text",text:t}]}(this.content,e.content),additional_kwargs:tl(this.additional_kwargs,e.additional_kwargs),response_metadata:tl(this.response_metadata,e.response_metadata),tool_call_chunks:[],id:this.id??e.id};if(void 0!==this.tool_call_chunks||void 0!==e.tool_call_chunks){let r=tu(this.tool_call_chunks,e.tool_call_chunks);void 0!==r&&r.length>0&&(t.tool_call_chunks=r)}if(void 0!==this.usage_metadata||void 0!==e.usage_metadata){let r=this.usage_metadata??{input_tokens:0,output_tokens:0,total_tokens:0},i=e.usage_metadata??{input_tokens:0,output_tokens:0,total_tokens:0};t.usage_metadata={input_tokens:r.input_tokens+i.input_tokens,output_tokens:r.output_tokens+i.output_tokens,total_tokens:r.total_tokens+i.total_tokens}}return new td(t)}}function th(e){return _isToolCall(e)?e:"string"==typeof e.id&&"function"===e.type&&"object"==typeof e.function&&null!==e.function&&"arguments"in e.function&&"string"==typeof e.function.arguments&&"name"in e.function&&"string"==typeof e.function.name?{id:e.id,args:JSON.parse(e.function.arguments),name:e.function.name,type:"tool_call"}:e}class tp extends e8{constructor(e={}){super(e),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"langchain_tracer"}),Object.defineProperty(this,"projectName",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"exampleId",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"client",{enumerable:!0,configurable:!0,writable:!0,value:void 0});let{exampleId:t,projectName:r,client:i}=e;this.projectName=r??e3("LANGCHAIN_PROJECT")??e3("LANGCHAIN_SESSION"),this.exampleId=t,this.client=i??new er({});let a=tp.getTraceableRunTree();a&&this.updateFromRunTree(a)}async _convertToCreate(e,t){return{...e,extra:{...e.extra,runtime:await e4()},child_runs:void 0,session_name:this.projectName,reference_example_id:e.parent_run_id?void 0:t}}async persistRun(e){}async onRunCreate(e){let t=await this._convertToCreate(e,this.exampleId);await this.client.createRun(t)}async onRunUpdate(e){let t={end_time:e.end_time,error:e.error,outputs:e.outputs,events:e.events,inputs:e.inputs,trace_id:e.trace_id,dotted_order:e.dotted_order,parent_run_id:e.parent_run_id};await this.client.updateRun(e.id,t)}getRun(e){return this.runMap.get(e)}updateFromRunTree(e){let t=e,r=new Set;for(;t.parent_run&&!r.has(t.id)&&(r.add(t.id),t.parent_run);){;t=t.parent_run}r.clear();let i=[t];for(;i.length>0;){let e=i.shift();!(!e||r.has(e.id))&&(r.add(e.id),this.runMap.set(e.id,e),e.child_runs&&i.push(...e.child_runs))}this.client=e.client??this.client,this.projectName=e.project_name??this.projectName,this.exampleId=e.reference_example_id??this.exampleId}convertToRunTree(e){let t={},r=[];for(let[e,i]of this.runMap){let a=new eg({...i,child_runs:[],parent_run:void 0,client:this.client,project_name:this.projectName,reference_example_id:this.exampleId,tracingEnabled:!0});t[e]=a,r.push([e,i.dotted_order])}for(let[e]of(r.sort((e,t)=>e[1]&&t[1]?e[1].localeCompare(t[1]):0),r)){let r=this.runMap.get(e),i=t[e];if(r&&i&&r.parent_run_id){let e=t[r.parent_run_id];e&&(e.child_runs.push(i),i.parent_run=e)}}return t[e]}static getTraceableRunTree(){try{return eS()}catch{return}}}async function tf(e,t){!0===t?await e():(void 0===o&&(o=new(0,T.default)({autoStart:!0,concurrency:1})),o.add(e))}let tm=e=>void 0!==e?e:!!["LANGSMITH_TRACING_V2","LANGCHAIN_TRACING_V2","LANGSMITH_TRACING","LANGCHAIN_TRACING"].find(e=>"true"===e3(e));class tg{setHandler(e){return this.setHandlers([e])}}class tb{constructor(e,t,r,i,a,n,s,o){Object.defineProperty(this,"runId",{enumerable:!0,configurable:!0,writable:!0,value:e}),Object.defineProperty(this,"handlers",{enumerable:!0,configurable:!0,writable:!0,value:t}),Object.defineProperty(this,"inheritableHandlers",{enumerable:!0,configurable:!0,writable:!0,value:r}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:i}),Object.defineProperty(this,"inheritableTags",{enumerable:!0,configurable:!0,writable:!0,value:a}),Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:n}),Object.defineProperty(this,"inheritableMetadata",{enumerable:!0,configurable:!0,writable:!0,value:s}),Object.defineProperty(this,"_parentRunId",{enumerable:!0,configurable:!0,writable:!0,value:o})}get parentRunId(){return this._parentRunId}async handleText(e){await Promise.all(this.handlers.map(t=>tf(async()=>{try{await t.handleText?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleText: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleCustomEvent(e,t,r,i,a){await Promise.all(this.handlers.map(r=>tf(async()=>{try{await r.handleCustomEvent?.(e,t,this.runId,this.tags,this.metadata)}catch(e){if((r.raiseError?console.error:console.warn)(`Error in handler ${r.constructor.name}, handleCustomEvent: ${e}`),r.raiseError)throw e}},r.awaitHandlers)))}}class ty extends tb{getChild(e){let t=new tE(this.runId);return t.setHandlers(this.inheritableHandlers),t.addTags(this.inheritableTags),t.addMetadata(this.inheritableMetadata),e&&t.addTags([e],!1),t}async handleRetrieverEnd(e){await Promise.all(this.handlers.map(t=>tf(async()=>{if(!t.ignoreRetriever)try{await t.handleRetrieverEnd?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleRetriever`),t.raiseError)throw e}},t.awaitHandlers)))}async handleRetrieverError(e){await Promise.all(this.handlers.map(t=>tf(async()=>{if(!t.ignoreRetriever)try{await t.handleRetrieverError?.(e,this.runId,this._parentRunId,this.tags)}catch(r){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleRetrieverError: ${r}`),t.raiseError)throw e}},t.awaitHandlers)))}}class tv extends tb{async handleLLMNewToken(e,t,r,i,a,n){await Promise.all(this.handlers.map(r=>tf(async()=>{if(!r.ignoreLLM)try{await r.handleLLMNewToken?.(e,t??{prompt:0,completion:0},this.runId,this._parentRunId,this.tags,n)}catch(e){if((r.raiseError?console.error:console.warn)(`Error in handler ${r.constructor.name}, handleLLMNewToken: ${e}`),r.raiseError)throw e}},r.awaitHandlers)))}async handleLLMError(e){await Promise.all(this.handlers.map(t=>tf(async()=>{if(!t.ignoreLLM)try{await t.handleLLMError?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleLLMError: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleLLMEnd(e){await Promise.all(this.handlers.map(t=>tf(async()=>{if(!t.ignoreLLM)try{await t.handleLLMEnd?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleLLMEnd: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}}class tw extends tb{getChild(e){let t=new tE(this.runId);return t.setHandlers(this.inheritableHandlers),t.addTags(this.inheritableTags),t.addMetadata(this.inheritableMetadata),e&&t.addTags([e],!1),t}async handleChainError(e,t,r,i,a){await Promise.all(this.handlers.map(t=>tf(async()=>{if(!t.ignoreChain)try{await t.handleChainError?.(e,this.runId,this._parentRunId,this.tags,a)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleChainError: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleChainEnd(e,t,r,i,a){await Promise.all(this.handlers.map(t=>tf(async()=>{if(!t.ignoreChain)try{await t.handleChainEnd?.(e,this.runId,this._parentRunId,this.tags,a)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleChainEnd: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleAgentAction(e){await Promise.all(this.handlers.map(t=>tf(async()=>{if(!t.ignoreAgent)try{await t.handleAgentAction?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleAgentAction: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleAgentEnd(e){await Promise.all(this.handlers.map(t=>tf(async()=>{if(!t.ignoreAgent)try{await t.handleAgentEnd?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleAgentEnd: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}}class t_ extends tb{getChild(e){let t=new tE(this.runId);return t.setHandlers(this.inheritableHandlers),t.addTags(this.inheritableTags),t.addMetadata(this.inheritableMetadata),e&&t.addTags([e],!1),t}async handleToolError(e){await Promise.all(this.handlers.map(t=>tf(async()=>{if(!t.ignoreAgent)try{await t.handleToolError?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleToolError: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleToolEnd(e){await Promise.all(this.handlers.map(t=>tf(async()=>{if(!t.ignoreAgent)try{await t.handleToolEnd?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleToolEnd: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}}class tE extends tg{constructor(e,t){super(),Object.defineProperty(this,"handlers",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"inheritableHandlers",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"inheritableTags",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"inheritableMetadata",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"callback_manager"}),Object.defineProperty(this,"_parentRunId",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.handlers=t?.handlers??this.handlers,this.inheritableHandlers=t?.inheritableHandlers??this.inheritableHandlers,this.tags=t?.tags??this.tags,this.inheritableTags=t?.inheritableTags??this.inheritableTags,this.metadata=t?.metadata??this.metadata,this.inheritableMetadata=t?.inheritableMetadata??this.inheritableMetadata,this._parentRunId=e}getParentRunId(){return this._parentRunId}async handleLLMStart(e,t,r,i,a,n,s,o){return Promise.all(t.map(async(t,i)=>{let n=0===i&&r?r:S();return await Promise.all(this.handlers.map(r=>{if(!r.ignoreLLM)return e5(r)&&r._createRunForLLMStart(e,[t],n,this._parentRunId,a,this.tags,this.metadata,o),tf(async()=>{try{await r.handleLLMStart?.(e,[t],n,this._parentRunId,a,this.tags,this.metadata,o)}catch(e){if((r.raiseError?console.error:console.warn)(`Error in handler ${r.constructor.name}, handleLLMStart: ${e}`),r.raiseError)throw e}},r.awaitHandlers)})),new tv(n,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}))}async handleChatModelStart(e,t,r,i,a,n,s,o){return Promise.all(t.map(async(t,i)=>{let n=0===i&&r?r:S();return await Promise.all(this.handlers.map(r=>{if(!r.ignoreLLM)return e5(r)&&r._createRunForChatModelStart(e,[t],n,this._parentRunId,a,this.tags,this.metadata,o),tf(async()=>{try{if(r.handleChatModelStart)await r.handleChatModelStart?.(e,[t],n,this._parentRunId,a,this.tags,this.metadata,o);else if(r.handleLLMStart){let i=function(e,t="Human",r="AI"){let i=[];for(let a of e){let e;if("human"===a._getType())e=t;else if("ai"===a._getType())e=r;else if("system"===a._getType())e="System";else if("function"===a._getType())e="Function";else if("tool"===a._getType())e="Tool";else if("generic"===a._getType())e=a.role;else throw Error(`Got unsupported message type: ${a._getType()}`);let n=a.name?`${a.name}, `:"",s="string"==typeof a.content?a.content:JSON.stringify(a.content,null,2);i.push(`${e}: ${n}${s}`)}return i.join("\n")}(t);await r.handleLLMStart?.(e,[i],n,this._parentRunId,a,this.tags,this.metadata,o)}}catch(e){if((r.raiseError?console.error:console.warn)(`Error in handler ${r.constructor.name}, handleLLMStart: ${e}`),r.raiseError)throw e}},r.awaitHandlers)})),new tv(n,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}))}async handleChainStart(e,t,r=S(),i,a,n,s){return await Promise.all(this.handlers.map(a=>{if(!a.ignoreChain)return e5(a)&&a._createRunForChainStart(e,t,r,this._parentRunId,this.tags,this.metadata,i,s),tf(async()=>{try{await a.handleChainStart?.(e,t,r,this._parentRunId,this.tags,this.metadata,i,s)}catch(e){if((a.raiseError?console.error:console.warn)(`Error in handler ${a.constructor.name}, handleChainStart: ${e}`),a.raiseError)throw e}},a.awaitHandlers)})),new tw(r,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}async handleToolStart(e,t,r=S(),i,a,n,s){return await Promise.all(this.handlers.map(i=>{if(!i.ignoreAgent)return e5(i)&&i._createRunForToolStart(e,t,r,this._parentRunId,this.tags,this.metadata,s),tf(async()=>{try{await i.handleToolStart?.(e,t,r,this._parentRunId,this.tags,this.metadata,s)}catch(e){if((i.raiseError?console.error:console.warn)(`Error in handler ${i.constructor.name}, handleToolStart: ${e}`),i.raiseError)throw e}},i.awaitHandlers)})),new t_(r,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}async handleRetrieverStart(e,t,r=S(),i,a,n,s){return await Promise.all(this.handlers.map(i=>{if(!i.ignoreRetriever)return e5(i)&&i._createRunForRetrieverStart(e,t,r,this._parentRunId,this.tags,this.metadata,s),tf(async()=>{try{await i.handleRetrieverStart?.(e,t,r,this._parentRunId,this.tags,this.metadata,s)}catch(e){if((i.raiseError?console.error:console.warn)(`Error in handler ${i.constructor.name}, handleRetrieverStart: ${e}`),i.raiseError)throw e}},i.awaitHandlers)})),new ty(r,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}async handleCustomEvent(e,t,r,i,a){await Promise.all(this.handlers.map(i=>tf(async()=>{if(!i.ignoreCustomEvent)try{await i.handleCustomEvent?.(e,t,r,this.tags,this.metadata)}catch(e){if((i.raiseError?console.error:console.warn)(`Error in handler ${i.constructor.name}, handleCustomEvent: ${e}`),i.raiseError)throw e}},i.awaitHandlers)))}addHandler(e,t=!0){this.handlers.push(e),t&&this.inheritableHandlers.push(e)}removeHandler(e){this.handlers=this.handlers.filter(t=>t!==e),this.inheritableHandlers=this.inheritableHandlers.filter(t=>t!==e)}setHandlers(e,t=!0){for(let r of(this.handlers=[],this.inheritableHandlers=[],e))this.addHandler(r,t)}addTags(e,t=!0){this.removeTags(e),this.tags.push(...e),t&&this.inheritableTags.push(...e)}removeTags(e){this.tags=this.tags.filter(t=>!e.includes(t)),this.inheritableTags=this.inheritableTags.filter(t=>!e.includes(t))}addMetadata(e,t=!0){this.metadata={...this.metadata,...e},t&&(this.inheritableMetadata={...this.inheritableMetadata,...e})}removeMetadata(e){for(let t of Object.keys(e))delete this.metadata[t],delete this.inheritableMetadata[t]}copy(e=[],t=!0){let r=new tE(this._parentRunId);for(let e of this.handlers){let t=this.inheritableHandlers.includes(e);r.addHandler(e,t)}for(let e of this.tags){let t=this.inheritableTags.includes(e);r.addTags([e],t)}for(let e of Object.keys(this.metadata)){let t=Object.keys(this.inheritableMetadata).includes(e);r.addMetadata({[e]:this.metadata[e]},t)}for(let i of e)r.handlers.filter(e=>"console_callback_handler"===e.name).some(e=>e.name===i.name)||r.addHandler(i,t);return r}static fromHandlers(e){class t extends e9{constructor(){super(),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:S()}),Object.assign(this,e)}}let r=new this;return r.addHandler(new t),r}static async configure(e,t,r,i,a,n,s){return this._configureSync(e,t,r,i,a,n,s)}static _configureSync(e,t,r,i,a,n,s){let o;(e||t)&&(Array.isArray(e)||!e?(o=new tE).setHandlers(e?.map(tO)??[],!0):o=e,o=o.copy(Array.isArray(t)?t.map(tO):t?.handlers,!1));let l="true"===e3("LANGCHAIN_VERBOSE")||s?.verbose,u=tp.getTraceableRunTree()?.tracingEnabled||tm(),c=u||(e3("LANGCHAIN_TRACING")??!1);if(l||c){if(o||(o=new tE),l&&!o.handlers.some(e=>e.name===tn.prototype.name)){let e=new tn;o.addHandler(e,!0)}if(c&&!o.handlers.some(e=>"langchain_tracer"===e.name)&&u){let e=new tp;o.addHandler(e,!0),o._parentRunId=tp.getTraceableRunTree()?.id??o._parentRunId}}return(r||i)&&o&&(o.addTags(r??[]),o.addTags(i??[],!1)),(a||n)&&o&&(o.addMetadata(a??{}),o.addMetadata(n??{},!1)),o}}function tO(e){return"name"in e?e:e9.fromMethods(e)}class tI{getStore(){}run(e,t){return t()}}let tS=new tI,tT=Symbol.for("ls:tracing_async_local_storage"),tP=Symbol.for("lc:child_config");class t${getInstance(){return globalThis[tT]??tS}getRunnableConfig(){let e=this.getInstance();return e.getStore()?.extra?.[tP]}runWithConfig(e,t,r){let i,a=tE._configureSync(e?.callbacks,void 0,e?.tags,void 0,e?.metadata),n=this.getInstance(),s=a?.getParentRunId(),o=a?.handlers?.find(e=>e?.name==="langchain_tracer");return o&&s?i=o.convertToRunTree(s):r||(i=new eg({name:"<runnable_lambda>",tracingEnabled:!1})),i&&(i.extra={...i.extra,[tP]:e}),n.run(i,t)}initializeGlobalInstance(e){void 0===globalThis[tT]&&(globalThis[tT]=e)}}let tA=new t$;async function tR(e,t){let r;return void 0===t?e:Promise.race([e.catch(e=>{if(!t?.aborted)throw e}),new Promise((e,i)=>{r=()=>{i(Error("Aborted"))},t.addEventListener("abort",r),t.aborted&&i(Error("Aborted"))})]).finally(()=>t.removeEventListener("abort",r))}class tj extends ReadableStream{constructor(){super(...arguments),Object.defineProperty(this,"reader",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}ensureReader(){this.reader||(this.reader=this.getReader())}async next(){this.ensureReader();try{let e=await this.reader.read();if(e.done)return this.reader.releaseLock(),{done:!0,value:void 0};return{done:!1,value:e.value}}catch(e){throw this.reader.releaseLock(),e}}async return(){if(this.ensureReader(),this.locked){let e=this.reader.cancel();this.reader.releaseLock(),await e}return{done:!0,value:void 0}}async throw(e){if(this.ensureReader(),this.locked){let e=this.reader.cancel();this.reader.releaseLock(),await e}throw e}[Symbol.asyncIterator](){return this}async [Symbol.asyncDispose](){await this.return()}static fromReadableStream(e){let t=e.getReader();return new tj({start:e=>(function r(){return t.read().then(({done:t,value:i})=>t?void e.close():(e.enqueue(i),r()))})(),cancel(){t.releaseLock()}})}static fromAsyncGenerator(e){return new tj({async pull(t){let{value:r,done:i}=await e.next();i&&t.close(),t.enqueue(r)},async cancel(t){await e.return(t)}})}}function tx(e,t=2){let r=Array.from({length:t},()=>[]);return r.map(async function*(t){for(;;)if(0===t.length){let t=await e.next();for(let e of r)e.push(t)}else{if(t[0].done)return;yield t.shift().value}})}function tk(e,t){if(Array.isArray(e)&&Array.isArray(t))return e.concat(t);if("string"==typeof e&&"string"==typeof t)return e+t;if("number"==typeof e&&"number"==typeof t)return e+t;if("concat"in e&&"function"==typeof e.concat)return e.concat(t);if("object"==typeof e&&"object"==typeof t){let r={...e};for(let[e,i]of Object.entries(t))e in r&&!Array.isArray(r[e])?r[e]=tk(r[e],i):r[e]=i;return r}else throw Error(`Cannot concat ${typeof e} and ${typeof t}`)}class tN{constructor(e){Object.defineProperty(this,"generator",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"setup",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"config",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"signal",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"firstResult",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"firstResultUsed",{enumerable:!0,configurable:!0,writable:!0,value:!1}),this.generator=e.generator,this.config=e.config,this.signal=e.signal??this.config?.signal,this.setup=new Promise((t,r)=>{tA.runWithConfig(e.config,async()=>{this.firstResult=e.generator.next(),e.startSetup?this.firstResult.then(e.startSetup).then(t,r):this.firstResult.then(e=>t(void 0),r)},!0)})}async next(...e){return(this.signal?.throwIfAborted(),this.firstResultUsed)?tA.runWithConfig(this.config,this.signal?async()=>tR(this.generator.next(...e),this.signal):async()=>this.generator.next(...e),!0):(this.firstResultUsed=!0,this.firstResult)}async return(e){return this.generator.return(e)}async throw(e){return this.generator.throw(e)}[Symbol.asyncIterator](){return this}async [Symbol.asyncDispose](){await this.return()}}async function tC(e,t,r,i,...a){let n=new tN({generator:t,startSetup:r,signal:i}),s=await n.setup;return{output:e(n,s,...a),setup:s}}class tL{constructor(e){Object.defineProperty(this,"ops",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.ops=e.ops??[]}concat(e){let t=this.ops.concat(e.ops),r=eU({},t);return new tM({ops:t,state:r[r.length-1].newDocument})}}class tM extends tL{constructor(e){super(e),Object.defineProperty(this,"state",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.state=e.state}concat(e){let t=this.ops.concat(e.ops),r=eU(this.state,e.ops);return new tM({ops:t,state:r[r.length-1].newDocument})}static fromRunLogPatch(e){let t=eU({},e.ops);return new tM({ops:e.ops,state:t[t.length-1].newDocument})}}let tD=e=>"log_stream_tracer"===e.name;async function tU(e,t){if("original"===t)throw Error("Do not assign inputs with original schema drop the key for now. When inputs are added to streamLog they should be added with standardized schema for streaming events.");let{inputs:r}=e;return["retriever","llm","prompt"].includes(e.run_type)?r:1!==Object.keys(r).length||r?.input!==""?r.input:void 0}async function tF(e,t){let{outputs:r}=e;return"original"===t||["retriever","llm","prompt"].includes(e.run_type)?r:void 0!==r&&1===Object.keys(r).length&&r?.output!==void 0?r.output:r}class tH extends e8{constructor(e){super({_awaitHandler:!0,...e}),Object.defineProperty(this,"autoClose",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"includeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_schemaFormat",{enumerable:!0,configurable:!0,writable:!0,value:"original"}),Object.defineProperty(this,"rootId",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"keyMapByRunId",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"counterMapByRunName",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"transformStream",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"writer",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"receiveStream",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"log_stream_tracer"}),this.autoClose=e?.autoClose??!0,this.includeNames=e?.includeNames,this.includeTypes=e?.includeTypes,this.includeTags=e?.includeTags,this.excludeNames=e?.excludeNames,this.excludeTypes=e?.excludeTypes,this.excludeTags=e?.excludeTags,this._schemaFormat=e?._schemaFormat??this._schemaFormat,this.transformStream=new TransformStream,this.writer=this.transformStream.writable.getWriter(),this.receiveStream=tj.fromReadableStream(this.transformStream.readable)}[Symbol.asyncIterator](){return this.receiveStream}async persistRun(e){}_includeRun(e){if(e.id===this.rootId)return!1;let t=e.tags??[],r=void 0===this.includeNames&&void 0===this.includeTags&&void 0===this.includeTypes;return void 0!==this.includeNames&&(r=r||this.includeNames.includes(e.name)),void 0!==this.includeTypes&&(r=r||this.includeTypes.includes(e.run_type)),void 0!==this.includeTags&&(r=r||void 0!==t.find(e=>this.includeTags?.includes(e))),void 0!==this.excludeNames&&(r=r&&!this.excludeNames.includes(e.name)),void 0!==this.excludeTypes&&(r=r&&!this.excludeTypes.includes(e.run_type)),void 0!==this.excludeTags&&(r=r&&t.every(e=>!this.excludeTags?.includes(e))),r}async *tapOutputIterable(e,t){for await(let r of t){if(e!==this.rootId){let t=this.keyMapByRunId[e];t&&await this.writer.write(new tL({ops:[{op:"add",path:`/logs/${t}/streamed_output/-`,value:r}]}))}yield r}}async onRunCreate(e){if(void 0===this.rootId&&(this.rootId=e.id,await this.writer.write(new tL({ops:[{op:"replace",path:"",value:{id:e.id,name:e.name,type:e.run_type,streamed_output:[],final_output:void 0,logs:{}}}]}))),!this._includeRun(e))return;void 0===this.counterMapByRunName[e.name]&&(this.counterMapByRunName[e.name]=0),this.counterMapByRunName[e.name]+=1;let t=this.counterMapByRunName[e.name];this.keyMapByRunId[e.id]=1===t?e.name:`${e.name}:${t}`;let r={id:e.id,name:e.name,type:e.run_type,tags:e.tags??[],metadata:e.extra?.metadata??{},start_time:new Date(e.start_time).toISOString(),streamed_output:[],streamed_output_str:[],final_output:void 0,end_time:void 0};"streaming_events"===this._schemaFormat&&(r.inputs=await tU(e,this._schemaFormat)),await this.writer.write(new tL({ops:[{op:"add",path:`/logs/${this.keyMapByRunId[e.id]}`,value:r}]}))}async onRunUpdate(e){try{let t=this.keyMapByRunId[e.id];if(void 0===t)return;let r=[];"streaming_events"===this._schemaFormat&&r.push({op:"replace",path:`/logs/${t}/inputs`,value:await tU(e,this._schemaFormat)}),r.push({op:"add",path:`/logs/${t}/final_output`,value:await tF(e,this._schemaFormat)}),void 0!==e.end_time&&r.push({op:"add",path:`/logs/${t}/end_time`,value:new Date(e.end_time).toISOString()});let i=new tL({ops:r});await this.writer.write(i)}finally{if(e.id===this.rootId){let t=new tL({ops:[{op:"replace",path:"/final_output",value:await tF(e,this._schemaFormat)}]});await this.writer.write(t),this.autoClose&&await this.writer.close()}}}async onLLMNewToken(e,t,r){let i,a=this.keyMapByRunId[e.id];if(void 0===a)return;if(void 0!==e.inputs.messages){var n;i=void 0!==(n=r?.chunk)&&void 0!==n.message?r?.chunk:new td({id:`run-${e.id}`,content:t})}else i=t;let s=new tL({ops:[{op:"add",path:`/logs/${a}/streamed_output_str/-`,value:t},{op:"add",path:`/logs/${a}/streamed_output/-`,value:i}]});await this.writer.write(s)}}class tB{constructor(e){Object.defineProperty(this,"text",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"generationInfo",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.text=e.text,this.generationInfo=e.generationInfo}concat(e){return new tB({text:this.text+e.text,generationInfo:{...this.generationInfo,...e.generationInfo}})}}function tz({name:e,serialized:t}){return void 0!==e?e:t?.name!==void 0?t.name:t?.id!==void 0&&Array.isArray(t?.id)?t.id[t.id.length-1]:"Unnamed"}let tG=e=>"event_stream_tracer"===e.name;class tq extends e8{constructor(e){super({_awaitHandler:!0,...e}),Object.defineProperty(this,"autoClose",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"includeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"runInfoMap",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),Object.defineProperty(this,"tappedPromises",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),Object.defineProperty(this,"transformStream",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"writer",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"receiveStream",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"event_stream_tracer"}),this.autoClose=e?.autoClose??!0,this.includeNames=e?.includeNames,this.includeTypes=e?.includeTypes,this.includeTags=e?.includeTags,this.excludeNames=e?.excludeNames,this.excludeTypes=e?.excludeTypes,this.excludeTags=e?.excludeTags,this.transformStream=new TransformStream,this.writer=this.transformStream.writable.getWriter(),this.receiveStream=tj.fromReadableStream(this.transformStream.readable)}[Symbol.asyncIterator](){return this.receiveStream}async persistRun(e){}_includeRun(e){let t=e.tags??[],r=void 0===this.includeNames&&void 0===this.includeTags&&void 0===this.includeTypes;return void 0!==this.includeNames&&(r=r||this.includeNames.includes(e.name)),void 0!==this.includeTypes&&(r=r||this.includeTypes.includes(e.runType)),void 0!==this.includeTags&&(r=r||void 0!==t.find(e=>this.includeTags?.includes(e))),void 0!==this.excludeNames&&(r=r&&!this.excludeNames.includes(e.name)),void 0!==this.excludeTypes&&(r=r&&!this.excludeTypes.includes(e.runType)),void 0!==this.excludeTags&&(r=r&&t.every(e=>!this.excludeTags?.includes(e))),r}async *tapOutputIterable(e,t){let r=await t.next();if(r.done)return;let i=this.runInfoMap.get(e);if(void 0===i)return void(yield r.value);function a(e,t){return"llm"===e&&"string"==typeof t?new tB({text:t}):t}let n=this.tappedPromises.get(e);if(void 0===n){let s;n=new Promise(e=>{s=e}),this.tappedPromises.set(e,n);try{let n={event:`on_${i.runType}_stream`,run_id:e,name:i.name,tags:i.tags,metadata:i.metadata,data:{}};for await(let e of(await this.send({...n,data:{chunk:a(i.runType,r.value)}},i),yield r.value,t))"tool"!==i.runType&&"retriever"!==i.runType&&await this.send({...n,data:{chunk:a(i.runType,e)}},i),yield e}finally{s()}}else for await(let e of(yield r.value,t))yield e}async send(e,t){this._includeRun(t)&&await this.writer.write(e)}async sendEndEvent(e,t){let r=this.tappedPromises.get(e.run_id);void 0!==r?r.then(()=>{this.send(e,t)}):await this.send(e,t)}async onLLMStart(e){let t=tz(e),r=void 0!==e.inputs.messages?"chat_model":"llm",i={tags:e.tags??[],metadata:e.extra?.metadata??{},name:t,runType:r,inputs:e.inputs};this.runInfoMap.set(e.id,i);let a=`on_${r}_start`;await this.send({event:a,data:{input:e.inputs},name:t,tags:e.tags??[],run_id:e.id,metadata:e.extra?.metadata??{}},i)}async onLLMNewToken(e,t,r){let i,a,n=this.runInfoMap.get(e.id);if(void 0===n)throw Error(`onLLMNewToken: Run ID ${e.id} not found in run map.`);if(1!==this.runInfoMap.size){if("chat_model"===n.runType)a="on_chat_model_stream",i=r?.chunk===void 0?new td({content:t,id:`run-${e.id}`}):r.chunk.message;else if("llm"===n.runType)a="on_llm_stream",i=r?.chunk===void 0?new tB({text:t}):r.chunk;else throw Error(`Unexpected run type ${n.runType}`);await this.send({event:a,data:{chunk:i},run_id:e.id,name:n.name,tags:n.tags,metadata:n.metadata},n)}}async onLLMEnd(e){let t,r,i=this.runInfoMap.get(e.id);if(this.runInfoMap.delete(e.id),void 0===i)throw Error(`onLLMEnd: Run ID ${e.id} not found in run map.`);let a=e.outputs?.generations;if("chat_model"===i.runType){for(let e of a??[]){if(void 0!==r)break;r=e[0]?.message}t="on_chat_model_end"}else if("llm"===i.runType)r={generations:a?.map(e=>e.map(e=>({text:e.text,generationInfo:e.generationInfo}))),llmOutput:e.outputs?.llmOutput??{}},t="on_llm_end";else throw Error(`onLLMEnd: Unexpected run type: ${i.runType}`);await this.sendEndEvent({event:t,data:{output:r,input:i.inputs},run_id:e.id,name:i.name,tags:i.tags,metadata:i.metadata},i)}async onChainStart(e){let t=tz(e),r=e.run_type??"chain",i={tags:e.tags??[],metadata:e.extra?.metadata??{},name:t,runType:e.run_type},a={};""===e.inputs.input&&1===Object.keys(e.inputs).length?(a={},i.inputs={}):void 0!==e.inputs.input?(a.input=e.inputs.input,i.inputs=e.inputs.input):(a.input=e.inputs,i.inputs=e.inputs),this.runInfoMap.set(e.id,i),await this.send({event:`on_${r}_start`,data:a,name:t,tags:e.tags??[],run_id:e.id,metadata:e.extra?.metadata??{}},i)}async onChainEnd(e){let t=this.runInfoMap.get(e.id);if(this.runInfoMap.delete(e.id),void 0===t)throw Error(`onChainEnd: Run ID ${e.id} not found in run map.`);let r=`on_${e.run_type}_end`,i=e.inputs??t.inputs??{},a={output:e.outputs?.output??e.outputs,input:i};i.input&&1===Object.keys(i).length&&(a.input=i.input,t.inputs=i.input),await this.sendEndEvent({event:r,data:a,run_id:e.id,name:t.name,tags:t.tags,metadata:t.metadata??{}},t)}async onToolStart(e){let t=tz(e),r={tags:e.tags??[],metadata:e.extra?.metadata??{},name:t,runType:"tool",inputs:e.inputs??{}};this.runInfoMap.set(e.id,r),await this.send({event:"on_tool_start",data:{input:e.inputs??{}},name:t,run_id:e.id,tags:e.tags??[],metadata:e.extra?.metadata??{}},r)}async onToolEnd(e){let t=this.runInfoMap.get(e.id);if(this.runInfoMap.delete(e.id),void 0===t)throw Error(`onToolEnd: Run ID ${e.id} not found in run map.`);if(void 0===t.inputs)throw Error(`onToolEnd: Run ID ${e.id} is a tool call, and is expected to have traced inputs.`);let r=e.outputs?.output===void 0?e.outputs:e.outputs.output;await this.sendEndEvent({event:"on_tool_end",data:{output:r,input:t.inputs},run_id:e.id,name:t.name,tags:t.tags,metadata:t.metadata},t)}async onRetrieverStart(e){let t=tz(e),r={tags:e.tags??[],metadata:e.extra?.metadata??{},name:t,runType:"retriever",inputs:{query:e.inputs.query}};this.runInfoMap.set(e.id,r),await this.send({event:"on_retriever_start",data:{input:{query:e.inputs.query}},name:t,tags:e.tags??[],run_id:e.id,metadata:e.extra?.metadata??{}},r)}async onRetrieverEnd(e){let t=this.runInfoMap.get(e.id);if(this.runInfoMap.delete(e.id),void 0===t)throw Error(`onRetrieverEnd: Run ID ${e.id} not found in run map.`);await this.sendEndEvent({event:"on_retriever_end",data:{output:e.outputs?.documents??e.outputs,input:t.inputs},run_id:e.id,name:t.name,tags:t.tags,metadata:t.metadata},t)}async handleCustomEvent(e,t,r){let i=this.runInfoMap.get(r);if(void 0===i)throw Error(`handleCustomEvent: Run ID ${r} not found in run map.`);await this.send({event:"on_custom_event",run_id:r,name:e,tags:i.tags,metadata:i.metadata,data:t},i)}async finish(){Promise.all([...this.tappedPromises.values()]).finally(()=>{this.writer.close()})}}async function tJ(e){return tE._configureSync(e?.callbacks,void 0,e?.tags,void 0,e?.metadata)}function tZ(...e){let t={};for(let r of e.filter(e=>!!e))for(let e of Object.keys(r))if("metadata"===e)t[e]={...t[e],...r[e]};else if("tags"===e){let i=t[e]??[];t[e]=[...new Set(i.concat(r[e]??[]))]}else if("configurable"===e)t[e]={...t[e],...r[e]};else if("timeout"===e)void 0===t.timeout?t.timeout=r.timeout:void 0!==r.timeout&&(t.timeout=Math.min(t.timeout,r.timeout));else if("signal"===e)void 0===t.signal?t.signal=r.signal:void 0!==r.signal&&("any"in AbortSignal?t.signal=AbortSignal.any([t.signal,r.signal]):t.signal=r.signal);else if("callbacks"===e){let e=t.callbacks,i=r.callbacks;if(Array.isArray(i))if(e)if(Array.isArray(e))t.callbacks=e.concat(i);else{let r=e.copy();for(let e of i)r.addHandler(tO(e),!0);t.callbacks=r}else t.callbacks=i;else if(i)if(e)if(Array.isArray(e)){let r=i.copy();for(let t of e)r.addHandler(tO(t),!0);t.callbacks=r}else t.callbacks=new tE(i._parentRunId,{handlers:e.handlers.concat(i.handlers),inheritableHandlers:e.inheritableHandlers.concat(i.inheritableHandlers),tags:Array.from(new Set(e.tags.concat(i.tags))),inheritableTags:Array.from(new Set(e.inheritableTags.concat(i.inheritableTags))),metadata:{...e.metadata,...i.metadata}});else t.callbacks=i}else t[e]=r[e]??t[e];return t}let tY=new Set(["string","number","boolean"]);function tW(e){let t=tA.getRunnableConfig(),r={tags:[],metadata:{},recursionLimit:25,runId:void 0};if(t){let{runId:e,runName:i,...a}=t;r=Object.entries(a).reduce((e,[t,r])=>(void 0!==r&&(e[t]=r),e),r)}if(e&&(r=Object.entries(e).reduce((e,[t,r])=>(void 0!==r&&(e[t]=r),e),r)),r?.configurable)for(let e of Object.keys(r.configurable))tY.has(typeof r.configurable[e])&&!r.metadata?.[e]&&(r.metadata||(r.metadata={}),r.metadata[e]=r.configurable[e]);if(void 0!==r.timeout){if(r.timeout<=0)throw Error("Timeout must be a positive number");let e=AbortSignal.timeout(r.timeout);void 0!==r.signal?"any"in AbortSignal&&(r.signal=AbortSignal.any([r.signal,e])):r.signal=e,delete r.timeout}return r}function tV(e={},{callbacks:t,maxConcurrency:r,recursionLimit:i,runName:a,configurable:n,runId:s}={}){let o=tW(e);return void 0!==t&&(delete o.runName,o.callbacks=t),void 0!==i&&(o.recursionLimit=i),void 0!==r&&(o.maxConcurrency=r),void 0!==a&&(o.runName=a),void 0!==n&&(o.configurable={...o.configurable,...n}),void 0!==s&&delete o.runId,o}let tK=[400,401,402,403,404,405,406,407,409],tX=e=>{if(e.message.startsWith("Cancel")||e.message.startsWith("AbortError")||"AbortError"===e.name||e?.code==="ECONNABORTED")throw e;let t=e?.response?.status??e?.status;if(t&&tK.includes(+t))throw e;if(e?.error?.code==="insufficient_quota"){let t=Error(e?.message);throw t.name="InsufficientQuotaError",t}};class tQ{constructor(e){Object.defineProperty(this,"maxConcurrency",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"maxRetries",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"onFailedAttempt",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"queue",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.maxConcurrency=e.maxConcurrency??1/0,this.maxRetries=e.maxRetries??6,this.onFailedAttempt=e.onFailedAttempt??tX;let t=T.default;this.queue=new t({concurrency:this.maxConcurrency})}call(e,...t){return this.queue.add(()=>y(()=>e(...t).catch(e=>{if(e instanceof Error)throw e;throw Error(e)}),{onFailedAttempt:this.onFailedAttempt,retries:this.maxRetries,randomize:!0}),{throwOnTimeout:!0})}callWithOptions(e,t,...r){return e.signal?Promise.race([this.call(t,...r),new Promise((t,r)=>{e.signal?.addEventListener("abort",()=>{r(Error("AbortError"))})})]):this.call(t,...r)}fetch(...e){return this.call(()=>fetch(...e).then(e=>e.ok?e:Promise.reject(e)))}}class t0 extends e8{constructor({config:e,onStart:t,onEnd:r,onError:i}){super({_awaitHandler:!0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"RootListenersTracer"}),Object.defineProperty(this,"rootId",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"config",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"argOnStart",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"argOnEnd",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"argOnError",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.config=e,this.argOnStart=t,this.argOnEnd=r,this.argOnError=i}persistRun(e){return Promise.resolve()}async onRunCreate(e){!this.rootId&&(this.rootId=e.id,this.argOnStart&&(1===this.argOnStart.length?await this.argOnStart(e):2===this.argOnStart.length&&await this.argOnStart(e,this.config)))}async onRunUpdate(e){e.id===this.rootId&&(e.error?this.argOnError&&(1===this.argOnError.length?await this.argOnError(e):2===this.argOnError.length&&await this.argOnError(e,this.config)):this.argOnEnd&&(1===this.argOnEnd.length?await this.argOnEnd(e):2===this.argOnEnd.length&&await this.argOnEnd(e,this.config)))}}function t1(e){return!!e&&e.lc_runnable}class t4{constructor(e){Object.defineProperty(this,"includeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.includeNames=e.includeNames,this.includeTypes=e.includeTypes,this.includeTags=e.includeTags,this.excludeNames=e.excludeNames,this.excludeTypes=e.excludeTypes,this.excludeTags=e.excludeTags}includeEvent(e,t){let r=void 0===this.includeNames&&void 0===this.includeTypes&&void 0===this.includeTags,i=e.tags??[];return void 0!==this.includeNames&&(r=r||this.includeNames.includes(e.name)),void 0!==this.includeTypes&&(r=r||this.includeTypes.includes(t)),void 0!==this.includeTags&&(r=r||i.some(e=>this.includeTags?.includes(e))),void 0!==this.excludeNames&&(r=r&&!this.excludeNames.includes(e.name)),void 0!==this.excludeTypes&&(r=r&&!this.excludeTypes.includes(t)),void 0!==this.excludeTags&&(r=r&&i.every(e=>!this.excludeTags?.includes(e))),r}}let t3=Symbol("Let zodToJsonSchema decide on which parser to use"),t2={name:void 0,$refStrategy:"root",basePath:["#"],effectStrategy:"input",pipeStrategy:"all",dateStrategy:"format:date-time",mapStrategy:"entries",removeAdditionalStrategy:"passthrough",allowedAdditionalProperties:!0,rejectedAdditionalProperties:!1,definitionPath:"definitions",target:"jsonSchema7",strictUnions:!1,definitions:{},errorMessages:!1,markdownDescription:!1,patternStrategy:"escape",applyRegexFlags:!1,emailStrategy:"format:email",base64Strategy:"contentEncoding:base64",nameStrategy:"ref"},t9=e=>"string"==typeof e?{...t2,name:e}:{...t2,...e},t6=e=>{let t=t9(e),r=void 0!==t.name?[...t.basePath,t.definitionPath,t.name]:t.basePath;return{...t,currentPath:r,propertyPath:void 0,seen:new Map(Object.entries(t.definitions).map(([e,r])=>[r._def,{def:r._def,path:[...t.basePath,t.definitionPath,e],jsonSchema:void 0}]))}};function t5(e,t,r,i){i?.errorMessages&&r&&(e.errorMessage={...e.errorMessage,[t]:r})}function t8(e,t,r,i,a){e[t]=r,t5(e,t,i,a)}function t7(e,t){return rb(e.type._def,t)}let re=(e,t)=>rb(e.innerType._def,t),rt=(e,t)=>{let r={type:"integer",format:"unix-time"};if("openApi3"===t.target)return r;for(let i of e.checks)switch(i.kind){case"min":t8(r,"minimum",i.value,i.message,t);break;case"max":t8(r,"maximum",i.value,i.message,t)}return r},rr=e=>(!("type"in e)||"string"!==e.type)&&"allOf"in e,ri={cuid:/^[cC][^\s-]{8,}$/,cuid2:/^[0-9a-z]+$/,ulid:/^[0-9A-HJKMNP-TV-Z]{26}$/,email:/^(?!\.)(?!.*\.\.)([a-zA-Z0-9_'+\-\.]*)[a-zA-Z0-9_+-]@([a-zA-Z0-9][a-zA-Z0-9\-]*\.)+[a-zA-Z]{2,}$/,emoji:()=>(void 0===l&&(l=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),l),ipv4Cidr:/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,ipv6Cidr:/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,base64:/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,base64url:/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,nanoid:/^[a-zA-Z0-9_-]{21}$/,jwt:/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/};function ra(e,t){let r={type:"string"};if(e.checks)for(let i of e.checks)switch(i.kind){case"min":t8(r,"minLength","number"==typeof r.minLength?Math.max(r.minLength,i.value):i.value,i.message,t);break;case"max":t8(r,"maxLength","number"==typeof r.maxLength?Math.min(r.maxLength,i.value):i.value,i.message,t);break;case"email":switch(t.emailStrategy){case"format:email":ro(r,"email",i.message,t);break;case"format:idn-email":ro(r,"idn-email",i.message,t);break;case"pattern:zod":rl(r,ri.email,i.message,t)}break;case"url":ro(r,"uri",i.message,t);break;case"uuid":ro(r,"uuid",i.message,t);break;case"regex":rl(r,i.regex,i.message,t);break;case"cuid":rl(r,ri.cuid,i.message,t);break;case"cuid2":rl(r,ri.cuid2,i.message,t);break;case"startsWith":rl(r,RegExp(`^${rn(i.value,t)}`),i.message,t);break;case"endsWith":rl(r,RegExp(`${rn(i.value,t)}$`),i.message,t);break;case"datetime":ro(r,"date-time",i.message,t);break;case"date":ro(r,"date",i.message,t);break;case"time":ro(r,"time",i.message,t);break;case"duration":ro(r,"duration",i.message,t);break;case"length":t8(r,"minLength","number"==typeof r.minLength?Math.max(r.minLength,i.value):i.value,i.message,t),t8(r,"maxLength","number"==typeof r.maxLength?Math.min(r.maxLength,i.value):i.value,i.message,t);break;case"includes":rl(r,RegExp(rn(i.value,t)),i.message,t);break;case"ip":"v6"!==i.version&&ro(r,"ipv4",i.message,t),"v4"!==i.version&&ro(r,"ipv6",i.message,t);break;case"base64url":rl(r,ri.base64url,i.message,t);break;case"jwt":rl(r,ri.jwt,i.message,t);break;case"cidr":"v6"!==i.version&&rl(r,ri.ipv4Cidr,i.message,t),"v4"!==i.version&&rl(r,ri.ipv6Cidr,i.message,t);break;case"emoji":rl(r,ri.emoji(),i.message,t);break;case"ulid":rl(r,ri.ulid,i.message,t);break;case"base64":switch(t.base64Strategy){case"format:binary":ro(r,"binary",i.message,t);break;case"contentEncoding:base64":t8(r,"contentEncoding","base64",i.message,t);break;case"pattern:zod":rl(r,ri.base64,i.message,t)}break;case"nanoid":rl(r,ri.nanoid,i.message,t)}return r}function rn(e,t){return"escape"===t.patternStrategy?function(e){let t="";for(let r=0;r<e.length;r++)rs.has(e[r])||(t+="\\"),t+=e[r];return t}(e):e}let rs=new Set("ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789");function ro(e,t,r,i){e.format||e.anyOf?.some(e=>e.format)?(e.anyOf||(e.anyOf=[]),e.format&&(e.anyOf.push({format:e.format,...e.errorMessage&&i.errorMessages&&{errorMessage:{format:e.errorMessage.format}}}),delete e.format,e.errorMessage&&(delete e.errorMessage.format,0===Object.keys(e.errorMessage).length&&delete e.errorMessage)),e.anyOf.push({format:t,...r&&i.errorMessages&&{errorMessage:{format:r}}})):t8(e,"format",t,r,i)}function rl(e,t,r,i){e.pattern||e.allOf?.some(e=>e.pattern)?(e.allOf||(e.allOf=[]),e.pattern&&(e.allOf.push({pattern:e.pattern,...e.errorMessage&&i.errorMessages&&{errorMessage:{pattern:e.errorMessage.pattern}}}),delete e.pattern,e.errorMessage&&(delete e.errorMessage.pattern,0===Object.keys(e.errorMessage).length&&delete e.errorMessage)),e.allOf.push({pattern:ru(t,i),...r&&i.errorMessages&&{errorMessage:{pattern:r}}})):t8(e,"pattern",ru(t,i),r,i)}function ru(e,t){if(!t.applyRegexFlags||!e.flags)return e.source;let r={i:e.flags.includes("i"),m:e.flags.includes("m"),s:e.flags.includes("s")},i=r.i?e.source.toLowerCase():e.source,a="",n=!1,s=!1,o=!1;for(let e=0;e<i.length;e++){if(n){a+=i[e],n=!1;continue}if(r.i){if(s){if(i[e].match(/[a-z]/)){o?(a+=i[e],a+=`${i[e-2]}-${i[e]}`.toUpperCase(),o=!1):"-"===i[e+1]&&i[e+2]?.match(/[a-z]/)?(a+=i[e],o=!0):a+=`${i[e]}${i[e].toUpperCase()}`;continue}}else if(i[e].match(/[a-z]/)){a+=`[${i[e]}${i[e].toUpperCase()}]`;continue}}if(r.m){if("^"===i[e]){a+=`(^|(?<=[\r
]))`;continue}else if("$"===i[e]){a+=`($|(?=[\r
]))`;continue}}if(r.s&&"."===i[e]){a+=s?`${i[e]}\r
`:`[${i[e]}\r
]`;continue}a+=i[e],"\\"===i[e]?n=!0:s&&"]"===i[e]?s=!1:s||"["!==i[e]||(s=!0)}try{new RegExp(a)}catch{return console.warn(`Could not convert regex pattern at ${t.currentPath.join("/")} to a flag-independent form! Falling back to the flag-ignorant source`),e.source}return a}function rc(e,t){if("openAi"===t.target&&console.warn("Warning: OpenAI may not support records in schemas! Try an array of key-value pairs instead."),"openApi3"===t.target&&e.keyType?._def.typeName===b.kY.ZodEnum)return{type:"object",required:e.keyType._def.values,properties:e.keyType._def.values.reduce((r,i)=>({...r,[i]:rb(e.valueType._def,{...t,currentPath:[...t.currentPath,"properties",i]})??{}}),{}),additionalProperties:t.rejectedAdditionalProperties};let r={type:"object",additionalProperties:rb(e.valueType._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]})??t.allowedAdditionalProperties};if("openApi3"===t.target)return r;if(e.keyType?._def.typeName===b.kY.ZodString&&e.keyType._def.checks?.length){let{type:i,...a}=ra(e.keyType._def,t);return{...r,propertyNames:a}}if(e.keyType?._def.typeName===b.kY.ZodEnum)return{...r,propertyNames:{enum:e.keyType._def.values}};if(e.keyType?._def.typeName===b.kY.ZodBranded&&e.keyType._def.type._def.typeName===b.kY.ZodString&&e.keyType._def.type._def.checks?.length){let{type:i,...a}=t7(e.keyType._def,t);return{...r,propertyNames:a}}return r}let rd={ZodString:"string",ZodNumber:"number",ZodBigInt:"integer",ZodBoolean:"boolean",ZodNull:"null"},rh=(e,t)=>{let r=(e.options instanceof Map?Array.from(e.options.values()):e.options).map((e,r)=>rb(e._def,{...t,currentPath:[...t.currentPath,"anyOf",`${r}`]})).filter(e=>!!e&&(!t.strictUnions||"object"==typeof e&&Object.keys(e).length>0));return r.length?{anyOf:r}:void 0},rp=(e,t)=>{if(t.currentPath.toString()===t.propertyPath?.toString())return rb(e.innerType._def,t);let r=rb(e.innerType._def,{...t,currentPath:[...t.currentPath,"anyOf","1"]});return r?{anyOf:[{not:{}},r]}:{}},rf=(e,t)=>{if("input"===t.pipeStrategy)return rb(e.in._def,t);if("output"===t.pipeStrategy)return rb(e.out._def,t);let r=rb(e.in._def,{...t,currentPath:[...t.currentPath,"allOf","0"]}),i=rb(e.out._def,{...t,currentPath:[...t.currentPath,"allOf",r?"1":"0"]});return{allOf:[r,i].filter(e=>void 0!==e)}},rm=(e,t)=>rb(e.innerType._def,t),rg=(e,t,r)=>{switch(t){case b.kY.ZodString:return ra(e,r);case b.kY.ZodNumber:return function(e,t){let r={type:"number"};if(!e.checks)return r;for(let i of e.checks)switch(i.kind){case"int":r.type="integer",t5(r,"type",i.message,t);break;case"min":"jsonSchema7"===t.target?i.inclusive?t8(r,"minimum",i.value,i.message,t):t8(r,"exclusiveMinimum",i.value,i.message,t):(i.inclusive||(r.exclusiveMinimum=!0),t8(r,"minimum",i.value,i.message,t));break;case"max":"jsonSchema7"===t.target?i.inclusive?t8(r,"maximum",i.value,i.message,t):t8(r,"exclusiveMaximum",i.value,i.message,t):(i.inclusive||(r.exclusiveMaximum=!0),t8(r,"maximum",i.value,i.message,t));break;case"multipleOf":t8(r,"multipleOf",i.value,i.message,t)}return r}(e,r);case b.kY.ZodObject:return function(e,t){let r="openAi"===t.target,i={type:"object",properties:{}},a=[],n=e.shape();for(let e in n){let s=n[e];if(void 0===s||void 0===s._def)continue;let o=function(e){try{return e.isOptional()}catch{return!0}}(s);o&&r&&(s instanceof b.Ii&&(s=s._def.innerType),s.isNullable()||(s=s.nullable()),o=!1);let l=rb(s._def,{...t,currentPath:[...t.currentPath,"properties",e],propertyPath:[...t.currentPath,"properties",e]});void 0!==l&&(i.properties[e]=l,o||a.push(e))}a.length&&(i.required=a);let s=function(e,t){if("ZodNever"!==e.catchall._def.typeName)return rb(e.catchall._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]});switch(e.unknownKeys){case"passthrough":return t.allowedAdditionalProperties;case"strict":return t.rejectedAdditionalProperties;case"strip":return"strict"===t.removeAdditionalStrategy?t.allowedAdditionalProperties:t.rejectedAdditionalProperties}}(e,t);return void 0!==s&&(i.additionalProperties=s),i}(e,r);case b.kY.ZodBigInt:return function(e,t){let r={type:"integer",format:"int64"};if(!e.checks)return r;for(let i of e.checks)switch(i.kind){case"min":"jsonSchema7"===t.target?i.inclusive?t8(r,"minimum",i.value,i.message,t):t8(r,"exclusiveMinimum",i.value,i.message,t):(i.inclusive||(r.exclusiveMinimum=!0),t8(r,"minimum",i.value,i.message,t));break;case"max":"jsonSchema7"===t.target?i.inclusive?t8(r,"maximum",i.value,i.message,t):t8(r,"exclusiveMaximum",i.value,i.message,t):(i.inclusive||(r.exclusiveMaximum=!0),t8(r,"maximum",i.value,i.message,t));break;case"multipleOf":t8(r,"multipleOf",i.value,i.message,t)}return r}(e,r);case b.kY.ZodBoolean:return{type:"boolean"};case b.kY.ZodDate:return function e(t,r,i){let a=i??r.dateStrategy;if(Array.isArray(a))return{anyOf:a.map((i,a)=>e(t,r,i))};switch(a){case"string":case"format:date-time":return{type:"string",format:"date-time"};case"format:date":return{type:"string",format:"date"};case"integer":return rt(t,r)}}(e,r);case b.kY.ZodUndefined:return{not:{}};case b.kY.ZodNull:return"openApi3"===r.target?{enum:["null"],nullable:!0}:{type:"null"};case b.kY.ZodArray:return function(e,t){let r={type:"array"};return e.type?._def&&e.type?._def?.typeName!==b.kY.ZodAny&&(r.items=rb(e.type._def,{...t,currentPath:[...t.currentPath,"items"]})),e.minLength&&t8(r,"minItems",e.minLength.value,e.minLength.message,t),e.maxLength&&t8(r,"maxItems",e.maxLength.value,e.maxLength.message,t),e.exactLength&&(t8(r,"minItems",e.exactLength.value,e.exactLength.message,t),t8(r,"maxItems",e.exactLength.value,e.exactLength.message,t)),r}(e,r);case b.kY.ZodUnion:case b.kY.ZodDiscriminatedUnion:return function(e,t){if("openApi3"===t.target)return rh(e,t);let r=e.options instanceof Map?Array.from(e.options.values()):e.options;if(r.every(e=>e._def.typeName in rd&&(!e._def.checks||!e._def.checks.length))){let e=r.reduce((e,t)=>{let r=rd[t._def.typeName];return r&&!e.includes(r)?[...e,r]:e},[]);return{type:e.length>1?e:e[0]}}if(r.every(e=>"ZodLiteral"===e._def.typeName&&!e.description)){let e=r.reduce((e,t)=>{let r=typeof t._def.value;switch(r){case"string":case"number":case"boolean":return[...e,r];case"bigint":return[...e,"integer"];case"object":if(null===t._def.value)return[...e,"null"];default:return e}},[]);if(e.length===r.length){let t=e.filter((e,t,r)=>r.indexOf(e)===t);return{type:t.length>1?t:t[0],enum:r.reduce((e,t)=>e.includes(t._def.value)?e:[...e,t._def.value],[])}}}else if(r.every(e=>"ZodEnum"===e._def.typeName))return{type:"string",enum:r.reduce((e,t)=>[...e,...t._def.values.filter(t=>!e.includes(t))],[])};return rh(e,t)}(e,r);case b.kY.ZodIntersection:return function(e,t){let r=[rb(e.left._def,{...t,currentPath:[...t.currentPath,"allOf","0"]}),rb(e.right._def,{...t,currentPath:[...t.currentPath,"allOf","1"]})].filter(e=>!!e),i="jsonSchema2019-09"===t.target?{unevaluatedProperties:!1}:void 0,a=[];return r.forEach(e=>{if(rr(e))a.push(...e.allOf),void 0===e.unevaluatedProperties&&(i=void 0);else{let t=e;if("additionalProperties"in e&&!1===e.additionalProperties){let{additionalProperties:r,...i}=e;t=i}else i=void 0;a.push(t)}}),a.length?{allOf:a,...i}:void 0}(e,r);case b.kY.ZodTuple:return function(e,t){return e.rest?{type:"array",minItems:e.items.length,items:e.items.map((e,r)=>rb(e._def,{...t,currentPath:[...t.currentPath,"items",`${r}`]})).reduce((e,t)=>void 0===t?e:[...e,t],[]),additionalItems:rb(e.rest._def,{...t,currentPath:[...t.currentPath,"additionalItems"]})}:{type:"array",minItems:e.items.length,maxItems:e.items.length,items:e.items.map((e,r)=>rb(e._def,{...t,currentPath:[...t.currentPath,"items",`${r}`]})).reduce((e,t)=>void 0===t?e:[...e,t],[])}}(e,r);case b.kY.ZodRecord:return rc(e,r);case b.kY.ZodLiteral:return function(e,t){let r=typeof e.value;return"bigint"!==r&&"number"!==r&&"boolean"!==r&&"string"!==r?{type:Array.isArray(e.value)?"array":"object"}:"openApi3"===t.target?{type:"bigint"===r?"integer":r,enum:[e.value]}:{type:"bigint"===r?"integer":r,const:e.value}}(e,r);case b.kY.ZodEnum:return{type:"string",enum:Array.from(e.values)};case b.kY.ZodNativeEnum:return function(e){let t=e.values,r=Object.keys(e.values).filter(e=>"number"!=typeof t[t[e]]).map(e=>t[e]),i=Array.from(new Set(r.map(e=>typeof e)));return{type:1===i.length?"string"===i[0]?"string":"number":["string","number"],enum:r}}(e);case b.kY.ZodNullable:return function(e,t){if(["ZodString","ZodNumber","ZodBigInt","ZodBoolean","ZodNull"].includes(e.innerType._def.typeName)&&(!e.innerType._def.checks||!e.innerType._def.checks.length))return"openApi3"===t.target?{type:rd[e.innerType._def.typeName],nullable:!0}:{type:[rd[e.innerType._def.typeName],"null"]};if("openApi3"===t.target){let r=rb(e.innerType._def,{...t,currentPath:[...t.currentPath]});return r&&"$ref"in r?{allOf:[r],nullable:!0}:r&&{...r,nullable:!0}}let r=rb(e.innerType._def,{...t,currentPath:[...t.currentPath,"anyOf","0"]});return r&&{anyOf:[r,{type:"null"}]}}(e,r);case b.kY.ZodOptional:return rp(e,r);case b.kY.ZodMap:return function(e,t){return"record"===t.mapStrategy?rc(e,t):{type:"array",maxItems:125,items:{type:"array",items:[rb(e.keyType._def,{...t,currentPath:[...t.currentPath,"items","items","0"]})||{},rb(e.valueType._def,{...t,currentPath:[...t.currentPath,"items","items","1"]})||{}],minItems:2,maxItems:2}}}(e,r);case b.kY.ZodSet:return function(e,t){let r={type:"array",uniqueItems:!0,items:rb(e.valueType._def,{...t,currentPath:[...t.currentPath,"items"]})};return e.minSize&&t8(r,"minItems",e.minSize.value,e.minSize.message,t),e.maxSize&&t8(r,"maxItems",e.maxSize.value,e.maxSize.message,t),r}(e,r);case b.kY.ZodLazy:return()=>e.getter()._def;case b.kY.ZodPromise:return rb(e.type._def,r);case b.kY.ZodNaN:case b.kY.ZodNever:return{not:{}};case b.kY.ZodEffects:return function(e,t){return"input"===t.effectStrategy?rb(e.schema._def,t):{}}(e,r);case b.kY.ZodAny:case b.kY.ZodUnknown:return{};case b.kY.ZodDefault:return function(e,t){return{...rb(e.innerType._def,t),default:e.defaultValue()}}(e,r);case b.kY.ZodBranded:return t7(e,r);case b.kY.ZodReadonly:return rm(e,r);case b.kY.ZodCatch:return re(e,r);case b.kY.ZodPipeline:return rf(e,r);case b.kY.ZodFunction:case b.kY.ZodVoid:case b.kY.ZodSymbol:default:return}};function rb(e,t,r=!1){let i=t.seen.get(e);if(t.override){let a=t.override?.(e,t,i,r);if(a!==t3)return a}if(i&&!r){let e=ry(i,t);if(void 0!==e)return e}let a={def:e,path:t.currentPath,jsonSchema:void 0};t.seen.set(e,a);let n=rg(e,e.typeName,t),s="function"==typeof n?rb(n(),t):n;if(s&&rw(e,t,s),t.postProcess){let r=t.postProcess(s,e,t);return a.jsonSchema=s,r}return a.jsonSchema=s,s}let ry=(e,t)=>{switch(t.$refStrategy){case"root":return{$ref:e.path.join("/")};case"relative":return{$ref:rv(t.currentPath,e.path)};case"none":case"seen":if(e.path.length<t.currentPath.length&&e.path.every((e,r)=>t.currentPath[r]===e))return console.warn(`Recursive reference detected at ${t.currentPath.join("/")}! Defaulting to any`),{};return"seen"===t.$refStrategy?{}:void 0}},rv=(e,t)=>{let r=0;for(;r<e.length&&r<t.length&&e[r]===t[r];r++);return[(e.length-r).toString(),...t.slice(r)].join("/")},rw=(e,t,r)=>(e.description&&(r.description=e.description,t.markdownDescription&&(r.markdownDescription=e.description)),r),r_=(e,t)=>{let r=t6(t),i="object"==typeof t&&t.definitions?Object.entries(t.definitions).reduce((e,[t,i])=>({...e,[t]:rb(i._def,{...r,currentPath:[...r.basePath,r.definitionPath,t]},!0)??{}}),{}):void 0,a="string"==typeof t?t:t?.nameStrategy==="title"?void 0:t?.name,n=rb(e._def,void 0===a?r:{...r,currentPath:[...r.basePath,r.definitionPath,a]},!1)??{},s="object"==typeof t&&void 0!==t.name&&"title"===t.nameStrategy?t.name:void 0;void 0!==s&&(n.title=s);let o=void 0===a?i?{...n,[r.definitionPath]:i}:n:{$ref:[..."relative"===r.$refStrategy?[]:r.basePath,r.definitionPath,a].join("/"),[r.definitionPath]:{...i,[a]:n}};return"jsonSchema7"===r.target?o.$schema="http://json-schema.org/draft-07/schema#":("jsonSchema2019-09"===r.target||"openAi"===r.target)&&(o.$schema="https://json-schema.org/draft/2019-09/schema#"),"openAi"===r.target&&("anyOf"in o||"oneOf"in o||"allOf"in o||"type"in o&&Array.isArray(o.type))&&console.warn("Warning: OpenAI may not support schemas with unions as roots! Try wrapping it in an object property."),o};function rE(e){return e.replace(/[^a-zA-Z-_0-9]/g,"_")}async function rO(e,t){let{backgroundColor:r="white"}=t??{},i=btoa(e);void 0!==r&&(/^#(?:[0-9a-fA-F]{3}){1,2}$/.test(r)||(r=`!${r}`));let a=`https://mermaid.ink/img/${i}?bgColor=${r}`,n=await fetch(a);if(!n.ok)throw Error(`Failed to render the graph using the Mermaid.INK API.
Status code: ${n.status}
Status text: ${n.statusText}`);return await n.blob()}function rI(e){if(!L(e.id))return e.id;if(!t1(e.data))return e.data.name??"UnknownSchema";try{let t=e.data.getName();return(t=t.startsWith("Runnable")?t.slice(8):t).length>42&&(t=`${t.substring(0,42)}...`),t}catch(t){return e.data.getName()}}class rS{constructor(){Object.defineProperty(this,"nodes",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"edges",{enumerable:!0,configurable:!0,writable:!0,value:[]})}toJSON(){let e={};return Object.values(this.nodes).forEach((t,r)=>{e[t.id]=L(t.id)?r:t.id}),{nodes:Object.values(this.nodes).map(t=>({id:e[t.id],...t1(t.data)?{type:"runnable",data:{id:t.data.lc_id,name:t.data.getName()}}:{type:"schema",data:{...r_(t.data.schema),title:t.data.name}}})),edges:this.edges.map(t=>{let r={source:e[t.source],target:e[t.target]};return void 0!==t.data&&(r.data=t.data),void 0!==t.conditional&&(r.conditional=t.conditional),r})}}addNode(e,t){if(void 0!==t&&void 0!==this.nodes[t])throw Error(`Node with id ${t} already exists`);let r=t||S(),i={id:r,data:e};return this.nodes[r]=i,i}removeNode(e){delete this.nodes[e.id],this.edges=this.edges.filter(t=>t.source!==e.id&&t.target!==e.id)}addEdge(e,t,r,i){if(void 0===this.nodes[e.id])throw Error(`Source node ${e.id} not in graph`);if(void 0===this.nodes[t.id])throw Error(`Target node ${t.id} not in graph`);let a={source:e.id,target:t.id,data:r,conditional:i};return this.edges.push(a),a}firstNode(){let e=new Set(this.edges.map(e=>e.target)),t=[];return Object.values(this.nodes).forEach(r=>{e.has(r.id)||t.push(r)}),t[0]}lastNode(){let e=new Set(this.edges.map(e=>e.source)),t=[];return Object.values(this.nodes).forEach(r=>{e.has(r.id)||t.push(r)}),t[0]}extend(e,t=""){let r=t;Object.values(e.nodes).map(e=>e.id).every(L)&&(r="");let i=e=>r?`${r}:${e}`:e;Object.entries(e.nodes).forEach(([e,t])=>{this.nodes[i(e)]={...t,id:i(e)}});let a=e.edges.map(e=>({...e,source:i(e.source),target:i(e.target)}));this.edges=[...this.edges,...a];let n=e.firstNode(),s=e.lastNode();return[n?{id:i(n.id),data:n.data}:void 0,s?{id:i(s.id),data:s.data}:void 0]}trimFirstNode(){let e=this.firstNode();if(e){let t=this.edges.filter(t=>t.source===e.id);(1===Object.keys(this.nodes).length||1===t.length)&&this.removeNode(e)}}trimLastNode(){let e=this.lastNode();if(e){let t=this.edges.filter(t=>t.target===e.id);(1===Object.keys(this.nodes).length||1===t.length)&&this.removeNode(e)}}drawMermaid(e){let{withStyles:t,curveStyle:r,nodeColors:i={start:"#ffdfba",end:"#baffc9",other:"#fad7de"},wrapLabelNWords:a}=e??{},n={};for(let e of Object.values(this.nodes))n[e.id]=rI(e);let s=this.firstNode(),o=s?rI(s):void 0,l=this.lastNode(),u=l?rI(l):void 0;return function(e,t,r){let{firstNodeLabel:i,lastNodeLabel:a,nodeColors:n,withStyles:s=!0,curveStyle:o="linear",wrapLabelNWords:l=9}=r??{},u=s?`%%{init: {'flowchart': {'curve': '${o}'}}}%%
graph TD;
`:"graph TD;\n";if(s){let t="default",r={[t]:"{0}([{1}]):::otherclass"};for(let n of(void 0!==i&&(r[i]="{0}[{0}]:::startclass"),void 0!==a&&(r[a]="{0}[{0}]:::endclass"),Object.values(e))){let e=r[n]??r[t],i=rE(n),a=n.split(":"),s=a[a.length-1];u+=`	${e.replace(/\{0\}/g,i).replace(/\{1\}/g,s)};
`}}let c="";for(let r of t){var d,h;let t=r.source.includes(":")?r.source.split(":")[0]:void 0,i=r.target.includes(":")?r.target.split(":")[0]:void 0;""!==c&&(c!==t||c!==i)&&(u+="	end\n",c=""),""===c&&void 0!==t&&t===i&&(u=`	subgraph ${t}
`,c=t);let[a,n]=(d=r,[(h=e)[d.source]??d.source,h[d.target]??d.target]),s="";if(void 0!==r.data){let e=r.data,t=e.split(" ");t.length>l&&(e=t.reduce((e,t,r)=>(r%l==0&&e.push(""),e[e.length-1]+=` ${t}`,e),[]).join("<br>")),s=r.conditional?` -. ${e} .-> `:` -- ${e} --> `}else s=r.conditional?" -.-> ":" --\x3e ";u+=`	${rE(a)}${s}${rE(n)};
`}return""!==c&&(u+="end\n"),s&&void 0!==n&&(u+=function(e){let t="";for(let[r,i]of Object.entries(e))t+=`	classDef ${r}class fill:${i};
`;return t}(n)),u}(n,this.edges,{firstNodeLabel:o,lastNodeLabel:u,withStyles:t,curveStyle:r,nodeColors:i,wrapLabelNWords:a})}async drawMermaidPng(e){return rO(this.drawMermaid(e),{backgroundColor:e?.backgroundColor})}}function rT(e){return"object"==typeof e&&null!==e&&"function"==typeof e[Symbol.iterator]&&"function"==typeof e.next}let rP=e=>null!=e&&"object"==typeof e&&"next"in e&&"function"==typeof e.next;function r$(e){return"object"==typeof e&&null!==e&&"function"==typeof e[Symbol.asyncIterator]}function*rA(e,t){for(;;){let{value:r,done:i}=tA.runWithConfig(e,t.next.bind(t),!0);if(i)break;yield r}}async function*rR(e,t){let r=t[Symbol.asyncIterator]();for(;;){let{value:i,done:a}=await tA.runWithConfig(e,r.next.bind(t),!0);if(a)break;yield i}}function rj(e,t){return!e||Array.isArray(e)||e instanceof Date||"object"!=typeof e?{[t]:e}:e}class rx extends eW{constructor(){super(...arguments),Object.defineProperty(this,"lc_runnable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}getName(e){let t=this.name??this.constructor.lc_name()??this.constructor.name;return e?`${t}${e}`:t}bind(e){return new rk({bound:this,kwargs:e,config:{}})}map(){return new rN({bound:this})}withRetry(e){return new rC({bound:this,kwargs:{},config:{},maxAttemptNumber:e?.stopAfterAttempt,...e})}withConfig(e){return new rk({bound:this,config:e,kwargs:{}})}withFallbacks(e){return new rF({runnable:this,fallbacks:Array.isArray(e)?e:e.fallbacks})}_getOptionsList(e,t=0){if(Array.isArray(e)&&e.length!==t)throw Error(`Passed "options" must be an array with the same length as the inputs, but got ${e.length} options for ${t} inputs`);if(Array.isArray(e))return e.map(tW);if(t>1&&!Array.isArray(e)&&e.runId){console.warn("Provided runId will be used only for the first element of the batch.");let r=Object.fromEntries(Object.entries(e).filter(([e])=>"runId"!==e));return Array.from({length:t},(t,i)=>tW(0===i?e:r))}return Array.from({length:t},()=>tW(e))}async batch(e,t,r){let i=this._getOptionsList(t??{},e.length),a=new tQ({maxConcurrency:i[0]?.maxConcurrency??r?.maxConcurrency,onFailedAttempt:e=>{throw e}});return Promise.all(e.map((e,t)=>a.call(async()=>{try{return await this.invoke(e,i[t])}catch(e){if(r?.returnExceptions)return e;throw e}})))}async *_streamIterator(e,t){yield this.invoke(e,t)}async stream(e,t){let r=tW(t),i=new tN({generator:this._streamIterator(e,r),config:r});return await i.setup,tj.fromAsyncGenerator(i)}_separateRunnableConfigFromCallOptions(e){let t;t=void 0===e?tW(e):tW({callbacks:e.callbacks,tags:e.tags,metadata:e.metadata,runName:e.runName,configurable:e.configurable,recursionLimit:e.recursionLimit,maxConcurrency:e.maxConcurrency,runId:e.runId,timeout:e.timeout,signal:e.signal});let r={...e};return delete r.callbacks,delete r.tags,delete r.metadata,delete r.runName,delete r.configurable,delete r.recursionLimit,delete r.maxConcurrency,delete r.runId,delete r.timeout,delete r.signal,[t,r]}async _callWithConfig(e,t,r){let i,a=tW(r),n=await tJ(a),s=await n?.handleChainStart(this.toJSON(),rj(t,"input"),a.runId,a?.runType,void 0,void 0,a?.runName??this.getName());delete a.runId;try{let n=e.call(this,t,a,s);i=await tR(n,r?.signal)}catch(e){throw await s?.handleChainError(e),e}return await s?.handleChainEnd(rj(i,"output")),i}async _batchWithConfig(e,t,r,i){let a,n=this._getOptionsList(r??{},t.length),s=await Promise.all(n.map(tJ)),o=await Promise.all(s.map(async(e,r)=>{let i=await e?.handleChainStart(this.toJSON(),rj(t[r],"input"),n[r].runId,n[r].runType,void 0,void 0,n[r].runName??this.getName());return delete n[r].runId,i}));try{let r=e.call(this,t,n,o,i);a=await tR(r,n?.[0]?.signal)}catch(e){throw await Promise.all(o.map(t=>t?.handleChainError(e))),e}return await Promise.all(o.map(e=>e?.handleChainEnd(rj(a,"output")))),a}async *_transformStreamWithConfig(e,t,r){let i,a,n,s=!0,o=!0,l=tW(r),u=await tJ(l);async function*c(){for await(let t of e){if(s)if(void 0===i)i=t;else try{i=tk(i,t)}catch{i=void 0,s=!1}yield t}}try{let e=await tC(t.bind(this),c(),async()=>u?.handleChainStart(this.toJSON(),{input:""},l.runId,l.runType,void 0,void 0,l.runName??this.getName()),r?.signal,l);delete l.runId,n=e.setup;let i=n?.handlers.find(tG),s=e.output;void 0!==i&&void 0!==n&&(s=i.tapOutputIterable(n.runId,s));let d=n?.handlers.find(tD);for await(let e of(void 0!==d&&void 0!==n&&(s=d.tapOutputIterable(n.runId,s)),s))if(yield e,o)if(void 0===a)a=e;else try{a=tk(a,e)}catch{a=void 0,o=!1}}catch(e){throw await n?.handleChainError(e,void 0,void 0,void 0,{inputs:rj(i,"input")}),e}await n?.handleChainEnd(a??{},void 0,void 0,void 0,{inputs:rj(i,"input")})}getGraph(e){let t=new rS,r=t.addNode({name:`${this.getName()}Input`,schema:b.z.any()}),i=t.addNode(this),a=t.addNode({name:`${this.getName()}Output`,schema:b.z.any()});return t.addEdge(r,i),t.addEdge(i,a),t}pipe(e){return new rL({first:this,last:rH(e)})}pick(e){return this.pipe(new rz(e))}assign(e){return this.pipe(new rB(new rM({steps:e})))}async *transform(e,t){let r;for await(let t of e)r=void 0===r?t:tk(r,t);yield*this._streamIterator(r,tW(t))}async *streamLog(e,t,r){let i=new tH({...r,autoClose:!1,_schemaFormat:"original"}),a=tW(t);yield*this._streamLog(e,i,a)}async *_streamLog(e,t,r){let{callbacks:i}=r;if(void 0===i)r.callbacks=[t];else if(Array.isArray(i))r.callbacks=i.concat([t]);else{let e=i.copy();e.addHandler(t,!0),r.callbacks=e}let a=this.stream(e,r),n=async function(){try{for await(let e of(await a)){let r=new tL({ops:[{op:"add",path:"/streamed_output/-",value:e}]});await t.writer.write(r)}}finally{await t.writer.close()}}();try{for await(let e of t)yield e}finally{await n}}streamEvents(e,t,r){let i;if("v1"===t.version)i=this._streamEventsV1(e,t,r);else if("v2"===t.version)i=this._streamEventsV2(e,t,r);else throw Error('Only versions "v1" and "v2" of the schema are currently supported.');if("text/event-stream"!==t.encoding)return tj.fromAsyncGenerator(i);var a=i;let n=new TextEncoder,s=new ReadableStream({async start(e){for await(let t of a)e.enqueue(n.encode(`event: data
data: ${JSON.stringify(t)}

`));e.enqueue(n.encode("event: end\n\n")),e.close()}});return tj.fromReadableStream(s)}async *_streamEventsV2(e,t,r){let i,a=new tq({...r,autoClose:!1}),n=tW(t),s=n.runId??S();n.runId=s;let o=n.callbacks;if(void 0===o)n.callbacks=[a];else if(Array.isArray(o))n.callbacks=o.concat(a);else{let e=o.copy();e.addHandler(a,!0),n.callbacks=e}let l=this,u=async function(){try{let t=await l.stream(e,n);for await(let e of a.tapOutputIterable(s,t));}finally{await a.finish()}}(),c=!1;try{for await(let t of a){if(!c){t.data.input=e,c=!0,i=t.run_id,yield t;continue}t.run_id===i&&t.event.endsWith("_end")&&t.data?.input&&delete t.data.input,yield t}}finally{await u}}async *_streamEventsV1(e,t,r){let i,a=!1,n=tW(t),s=n.tags??[],o=n.metadata??{},l=n.runName??this.getName(),u=new tH({...r,autoClose:!1,_schemaFormat:"streaming_events"}),c=new t4({...r});for await(let t of this._streamLog(e,u,n)){if(void 0===(i=i?i.concat(t):tM.fromRunLogPatch(t)).state)throw Error('Internal error: "streamEvents" state is missing. Please open a bug report.');if(!a){a=!0;let t={...i.state},r={run_id:t.id,event:`on_${t.type}_start`,name:l,tags:s,metadata:o,data:{input:e}};c.includeEvent(r,t.type)&&(yield r)}for(let e of[...new Set(t.ops.filter(e=>e.path.startsWith("/logs/")).map(e=>e.path.split("/")[2]))]){let t,r={},a=i.state.logs[e];if("start"==(t=void 0===a.end_time?a.streamed_output.length>0?"stream":"start":"end"))void 0!==a.inputs&&(r.input=a.inputs);else if("end"===t)void 0!==a.inputs&&(r.input=a.inputs),r.output=a.final_output;else if("stream"===t){let e=a.streamed_output.length;if(1!==e)throw Error(`Expected exactly one chunk of streamed output, got ${e} instead. Encountered in: "${a.name}"`);r={chunk:a.streamed_output[0]},a.streamed_output=[]}yield{event:`on_${a.type}_${t}`,name:a.name,run_id:a.id,tags:a.tags,metadata:a.metadata,data:r}}let{state:r}=i;if(r.streamed_output.length>0){let e=r.streamed_output.length;if(1!==e)throw Error(`Expected exactly one chunk of streamed output, got ${e} instead. Encountered in: "${r.name}"`);let t={chunk:r.streamed_output[0]};r.streamed_output=[];let i={event:`on_${r.type}_stream`,run_id:r.id,tags:s,metadata:o,name:l,data:t};c.includeEvent(i,r.type)&&(yield i)}}let d=i?.state;if(void 0!==d){let e={event:`on_${d.type}_end`,name:l,run_id:d.id,tags:s,metadata:o,data:{output:d.final_output}};c.includeEvent(e,d.type)&&(yield e)}}static isRunnable(e){return t1(e)}withListeners({onStart:e,onEnd:t,onError:r}){return new rk({bound:this,config:{},configFactories:[i=>({callbacks:[new t0({config:i,onStart:e,onEnd:t,onError:r})]})]})}asTool(e){var t=this,r=e;let i=r.name??t.getName(),a=r.description??r.schema?.description;return new rG(r.schema.constructor===b.z.ZodString?{name:i,description:a,schema:b.z.object({input:b.z.string()}).transform(e=>e.input),bound:t}:{name:i,description:a,schema:r.schema,bound:t})}}class rk extends rx{static lc_name(){return"RunnableBinding"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"bound",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"config",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"kwargs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"configFactories",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.bound=e.bound,this.kwargs=e.kwargs,this.config=e.config,this.configFactories=e.configFactories}getName(e){return this.bound.getName(e)}async _mergeConfig(...e){let t=tZ(this.config,...e);return tZ(t,...this.configFactories?await Promise.all(this.configFactories.map(async e=>await e(t))):[])}bind(e){return new this.constructor({bound:this.bound,kwargs:{...this.kwargs,...e},config:this.config})}withConfig(e){return new this.constructor({bound:this.bound,kwargs:this.kwargs,config:{...this.config,...e}})}withRetry(e){return new this.constructor({bound:this.bound.withRetry(e),kwargs:this.kwargs,config:this.config})}async invoke(e,t){return this.bound.invoke(e,await this._mergeConfig(tW(t),this.kwargs))}async batch(e,t,r){let i=Array.isArray(t)?await Promise.all(t.map(async e=>this._mergeConfig(tW(e),this.kwargs))):await this._mergeConfig(tW(t),this.kwargs);return this.bound.batch(e,i,r)}async *_streamIterator(e,t){yield*this.bound._streamIterator(e,await this._mergeConfig(tW(t),this.kwargs))}async stream(e,t){return this.bound.stream(e,await this._mergeConfig(tW(t),this.kwargs))}async *transform(e,t){yield*this.bound.transform(e,await this._mergeConfig(tW(t),this.kwargs))}streamEvents(e,t,r){let i=this,a=async function*(){yield*i.bound.streamEvents(e,{...await i._mergeConfig(tW(t),i.kwargs),version:t.version},r)};return tj.fromAsyncGenerator(a())}static isRunnableBinding(e){return e.bound&&rx.isRunnable(e.bound)}withListeners({onStart:e,onEnd:t,onError:r}){return new rk({bound:this.bound,kwargs:this.kwargs,config:this.config,configFactories:[i=>({callbacks:[new t0({config:i,onStart:e,onEnd:t,onError:r})]})]})}}class rN extends rx{static lc_name(){return"RunnableEach"}constructor(e){super(e),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"bound",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.bound=e.bound}bind(e){return new rN({bound:this.bound.bind(e)})}async invoke(e,t){return this._callWithConfig(this._invoke,e,t)}async _invoke(e,t,r){return this.bound.batch(e,tV(t,{callbacks:r?.getChild()}))}withListeners({onStart:e,onEnd:t,onError:r}){return new rN({bound:this.bound.withListeners({onStart:e,onEnd:t,onError:r})})}}class rC extends rk{static lc_name(){return"RunnableRetry"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"maxAttemptNumber",{enumerable:!0,configurable:!0,writable:!0,value:3}),Object.defineProperty(this,"onFailedAttempt",{enumerable:!0,configurable:!0,writable:!0,value:()=>{}}),this.maxAttemptNumber=e.maxAttemptNumber??this.maxAttemptNumber,this.onFailedAttempt=e.onFailedAttempt??this.onFailedAttempt}_patchConfigForRetry(e,t,r){let i=e>1?`retry:attempt:${e}`:void 0;return tV(t,{callbacks:r?.getChild(i)})}async _invoke(e,t,r){return y(i=>super.invoke(e,this._patchConfigForRetry(i,t,r)),{onFailedAttempt:t=>this.onFailedAttempt(t,e),retries:Math.max(this.maxAttemptNumber-1,0),randomize:!0})}async invoke(e,t){return this._callWithConfig(this._invoke,e,t)}async _batch(e,t,r,i){let a={};try{await y(async n=>{let s,o=e.map((e,t)=>t).filter(e=>void 0===a[e.toString()]||a[e.toString()]instanceof Error),l=o.map(t=>e[t]),u=o.map(e=>this._patchConfigForRetry(n,t?.[e],r?.[e])),c=await super.batch(l,u,{...i,returnExceptions:!0});for(let e=0;e<c.length;e+=1){let t=c[e],r=o[e];t instanceof Error&&void 0===s&&((s=t).input=l[e]),a[r.toString()]=t}if(s)throw s;return c},{onFailedAttempt:e=>this.onFailedAttempt(e,e.input),retries:Math.max(this.maxAttemptNumber-1,0),randomize:!0})}catch(e){if(i?.returnExceptions!==!0)throw e}return Object.keys(a).sort((e,t)=>parseInt(e,10)-parseInt(t,10)).map(e=>a[parseInt(e,10)])}async batch(e,t,r){return this._batchWithConfig(this._batch.bind(this),e,t,r)}}class rL extends rx{static lc_name(){return"RunnableSequence"}constructor(e){super(e),Object.defineProperty(this,"first",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"middle",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"last",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),this.first=e.first,this.middle=e.middle??this.middle,this.last=e.last,this.name=e.name}get steps(){return[this.first,...this.middle,this.last]}async invoke(e,t){let r,i=tW(t),a=await tJ(i),n=await a?.handleChainStart(this.toJSON(),rj(e,"input"),i.runId,void 0,void 0,void 0,i?.runName);delete i.runId;let s=e;try{let e=[this.first,...this.middle];for(let r=0;r<e.length;r+=1){let a=e[r].invoke(s,tV(i,{callbacks:n?.getChild(`seq:step:${r+1}`)}));s=await tR(a,t?.signal)}if(t?.signal?.aborted)throw Error("Aborted");r=await this.last.invoke(s,tV(i,{callbacks:n?.getChild(`seq:step:${this.steps.length}`)}))}catch(e){throw await n?.handleChainError(e),e}return await n?.handleChainEnd(rj(r,"output")),r}async batch(e,t,r){let i=this._getOptionsList(t??{},e.length),a=await Promise.all(i.map(tJ)),n=await Promise.all(a.map(async(t,r)=>{let a=await t?.handleChainStart(this.toJSON(),rj(e[r],"input"),i[r].runId,void 0,void 0,void 0,i[r].runName);return delete i[r].runId,a})),s=e;try{for(let e=0;e<this.steps.length;e+=1){let t=this.steps[e].batch(s,n.map((t,r)=>{let a=t?.getChild(`seq:step:${e+1}`);return tV(i[r],{callbacks:a})}),r);s=await tR(t,i[0]?.signal)}}catch(e){throw await Promise.all(n.map(t=>t?.handleChainError(e))),e}return await Promise.all(n.map(e=>e?.handleChainEnd(rj(s,"output")))),s}async *_streamIterator(e,t){let r,i=await tJ(t),{runId:a,...n}=t??{},s=await i?.handleChainStart(this.toJSON(),rj(e,"input"),a,void 0,void 0,void 0,n?.runName),o=[this.first,...this.middle,this.last],l=!0;async function*u(){yield e}try{let e=o[0].transform(u(),tV(n,{callbacks:s?.getChild("seq:step:1")}));for(let t=1;t<o.length;t+=1){let r=o[t];e=await r.transform(e,tV(n,{callbacks:s?.getChild(`seq:step:${t+1}`)}))}for await(let i of e)if(t?.signal?.throwIfAborted(),yield i,l)if(void 0===r)r=i;else try{r=tk(r,i)}catch(e){r=void 0,l=!1}}catch(e){throw await s?.handleChainError(e),e}await s?.handleChainEnd(rj(r,"output"))}getGraph(e){let t=new rS,r=null;return this.steps.forEach((i,a)=>{let n=i.getGraph(e);0!==a&&n.trimFirstNode(),a!==this.steps.length-1&&n.trimLastNode(),t.extend(n);let s=n.firstNode();if(!s)throw Error(`Runnable ${i} has no first node`);r&&t.addEdge(r,s),r=n.lastNode()}),t}pipe(e){return new rL(rL.isRunnableSequence(e)?{first:this.first,middle:this.middle.concat([this.last,e.first,...e.middle]),last:e.last,name:this.name??e.name}:{first:this.first,middle:[...this.middle,this.last],last:rH(e),name:this.name})}static isRunnableSequence(e){return Array.isArray(e.middle)&&rx.isRunnable(e)}static from([e,...t],r){return new rL({first:rH(e),middle:t.slice(0,-1).map(rH),last:rH(t[t.length-1]),name:r})}}class rM extends rx{static lc_name(){return"RunnableMap"}getStepsKeys(){return Object.keys(this.steps)}constructor(e){for(let[t,r]of(super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"steps",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.steps={},Object.entries(e.steps)))this.steps[t]=rH(r)}static from(e){return new rM({steps:e})}async invoke(e,t){let r=tW(t),i=await tJ(r),a=await i?.handleChainStart(this.toJSON(),{input:e},r.runId,void 0,void 0,void 0,r?.runName);delete r.runId;let n={};try{let i=Object.entries(this.steps).map(async([t,i])=>{n[t]=await i.invoke(e,tV(r,{callbacks:a?.getChild(`map:key:${t}`)}))});await tR(Promise.all(i),t?.signal)}catch(e){throw await a?.handleChainError(e),e}return await a?.handleChainEnd(n),n}async *_transform(e,t,r){let i={...this.steps},a=tx(e,Object.keys(i).length),n=new Map(Object.entries(i).map(([e,i],n)=>{let s=i.transform(a[n],tV(r,{callbacks:t?.getChild(`map:key:${e}`)}));return[e,s.next().then(t=>({key:e,gen:s,result:t}))]}));for(;n.size;){let e=Promise.race(n.values()),{key:t,result:i,gen:a}=await tR(e,r?.signal);n.delete(t),i.done||(yield{[t]:i.value},n.set(t,a.next().then(e=>({key:t,gen:a,result:e}))))}}transform(e,t){return this._transformStreamWithConfig(e,this._transform.bind(this),t)}async stream(e,t){async function*r(){yield e}let i=tW(t),a=new tN({generator:this.transform(r(),i),config:i});return await a.setup,tj.fromAsyncGenerator(a)}}class rD extends rx{constructor(e){if(super(e),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"func",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),!eT(e.func))throw Error("RunnableTraceable requires a function that is wrapped in traceable higher-order function");this.func=e.func}async invoke(e,t){let[r]=this._getOptionsList(t??{},1),i=await tJ(r);return tR(this.func(tV(r,{callbacks:i}),e),r?.signal)}async *_streamIterator(e,t){let[r]=this._getOptionsList(t??{},1),i=await this.invoke(e,t);if(r$(i)){for await(let e of i)r?.signal?.throwIfAborted(),yield e;return}if(rP(i)){for(;;){r?.signal?.throwIfAborted();let e=i.next();if(e.done)break;yield e.value}return}yield i}static from(e){return new rD({func:e})}}class rU extends rx{static lc_name(){return"RunnableLambda"}constructor(e){if(eT(e.func))return rD.from(e.func);if(super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"func",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),eT(e.func))throw Error("RunnableLambda requires a function that is not wrapped in traceable higher-order function. This shouldn't happen.");this.func=e.func}static from(e){return new rU({func:e})}async _invoke(e,t,r){return new Promise((i,a)=>{let n=tV(t,{callbacks:r?.getChild(),recursionLimit:(t?.recursionLimit??25)-1});tA.runWithConfig(n,async()=>{try{let r=await this.func(e,{...n,config:n});if(r&&rx.isRunnable(r)){if(t?.recursionLimit===0)throw Error("Recursion limit reached.");r=await r.invoke(e,{...n,recursionLimit:(n.recursionLimit??25)-1})}else if(r$(r)){let e;for await(let i of rR(n,r))if(t?.signal?.throwIfAborted(),void 0===e)e=i;else try{e=tk(e,i)}catch(t){e=i}r=e}else if(rT(r)){let e;for(let i of rA(n,r))if(t?.signal?.throwIfAborted(),void 0===e)e=i;else try{e=tk(e,i)}catch(t){e=i}r=e}i(r)}catch(e){a(e)}})})}async invoke(e,t){return this._callWithConfig(this._invoke,e,t)}async *_transform(e,t,r){let i;for await(let t of e)if(void 0===i)i=t;else try{i=tk(i,t)}catch(e){i=t}let a=tV(r,{callbacks:t?.getChild(),recursionLimit:(r?.recursionLimit??25)-1}),n=await new Promise((e,t)=>{tA.runWithConfig(a,async()=>{try{let t=await this.func(i,{...a,config:a});e(t)}catch(e){t(e)}})});if(n&&rx.isRunnable(n)){if(r?.recursionLimit===0)throw Error("Recursion limit reached.");for await(let e of(await n.stream(i,a)))yield e}else if(r$(n))for await(let e of rR(a,n))r?.signal?.throwIfAborted(),yield e;else if(rT(n))for(let e of rA(a,n))r?.signal?.throwIfAborted(),yield e;else yield n}transform(e,t){return this._transformStreamWithConfig(e,this._transform.bind(this),t)}async stream(e,t){async function*r(){yield e}let i=tW(t),a=new tN({generator:this.transform(r(),i),config:i});return await a.setup,tj.fromAsyncGenerator(a)}}class rF extends rx{static lc_name(){return"RunnableWithFallbacks"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"runnable",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"fallbacks",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.runnable=e.runnable,this.fallbacks=e.fallbacks}*runnables(){for(let e of(yield this.runnable,this.fallbacks))yield e}async invoke(e,t){let r,i=tW(t),a=await tJ(t),{runId:n,...s}=i,o=await a?.handleChainStart(this.toJSON(),rj(e,"input"),n,void 0,void 0,void 0,s?.runName);for(let t of this.runnables()){i?.signal?.throwIfAborted();try{let r=await t.invoke(e,tV(s,{callbacks:o?.getChild()}));return await o?.handleChainEnd(rj(r,"output")),r}catch(e){void 0===r&&(r=e)}}if(void 0===r)throw Error("No error stored at end of fallback.");throw await o?.handleChainError(r),r}async *_streamIterator(e,t){let r,i,a,n=tW(t),s=await tJ(t),{runId:o,...l}=n,u=await s?.handleChainStart(this.toJSON(),rj(e,"input"),o,void 0,void 0,void 0,l?.runName);for(let t of this.runnables()){n?.signal?.throwIfAborted();let a=tV(l,{callbacks:u?.getChild()});try{i=await t.stream(e,a);break}catch(e){void 0===r&&(r=e)}}if(void 0===i){let e=r??Error("No error stored at end of fallback.");throw await u?.handleChainError(e),e}try{for await(let e of i){yield e;try{a=void 0===a?a:tk(a,e)}catch(e){a=void 0}}}catch(e){throw await u?.handleChainError(e),e}await u?.handleChainEnd(rj(a,"output"))}async batch(e,t,r){let i;if(r?.returnExceptions)throw Error("Not implemented.");let a=this._getOptionsList(t??{},e.length),n=await Promise.all(a.map(e=>tJ(e))),s=await Promise.all(n.map(async(t,r)=>{let i=await t?.handleChainStart(this.toJSON(),rj(e[r],"input"),a[r].runId,void 0,void 0,void 0,a[r].runName);return delete a[r].runId,i}));for(let t of this.runnables()){a[0].signal?.throwIfAborted();try{let i=await t.batch(e,s.map((e,t)=>tV(a[t],{callbacks:e?.getChild()})),r);return await Promise.all(s.map((e,t)=>e?.handleChainEnd(rj(i[t],"output")))),i}catch(e){void 0===i&&(i=e)}}if(!i)throw Error("No error stored at end of fallbacks.");throw await Promise.all(s.map(e=>e?.handleChainError(i))),i}}function rH(e){if("function"==typeof e)return new rU({func:e});if(rx.isRunnable(e))return e;if(Array.isArray(e)||"object"!=typeof e)throw Error(`Expected a Runnable, function or object.
Instead got an unsupported type.`);{let t={};for(let[r,i]of Object.entries(e))t[r]=rH(i);return new rM({steps:t})}}class rB extends rx{static lc_name(){return"RunnableAssign"}constructor(e){e instanceof rM&&(e={mapper:e}),super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"mapper",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.mapper=e.mapper}async invoke(e,t){let r=await this.mapper.invoke(e,t);return{...e,...r}}async *_transform(e,t,r){let i=this.mapper.getStepsKeys(),[a,n]=tx(e),s=this.mapper.transform(n,tV(r,{callbacks:t?.getChild()})),o=s.next();for await(let e of a){if("object"!=typeof e||Array.isArray(e))throw Error(`RunnableAssign can only be used with objects as input, got ${typeof e}`);let t=Object.fromEntries(Object.entries(e).filter(([e])=>!i.includes(e)));Object.keys(t).length>0&&(yield t)}for await(let e of(yield(await o).value,s))yield e}transform(e,t){return this._transformStreamWithConfig(e,this._transform.bind(this),t)}async stream(e,t){async function*r(){yield e}let i=tW(t),a=new tN({generator:this.transform(r(),i),config:i});return await a.setup,tj.fromAsyncGenerator(a)}}class rz extends rx{static lc_name(){return"RunnablePick"}constructor(e){("string"==typeof e||Array.isArray(e))&&(e={keys:e}),super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"keys",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.keys=e.keys}async _pick(e){if("string"==typeof this.keys)return e[this.keys];{let t=this.keys.map(t=>[t,e[t]]).filter(e=>void 0!==e[1]);return 0===t.length?void 0:Object.fromEntries(t)}}async invoke(e,t){return this._callWithConfig(this._pick.bind(this),e,t)}async *_transform(e){for await(let t of e){let e=await this._pick(t);void 0!==e&&(yield e)}}transform(e,t){return this._transformStreamWithConfig(e,this._transform.bind(this),t)}async stream(e,t){async function*r(){yield e}let i=tW(t),a=new tN({generator:this.transform(r(),i),config:i});return await a.setup,tj.fromAsyncGenerator(a)}}class rG extends rk{constructor(e){super({bound:rL.from([rU.from(async e=>{let t;if(function(e){return!!(e&&"object"==typeof e&&"type"in e&&"tool_call"===e.type)}(e))try{t=await this.schema.parseAsync(e.args)}catch(t){throw new ts("Received tool input did not match expected schema",JSON.stringify(e.args))}else t=e;return t}).withConfig({runName:`${e.name}:parse_input`}),e.bound]).withConfig({runName:e.name}),config:e.config??{}}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"description",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"schema",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name=e.name,this.description=e.description,this.schema=e.schema}static lc_name(){return"RunnableToolLike"}}class rq extends rx{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","documents","transformers"]})}invoke(e,t){return this.transformDocuments(e)}}var rJ=r(18929),rZ=Object.defineProperty,rY=(e,t,r)=>t in e?rZ(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,rW=class{specialTokens;inverseSpecialTokens;patStr;textEncoder=new TextEncoder;textDecoder=new TextDecoder("utf-8");rankMap=new Map;textMap=new Map;constructor(e,t){for(let[t,r]of(this.patStr=e.pat_str,Object.entries(e.bpe_ranks.split("\n").filter(Boolean).reduce((e,t)=>{let[r,i,...a]=t.split(" "),n=Number.parseInt(i,10);return a.forEach((t,r)=>e[t]=n+r),e},{})))){let e=rJ.toByteArray(t);this.rankMap.set(e.join(","),r),this.textMap.set(r,e)}this.specialTokens={...e.special_tokens,...t},this.inverseSpecialTokens=Object.entries(this.specialTokens).reduce((e,[t,r])=>(e[r]=this.textEncoder.encode(t),e),{})}encode(e,t=[],r="all"){let i=RegExp(this.patStr,"ug"),a=rW.specialTokenRegex(Object.keys(this.specialTokens)),n=[],s=new Set("all"===t?Object.keys(this.specialTokens):t),o=new Set("all"===r?Object.keys(this.specialTokens).filter(e=>!s.has(e)):r);if(o.size>0){let t=rW.specialTokenRegex([...o]),r=e.match(t);if(null!=r)throw Error(`The text contains a special token that is not allowed: ${r[0]}`)}let l=0;for(;;){let t=null,r=l;for(;a.lastIndex=r,!(null==(t=a.exec(e))||s.has(t[0]));)r=t.index+1;let o=t?.index??e.length;for(let t of e.substring(l,o).matchAll(i)){let e=this.textEncoder.encode(t[0]),r=this.rankMap.get(e.join(","));if(null!=r){n.push(r);continue}n.push(...function(e,t){return 1===e.length?[t.get(e.join(","))]:(function(e,t){let r=Array.from({length:e.length},(e,t)=>({start:t,end:t+1}));for(;r.length>1;){let i=null;for(let a=0;a<r.length-1;a++){let n=e.slice(r[a].start,r[a+1].end),s=t.get(n.join(","));null!=s&&(null==i||s<i[0])&&(i=[s,a])}if(null!=i){let e=i[1];r[e]={start:r[e].start,end:r[e+1].end},r.splice(e+1,1)}else break}return r})(e,t).map(r=>t.get(e.slice(r.start,r.end).join(","))).filter(e=>null!=e)}(e,this.rankMap))}if(null==t)break;let u=this.specialTokens[t[0]];n.push(u),l=t.index+t[0].length}return n}decode(e){let t=[],r=0;for(let i=0;i<e.length;++i){let a=e[i],n=this.textMap.get(a)??this.inverseSpecialTokens[a];null!=n&&(t.push(n),r+=n.length)}let i=new Uint8Array(r),a=0;for(let e of t)i.set(e,a),a+=e.length;return this.textDecoder.decode(i)}};((e,t,r)=>rY(e,"symbol"!=typeof t?t+"":t,r))(rW,"specialTokenRegex",e=>RegExp(e.map(e=>e.replace(/[\\^$*+?.()|[\]{}]/g,"\\$&")).join("|"),"g"));let rV={},rK=new tQ({});class rX extends rq{constructor(e){if(super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain","document_transformers","text_splitters"]}),Object.defineProperty(this,"chunkSize",{enumerable:!0,configurable:!0,writable:!0,value:1e3}),Object.defineProperty(this,"chunkOverlap",{enumerable:!0,configurable:!0,writable:!0,value:200}),Object.defineProperty(this,"keepSeparator",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"lengthFunction",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.chunkSize=e?.chunkSize??this.chunkSize,this.chunkOverlap=e?.chunkOverlap??this.chunkOverlap,this.keepSeparator=e?.keepSeparator??this.keepSeparator,this.lengthFunction=e?.lengthFunction??(e=>e.length),this.chunkOverlap>=this.chunkSize)throw Error("Cannot have chunkOverlap >= chunkSize")}async transformDocuments(e,t={}){return this.splitDocuments(e,t)}splitOnSeparator(e,t){let r;if(t)if(this.keepSeparator){let i=t.replace(/[/\-\\^$*+?.()|[\]{}]/g,"\\$&");r=e.split(RegExp(`(?=${i})`))}else r=e.split(t);else r=e.split("");return r.filter(e=>""!==e)}async createDocuments(e,t=[],r={}){let i=t.length>0?t:[...Array(e.length)].map(()=>({})),{chunkHeader:a="",chunkOverlapHeader:n="(cont'd) ",appendChunkOverlapHeader:s=!1}=r,o=[];for(let t=0;t<e.length;t+=1){let r=e[t],l=1,u=null,c=-1;for(let e of(await this.splitText(r))){let d=a,h=r.indexOf(e,c+1);if(null===u)l+=this.numberOfNewLines(r,0,h);else{let e=c+await this.lengthFunction(u);e<h?l+=this.numberOfNewLines(r,e,h):e>h&&(l-=this.numberOfNewLines(r,h,e)),s&&(d+=n)}let p=this.numberOfNewLines(e),f=i[t].loc&&"object"==typeof i[t].loc?{...i[t].loc}:{};f.lines={from:l,to:l+p};let m={...i[t],loc:f};d+=e,o.push(new g({pageContent:d,metadata:m})),l+=p,u=e,c=h}}return o}numberOfNewLines(e,t,r){return(e.slice(t,r).match(/\n/g)||[]).length}async splitDocuments(e,t={}){let r=e.filter(e=>void 0!==e.pageContent),i=r.map(e=>e.pageContent),a=r.map(e=>e.metadata);return this.createDocuments(i,a,t)}joinDocs(e,t){let r=e.join(t).trim();return""===r?null:r}async mergeSplits(e,t){let r=[],i=[],a=0;for(let n of e){let e=await this.lengthFunction(n);if(a+e+i.length*t.length>this.chunkSize&&(a>this.chunkSize&&console.warn(`Created a chunk of size ${a}, +
which is longer than the specified ${this.chunkSize}`),i.length>0)){let n=this.joinDocs(i,t);for(null!==n&&r.push(n);a>this.chunkOverlap||a+e+i.length*t.length>this.chunkSize&&a>0;)a-=await this.lengthFunction(i[0]),i.shift()}i.push(n),a+=e}let n=this.joinDocs(i,t);return null!==n&&r.push(n),r}}class rQ extends rX{static lc_name(){return"RecursiveCharacterTextSplitter"}constructor(e){super(e),Object.defineProperty(this,"separators",{enumerable:!0,configurable:!0,writable:!0,value:["\n\n","\n"," ",""]}),this.separators=e?.separators??this.separators,this.keepSeparator=e?.keepSeparator??!0}async _splitText(e,t){let r,i=[],a=t[t.length-1];for(let i=0;i<t.length;i+=1){let n=t[i];if(""===n){a=n;break}if(e.includes(n)){a=n,r=t.slice(i+1);break}}let n=this.splitOnSeparator(e,a),s=[],o=this.keepSeparator?"":a;for(let e of n)if(await this.lengthFunction(e)<this.chunkSize)s.push(e);else{if(s.length){let e=await this.mergeSplits(s,o);i.push(...e),s=[]}if(r){let t=await this._splitText(e,r);i.push(...t)}else i.push(e)}if(s.length){let e=await this.mergeSplits(s,o);i.push(...e)}return i}async splitText(e){return this._splitText(e,this.separators)}static fromLanguage(e,t){return new rQ({...t,separators:rQ.getSeparatorsForLanguage(e)})}static getSeparatorsForLanguage(e){if("cpp"===e)return["\nclass ","\nvoid ","\nint ","\nfloat ","\ndouble ","\nif ","\nfor ","\nwhile ","\nswitch ","\ncase ","\n\n","\n"," ",""];if("go"===e)return["\nfunc ","\nvar ","\nconst ","\ntype ","\nif ","\nfor ","\nswitch ","\ncase ","\n\n","\n"," ",""];if("java"===e)return["\nclass ","\npublic ","\nprotected ","\nprivate ","\nstatic ","\nif ","\nfor ","\nwhile ","\nswitch ","\ncase ","\n\n","\n"," ",""];if("js"===e)return["\nfunction ","\nconst ","\nlet ","\nvar ","\nclass ","\nif ","\nfor ","\nwhile ","\nswitch ","\ncase ","\ndefault ","\n\n","\n"," ",""];if("php"===e)return["\nfunction ","\nclass ","\nif ","\nforeach ","\nwhile ","\ndo ","\nswitch ","\ncase ","\n\n","\n"," ",""];else if("proto"===e)return["\nmessage ","\nservice ","\nenum ","\noption ","\nimport ","\nsyntax ","\n\n","\n"," ",""];else if("python"===e)return["\nclass ","\ndef ","\n	def ","\n\n","\n"," ",""];else if("rst"===e)return["\n===\n","\n---\n","\n***\n","\n.. ","\n\n","\n"," ",""];else if("ruby"===e)return["\ndef ","\nclass ","\nif ","\nunless ","\nwhile ","\nfor ","\ndo ","\nbegin ","\nrescue ","\n\n","\n"," ",""];else if("rust"===e)return["\nfn ","\nconst ","\nlet ","\nif ","\nwhile ","\nfor ","\nloop ","\nmatch ","\nconst ","\n\n","\n"," ",""];else if("scala"===e)return["\nclass ","\nobject ","\ndef ","\nval ","\nvar ","\nif ","\nfor ","\nwhile ","\nmatch ","\ncase ","\n\n","\n"," ",""];else if("swift"===e)return["\nfunc ","\nclass ","\nstruct ","\nenum ","\nif ","\nfor ","\nwhile ","\ndo ","\nswitch ","\ncase ","\n\n","\n"," ",""];else if("markdown"===e)return["\n## ","\n### ","\n#### ","\n##### ","\n###### ","```\n\n","\n\n***\n\n","\n\n---\n\n","\n\n___\n\n","\n\n","\n"," ",""];else if("latex"===e)return["\n\\chapter{","\n\\section{","\n\\subsection{","\n\\subsubsection{","\n\\begin{enumerate}","\n\\begin{itemize}","\n\\begin{description}","\n\\begin{list}","\n\\begin{quote}","\n\\begin{quotation}","\n\\begin{verse}","\n\\begin{verbatim}","\n\\begin{align}","$$","$","\n\n","\n"," ",""];else if("html"===e)return["<body>","<div>","<p>","<br>","<li>","<h1>","<h2>","<h3>","<h4>","<h5>","<h6>","<span>","<table>","<tr>","<td>","<th>","<ul>","<ol>","<header>","<footer>","<nav>","<head>","<style>","<script>","<meta>","<title>"," ",""];else if("sol"===e)return["\npragma ","\nusing ","\ncontract ","\ninterface ","\nlibrary ","\nconstructor ","\ntype ","\nfunction ","\nevent ","\nmodifier ","\nerror ","\nstruct ","\nenum ","\nif ","\nfor ","\nwhile ","\ndo while ","\nassembly ","\n\n","\n"," ",""];else throw Error(`Language ${e} is not supported.`)}}var r0=r(88108);let r1=require("fs/promises");var r4=r(33873);let r3=require("os"),r2=require("pdf-parse");var r9=r.n(r2);let r6=new rQ({chunkSize:1500,chunkOverlap:300,separators:["\n\n","\n",". ","! ","? ","; ",", "," ",""]});async function r5(e){try{let t=await (0,m.createSupabaseServerClientOnRequest)(),{data:{user:i},error:a}=await t.auth.getUser();if(a||!i)return f.NextResponse.json({error:"Unauthorized"},{status:401});let n=await e.formData(),s=n.get("file"),o=n.get("configId");if(!s||!o)return f.NextResponse.json({error:"File and configId are required"},{status:400});if(!["application/pdf","text/plain","text/markdown"].includes(s.type))return f.NextResponse.json({error:"Unsupported file type. Please upload PDF, TXT, or MD files."},{status:400});if(s.size>0xa00000)return f.NextResponse.json({error:"File size too large. Maximum size is 10MB."},{status:400});let l=await s.arrayBuffer(),u=Buffer.from(l),c=(0,r4.join)((0,r3.tmpdir)(),`upload_${Date.now()}_${s.name}`);await (0,r1.writeFile)(c,u);let d="";try{switch(s.type){case"application/pdf":let e=console.warn;console.warn=t=>{"string"==typeof t&&t.includes("Ran out of space in font private use area")||e(t)};try{if((!(d=(await r9()(u,{max:0})).text)||0===d.trim().length)&&(!(d=(await r9()(u,{max:0})).text)||0===d.trim().length))throw Error("No text could be extracted from this PDF. The file may be image-based, password-protected, or corrupted.")}catch(e){throw Error(`Failed to process PDF: ${e.message||"Unknown error"}`)}finally{console.warn=e}break;case"application/vnd.openxmlformats-officedocument.wordprocessingml.document":throw Error("DOCX support temporarily disabled. Please use PDF, TXT, or MD files.");case"text/plain":case"text/markdown":d=await (0,r1.readFile)(c,"utf-8");break;default:throw Error("Unsupported file type")}let a={pageContent:d,metadata:{source:s.name,type:s.type}},n=await r6.splitDocuments([a]),{data:l,error:h}=await t.from("documents").insert({user_id:i.id,custom_api_config_id:o,filename:s.name,file_type:s.type,file_size:s.size,content:d,metadata:{chunks_count:n.length,processing_started_at:new Date().toISOString()},status:"processing"}).select().single();if(h)throw Error("Failed to store document metadata");try{let e=await r0.jinaEmbeddings.embedQuery("test");if(1024!==e.length)throw Error(`Dimension mismatch: Database expects 1024 dimensions but Jina v3 produces ${e.length} dimensions`)}catch(e){throw Error(`Jina embedding generation failed: ${e.message}`)}let p=n.map(async(e,r)=>{try{let a=await r0.jinaEmbeddings.embedQuery(e.pageContent),n={document_id:l.id,user_id:i.id,custom_api_config_id:o,content:e.pageContent,metadata:{...e.metadata,chunk_index:r,chunk_size:e.pageContent.length},embedding:a},{data:s,error:u}=await t.from("document_chunks").insert(n).select().single();if(u)throw u;return{success:!0,index:r,chunkId:s.id}}catch(e){return{success:!1,index:r,error:e.message||e}}}),m=await Promise.all(p),g=m.filter(e=>e.success).length,b=m.filter(e=>!e.success).length,y=m.filter(e=>!e.success),v=0===b?"completed":"failed",{error:w}=await t.from("documents").update({status:v,metadata:{...l.metadata,chunks_processed:g,chunks_failed:b,processing_completed_at:new Date().toISOString(),...b>0&&{failed_chunk_errors:y.slice(0,5)}},updated_at:new Date().toISOString()}).eq("id",l.id);await (0,r1.unlink)(c);try{let{trainingDataCache:e}=await r.e(2842).then(r.bind(r,2842));e.invalidate(o)}catch(e){}return f.NextResponse.json({success:!0,document:{id:l.id,filename:s.name,status:v,chunks_processed:g,chunks_total:n.length}})}catch(e){try{await (0,r1.unlink)(c)}catch(e){}throw e}}catch(e){return f.NextResponse.json({error:"Failed to process document",details:e.message},{status:500})}}let r8=new d.AppRouteRouteModule({definition:{kind:h.RouteKind.APP_ROUTE,page:"/api/documents/upload/route",pathname:"/api/documents/upload",filename:"route",bundlePath:"app/api/documents/upload/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\documents\\upload\\route.ts",nextConfigOutput:"",userland:c}),{workAsyncStorage:r7,workUnitAsyncStorage:ie,serverHooks:it}=r8;function ir(){return(0,p.patchFetch)({workAsyncStorage:r7,workUnitAsyncStorage:ie})}},51862:e=>{"use strict";let t=/[\p{Lu}]/u,r=/[\p{Ll}]/u,i=/^[\p{Lu}](?![\p{Lu}])/gu,a=/([\p{Alpha}\p{N}_]|$)/u,n=/[_.\- ]+/,s=RegExp("^"+n.source),o=RegExp(n.source+a.source,"gu"),l=RegExp("\\d+"+a.source,"gu"),u=(e,i,a)=>{let n=!1,s=!1,o=!1;for(let l=0;l<e.length;l++){let u=e[l];n&&t.test(u)?(e=e.slice(0,l)+"-"+e.slice(l),n=!1,o=s,s=!0,l++):s&&o&&r.test(u)?(e=e.slice(0,l-1)+"-"+e.slice(l-1),o=s,s=!1,n=!0):(n=i(u)===u&&a(u)!==u,o=s,s=a(u)===u&&i(u)!==u)}return e},c=(e,t)=>(i.lastIndex=0,e.replace(i,e=>t(e))),d=(e,t)=>(o.lastIndex=0,l.lastIndex=0,e.replace(o,(e,r)=>t(r)).replace(l,e=>t(e))),h=(e,t)=>{if(!("string"==typeof e||Array.isArray(e)))throw TypeError("Expected the input to be `string | string[]`");if(t={pascalCase:!1,preserveConsecutiveUppercase:!1,...t},0===(e=Array.isArray(e)?e.map(e=>e.trim()).filter(e=>e.length).join("-"):e.trim()).length)return"";let r=!1===t.locale?e=>e.toLowerCase():e=>e.toLocaleLowerCase(t.locale),i=!1===t.locale?e=>e.toUpperCase():e=>e.toLocaleUpperCase(t.locale);return 1===e.length?t.pascalCase?i(e):r(e):(e!==r(e)&&(e=u(e,r,i)),e=e.replace(s,""),e=t.preserveConsecutiveUppercase?c(e,r):r(e),t.pascalCase&&(e=i(e.charAt(0))+e.slice(1)),d(e,i))};e.exports=h,e.exports.default=h},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55332:(e,t,r)=>{var i=r(81174);t.operation=function(e){return new i(t.timeouts(e),{forever:e&&(e.forever||e.retries===1/0),unref:e&&e.unref,maxRetryTime:e&&e.maxRetryTime})},t.timeouts=function(e){if(e instanceof Array)return[].concat(e);var t={retries:10,factor:2,minTimeout:1e3,maxTimeout:1/0,randomize:!1};for(var r in e)t[r]=e[r];if(t.minTimeout>t.maxTimeout)throw Error("minTimeout is greater than maxTimeout");for(var i=[],a=0;a<t.retries;a++)i.push(this.createTimeout(a,t));return e&&e.forever&&!i.length&&i.push(this.createTimeout(a,t)),i.sort(function(e,t){return e-t}),i},t.createTimeout=function(e,t){var r=Math.round((t.randomize?Math.random()+1:1)*Math.max(t.minTimeout,1)*Math.pow(t.factor,e));return Math.min(r,t.maxTimeout)},t.wrap=function(e,r,i){if(r instanceof Array&&(i=r,r=null),!i)for(var a in i=[],e)"function"==typeof e[a]&&i.push(a);for(var n=0;n<i.length;n++){var s=i[n],o=e[s];e[s]=(function(i){var a=t.operation(r),n=Array.prototype.slice.call(arguments,1),s=n.pop();n.push(function(e){a.retry(e)||(e&&(arguments[0]=a.mainError()),s.apply(this,arguments))}),a.attempt(function(){i.apply(e,n)})}).bind(e,o),e[s].options=r}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57543:e=>{"use strict";e.exports=function(e,t){if("string"!=typeof e)throw TypeError("Expected a string");return t=void 0===t?"_":t,e.replace(/([a-z\d])([A-Z])/g,"$1"+t+"$2").replace(/([A-Z]+)([A-Z][a-z\d]+)/g,"$1"+t+"$2").toLowerCase()}},58361:(e,t,r)=>{"use strict";let i=r(64487);e.exports=(e,t,r=!1)=>{if(e instanceof i)return e;try{return new i(e,t)}catch(e){if(!r)return null;throw e}}},60301:(e,t,r)=>{"use strict";let i=r(33877);e.exports=(e,t,r)=>0>=i(e,t,r)},62502:(e,t,r)=>{"use strict";let i=r(25706);class a extends Error{constructor(e){super(e),this.name="TimeoutError"}}let n=(e,t,r)=>new Promise((n,s)=>{if("number"!=typeof t||t<0)throw TypeError("Expected `milliseconds` to be a positive number");if(t===1/0)return void n(e);let o=setTimeout(()=>{if("function"==typeof r){try{n(r())}catch(e){s(e)}return}let i="string"==typeof r?r:`Promise timed out after ${t} milliseconds`,o=r instanceof Error?r:new a(i);"function"==typeof e.cancel&&e.cancel(),s(o)},t);i(e.then(n,s),()=>{clearTimeout(o)})});e.exports=n,e.exports.default=n,e.exports.TimeoutError=a},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63391:(e,t,r)=>{"use strict";e=r.nmd(e);let i=(e=0)=>t=>`\u001B[${38+e};5;${t}m`,a=(e=0)=>(t,r,i)=>`\u001B[${38+e};2;${t};${r};${i}m`;Object.defineProperty(e,"exports",{enumerable:!0,get:function(){let e=new Map,t={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};for(let[r,i]of(t.color.gray=t.color.blackBright,t.bgColor.bgGray=t.bgColor.bgBlackBright,t.color.grey=t.color.blackBright,t.bgColor.bgGrey=t.bgColor.bgBlackBright,Object.entries(t))){for(let[r,a]of Object.entries(i))t[r]={open:`\u001B[${a[0]}m`,close:`\u001B[${a[1]}m`},i[r]=t[r],e.set(a[0],a[1]);Object.defineProperty(t,r,{value:i,enumerable:!1})}return Object.defineProperty(t,"codes",{value:e,enumerable:!1}),t.color.close="\x1b[39m",t.bgColor.close="\x1b[49m",t.color.ansi256=i(),t.color.ansi16m=a(),t.bgColor.ansi256=i(10),t.bgColor.ansi16m=a(10),Object.defineProperties(t,{rgbToAnsi256:{value:(e,t,r)=>e===t&&t===r?e<8?16:e>248?231:Math.round((e-8)/247*24)+232:16+36*Math.round(e/255*5)+6*Math.round(t/255*5)+Math.round(r/255*5),enumerable:!1},hexToRgb:{value:e=>{let t=/(?<colorString>[a-f\d]{6}|[a-f\d]{3})/i.exec(e.toString(16));if(!t)return[0,0,0];let{colorString:r}=t.groups;3===r.length&&(r=r.split("").map(e=>e+e).join(""));let i=Number.parseInt(r,16);return[i>>16&255,i>>8&255,255&i]},enumerable:!1},hexToAnsi256:{value:e=>t.rgbToAnsi256(...t.hexToRgb(e)),enumerable:!1}}),t}})},63611:(e,t,r)=>{"use strict";let i=r(23518),a=["Failed to fetch","NetworkError when attempting to fetch resource.","The Internet connection appears to be offline.","Network request failed"];class n extends Error{constructor(e){super(),e instanceof Error?(this.originalError=e,{message:e}=e):(this.originalError=Error(e),this.originalError.stack=this.stack),this.name="AbortError",this.message=e}}let s=(e,t,r)=>{let i=r.retries-(t-1);return e.attemptNumber=t,e.retriesLeft=i,e},o=e=>a.includes(e),l=(e,t)=>new Promise((r,a)=>{t={onFailedAttempt:()=>{},retries:10,...t};let l=i.operation(t);l.attempt(async i=>{try{r(await e(i))}catch(e){if(!(e instanceof Error))return void a(TypeError(`Non-error was thrown: "${e}". You should only throw errors.`));if(e instanceof n)l.stop(),a(e.originalError);else if(e instanceof TypeError&&!o(e.message))l.stop(),a(e);else{s(e,i,t);try{await t.onFailedAttempt(e)}catch(e){a(e);return}l.retry(e)||a(l.mainError())}}})});e.exports=l,e.exports.default=l,e.exports.AbortError=n},64487:(e,t,r)=>{"use strict";let i=r(38267),{MAX_LENGTH:a,MAX_SAFE_INTEGER:n}=r(32397),{safeRe:s,t:o}=r(26515),l=r(98300),{compareIdentifiers:u}=r(78668);class c{constructor(e,t){if(t=l(t),e instanceof c)if(!!t.loose===e.loose&&!!t.includePrerelease===e.includePrerelease)return e;else e=e.version;else if("string"!=typeof e)throw TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>a)throw TypeError(`version is longer than ${a} characters`);i("SemVer",e,t),this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease;let r=e.trim().match(t.loose?s[o.LOOSE]:s[o.FULL]);if(!r)throw TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+r[1],this.minor=+r[2],this.patch=+r[3],this.major>n||this.major<0)throw TypeError("Invalid major version");if(this.minor>n||this.minor<0)throw TypeError("Invalid minor version");if(this.patch>n||this.patch<0)throw TypeError("Invalid patch version");r[4]?this.prerelease=r[4].split(".").map(e=>{if(/^[0-9]+$/.test(e)){let t=+e;if(t>=0&&t<n)return t}return e}):this.prerelease=[],this.build=r[5]?r[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(i("SemVer.compare",this.version,this.options,e),!(e instanceof c)){if("string"==typeof e&&e===this.version)return 0;e=new c(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof c||(e=new c(e,this.options)),u(this.major,e.major)||u(this.minor,e.minor)||u(this.patch,e.patch)}comparePre(e){if(e instanceof c||(e=new c(e,this.options)),this.prerelease.length&&!e.prerelease.length)return -1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let t=0;do{let r=this.prerelease[t],a=e.prerelease[t];if(i("prerelease compare",t,r,a),void 0===r&&void 0===a)return 0;if(void 0===a)return 1;if(void 0===r)return -1;else if(r===a)continue;else return u(r,a)}while(++t)}compareBuild(e){e instanceof c||(e=new c(e,this.options));let t=0;do{let r=this.build[t],a=e.build[t];if(i("build compare",t,r,a),void 0===r&&void 0===a)return 0;if(void 0===a)return 1;if(void 0===r)return -1;else if(r===a)continue;else return u(r,a)}while(++t)}inc(e,t,r){if(e.startsWith("pre")){if(!t&&!1===r)throw Error("invalid increment argument: identifier is empty");if(t){let e=`-${t}`.match(this.options.loose?s[o.PRERELEASELOOSE]:s[o.PRERELEASE]);if(!e||e[1]!==t)throw Error(`invalid identifier: ${t}`)}}switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t,r);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t,r);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t,r),this.inc("pre",t,r);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",t,r),this.inc("pre",t,r);break;case"release":if(0===this.prerelease.length)throw Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":(0!==this.minor||0!==this.patch||0===this.prerelease.length)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(0!==this.patch||0===this.prerelease.length)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":{let e=+!!Number(r);if(0===this.prerelease.length)this.prerelease=[e];else{let i=this.prerelease.length;for(;--i>=0;)"number"==typeof this.prerelease[i]&&(this.prerelease[i]++,i=-2);if(-1===i){if(t===this.prerelease.join(".")&&!1===r)throw Error("invalid increment argument: identifier already exists");this.prerelease.push(e)}}if(t){let i=[t,e];!1===r&&(i=[t]),0===u(this.prerelease[0],t)?isNaN(this.prerelease[1])&&(this.prerelease=i):this.prerelease=i}break}default:throw Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}}e.exports=c},71505:(e,t,r)=>{"use strict";let i=r(3706);e.exports=(e,t,r)=>(e=new i(e,r),t=new i(t,r),e.intersects(t,r))},71611:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){let i=0,a=e.length;for(;a>0;){let n=a/2|0,s=i+n;0>=r(e[s],t)?(i=++s,a-=n+1):a=n}return i}},71719:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let i=r(339),a=r(62502),n=r(12441),s=()=>{},o=new a.TimeoutError;class l extends i{constructor(e){var t,r,i,a;if(super(),this._intervalCount=0,this._intervalEnd=0,this._pendingCount=0,this._resolveEmpty=s,this._resolveIdle=s,!("number"==typeof(e=Object.assign({carryoverConcurrencyCount:!1,intervalCap:1/0,interval:0,concurrency:1/0,autoStart:!0,queueClass:n.default},e)).intervalCap&&e.intervalCap>=1))throw TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${null!=(r=null==(t=e.intervalCap)?void 0:t.toString())?r:""}\` (${typeof e.intervalCap})`);if(void 0===e.interval||!(Number.isFinite(e.interval)&&e.interval>=0))throw TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${null!=(a=null==(i=e.interval)?void 0:i.toString())?a:""}\` (${typeof e.interval})`);this._carryoverConcurrencyCount=e.carryoverConcurrencyCount,this._isIntervalIgnored=e.intervalCap===1/0||0===e.interval,this._intervalCap=e.intervalCap,this._interval=e.interval,this._queue=new e.queueClass,this._queueClass=e.queueClass,this.concurrency=e.concurrency,this._timeout=e.timeout,this._throwOnTimeout=!0===e.throwOnTimeout,this._isPaused=!1===e.autoStart}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--,this._tryToStartAnother(),this.emit("next")}_resolvePromises(){this._resolveEmpty(),this._resolveEmpty=s,0===this._pendingCount&&(this._resolveIdle(),this._resolveIdle=s,this.emit("idle"))}_onResumeInterval(){this._onInterval(),this._initializeIntervalIfNeeded(),this._timeoutId=void 0}_isIntervalPaused(){let e=Date.now();if(void 0===this._intervalId){let t=this._intervalEnd-e;if(!(t<0))return void 0===this._timeoutId&&(this._timeoutId=setTimeout(()=>{this._onResumeInterval()},t)),!0;this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}return!1}_tryToStartAnother(){if(0===this._queue.size)return this._intervalId&&clearInterval(this._intervalId),this._intervalId=void 0,this._resolvePromises(),!1;if(!this._isPaused){let e=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){let t=this._queue.dequeue();return!!t&&(this.emit("active"),t(),e&&this._initializeIntervalIfNeeded(),!0)}}return!1}_initializeIntervalIfNeeded(){this._isIntervalIgnored||void 0!==this._intervalId||(this._intervalId=setInterval(()=>{this._onInterval()},this._interval),this._intervalEnd=Date.now()+this._interval)}_onInterval(){0===this._intervalCount&&0===this._pendingCount&&this._intervalId&&(clearInterval(this._intervalId),this._intervalId=void 0),this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0,this._processQueue()}_processQueue(){for(;this._tryToStartAnother(););}get concurrency(){return this._concurrency}set concurrency(e){if(!("number"==typeof e&&e>=1))throw TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${e}\` (${typeof e})`);this._concurrency=e,this._processQueue()}async add(e,t={}){return new Promise((r,i)=>{let n=async()=>{this._pendingCount++,this._intervalCount++;try{let n=void 0===this._timeout&&void 0===t.timeout?e():a.default(Promise.resolve(e()),void 0===t.timeout?this._timeout:t.timeout,()=>{(void 0===t.throwOnTimeout?this._throwOnTimeout:t.throwOnTimeout)&&i(o)});r(await n)}catch(e){i(e)}this._next()};this._queue.enqueue(n,t),this._tryToStartAnother(),this.emit("add")})}async addAll(e,t){return Promise.all(e.map(async e=>this.add(e,t)))}start(){return this._isPaused&&(this._isPaused=!1,this._processQueue()),this}pause(){this._isPaused=!0}clear(){this._queue=new this._queueClass}async onEmpty(){if(0!==this._queue.size)return new Promise(e=>{let t=this._resolveEmpty;this._resolveEmpty=()=>{t(),e()}})}async onIdle(){if(0!==this._pendingCount||0!==this._queue.size)return new Promise(e=>{let t=this._resolveIdle;this._resolveIdle=()=>{t(),e()}})}get size(){return this._queue.size}sizeBy(e){return this._queue.filter(e).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(e){this._timeout=e}}t.default=l},73051:(e,t,r)=>{"use strict";let i=r(58361);e.exports=(e,t)=>{let r=i(e.trim().replace(/^[=v]+/,""),t);return r?r.version:null}},73438:(e,t,r)=>{"use strict";let i=r(33877);e.exports=(e,t,r)=>0===i(e,t,r)},74075:e=>{"use strict";e.exports=require("zlib")},77860:(e,t,r)=>{"use strict";let i=r(42679),a=r(33877);e.exports=(e,t,r)=>{let n=[],s=null,o=null,l=e.sort((e,t)=>a(e,t,r));for(let e of l)i(e,t,r)?(o=e,s||(s=e)):(o&&n.push([s,o]),o=null,s=null);s&&n.push([s,null]);let u=[];for(let[e,t]of n)e===t?u.push(e):t||e!==l[0]?t?e===l[0]?u.push(`<=${t}`):u.push(`${e} - ${t}`):u.push(`>=${e}`):u.push("*");let c=u.join(" || "),d="string"==typeof t.raw?t.raw:String(t);return c.length<d.length?c:t}},78172:(e,t,r)=>{"use strict";let i=r(64487);e.exports=(e,t)=>new i(e,t).patch},78335:()=>{},78668:e=>{"use strict";let t=/^[0-9]+$/,r=(e,r)=>{let i=t.test(e),a=t.test(r);return i&&a&&(e*=1,r*=1),e===r?0:i&&!a?-1:a&&!i?1:e<r?-1:1};e.exports={compareIdentifiers:r,rcompareIdentifiers:(e,t)=>r(t,e)}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81174:e=>{function t(e,t){"boolean"==typeof t&&(t={forever:t}),this._originalTimeouts=JSON.parse(JSON.stringify(e)),this._timeouts=e,this._options=t||{},this._maxRetryTime=t&&t.maxRetryTime||1/0,this._fn=null,this._errors=[],this._attempts=1,this._operationTimeout=null,this._operationTimeoutCb=null,this._timeout=null,this._operationStart=null,this._timer=null,this._options.forever&&(this._cachedTimeouts=this._timeouts.slice(0))}e.exports=t,t.prototype.reset=function(){this._attempts=1,this._timeouts=this._originalTimeouts.slice(0)},t.prototype.stop=function(){this._timeout&&clearTimeout(this._timeout),this._timer&&clearTimeout(this._timer),this._timeouts=[],this._cachedTimeouts=null},t.prototype.retry=function(e){if(this._timeout&&clearTimeout(this._timeout),!e)return!1;var t=new Date().getTime();if(e&&t-this._operationStart>=this._maxRetryTime)return this._errors.push(e),this._errors.unshift(Error("RetryOperation timeout occurred")),!1;this._errors.push(e);var r=this._timeouts.shift();if(void 0===r)if(!this._cachedTimeouts)return!1;else this._errors.splice(0,this._errors.length-1),r=this._cachedTimeouts.slice(-1);var i=this;return this._timer=setTimeout(function(){i._attempts++,i._operationTimeoutCb&&(i._timeout=setTimeout(function(){i._operationTimeoutCb(i._attempts)},i._operationTimeout),i._options.unref&&i._timeout.unref()),i._fn(i._attempts)},r),this._options.unref&&this._timer.unref(),!0},t.prototype.attempt=function(e,t){this._fn=e,t&&(t.timeout&&(this._operationTimeout=t.timeout),t.cb&&(this._operationTimeoutCb=t.cb));var r=this;this._operationTimeoutCb&&(this._timeout=setTimeout(function(){r._operationTimeoutCb()},r._operationTimeout)),this._operationStart=new Date().getTime(),this._fn(this._attempts)},t.prototype.try=function(e){console.log("Using RetryOperation.try() is deprecated"),this.attempt(e)},t.prototype.start=function(e){console.log("Using RetryOperation.start() is deprecated"),this.attempt(e)},t.prototype.start=t.prototype.try,t.prototype.errors=function(){return this._errors},t.prototype.attempts=function(){return this._attempts},t.prototype.mainError=function(){if(0===this._errors.length)return null;for(var e={},t=null,r=0,i=0;i<this._errors.length;i++){var a=this._errors[i],n=a.message,s=(e[n]||0)+1;e[n]=s,s>=r&&(t=a,r=s)}return t}},81630:e=>{"use strict";e.exports=require("http")},84450:(e,t,r)=>{"use strict";let i=r(73438),a=r(27290),n=r(42699),s=r(44156),o=r(40720),l=r(60301);e.exports=(e,t,r,u)=>{switch(t){case"===":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e===r;case"!==":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e!==r;case"":case"=":case"==":return i(e,r,u);case"!=":return a(e,r,u);case">":return n(e,r,u);case">=":return s(e,r,u);case"<":return o(e,r,u);case"<=":return l(e,r,u);default:throw TypeError(`Invalid operator: ${t}`)}}},86605:(e,t,r)=>{"use strict";let i=r(33877);e.exports=(e,t,r)=>i(t,e,r)},88108:(e,t,r)=>{"use strict";r.d(t,{jinaEmbeddings:()=>a});class i{constructor(){if(this.currentKeyIndex=0,this.keyUsage=new Map,this.baseUrl="https://api.jina.ai/v1/embeddings",this.model="jina-embeddings-v3",this.apiKeys=[process.env.JINA_API_KEY,process.env.JINA_API_KEY_2,process.env.JINA_API_KEY_3,process.env.JINA_API_KEY_4,process.env.JINA_API_KEY_5,process.env.JINA_API_KEY_6,process.env.JINA_API_KEY_7,process.env.JINA_API_KEY_9,process.env.JINA_API_KEY_10].filter(Boolean),0===this.apiKeys.length)throw Error("No Jina API keys found in environment variables");this.apiKeys.forEach(e=>{this.keyUsage.set(e,{requests:0,tokens:0,lastUsed:new Date,errors:0})})}getNextKey(){let e=this.apiKeys[this.currentKeyIndex];return this.currentKeyIndex=(this.currentKeyIndex+1)%this.apiKeys.length,e}getBestKey(){return this.getNextKey()}updateKeyUsage(e,t,r=!1){let i=this.keyUsage.get(e);i&&(i.requests++,i.tokens+=t,i.lastUsed=new Date,r&&(i.errors++,i.lastError=new Date))}async embedQuery(e){let t=this.apiKeys.length,r=null;for(let i=0;i<t;i++)try{let t=this.getBestKey(),r=await fetch(this.baseUrl,{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({model:this.model,input:[e],normalized:!0,embedding_type:"float"})});if(!r.ok){let e=await r.text();if(429===r.status){this.updateKeyUsage(t,0,!0);continue}throw Error(`HTTP ${r.status}: ${e}`)}let i=await r.json();if(!i.data||0===i.data.length)throw Error("No embedding data returned from Jina API");let a=i.data[0].embedding;return this.updateKeyUsage(t,i.usage?.total_tokens||e.length),a}catch(e){if(r=e,i===t-1)break}throw Error(`All Jina API keys failed. Last error: ${r?.message||"Unknown error"}`)}async embedDocuments(e){let t=[];for(let r=0;r<e.length;r++){let i=await this.embedQuery(e[r]);t.push(i),r<e.length-1&&await new Promise(e=>setTimeout(e,100))}return t}getUsageStats(){let e={};return this.apiKeys.forEach((t,r)=>{let i=this.keyUsage.get(t);i&&(e[`key_${r+1}`]={...i})}),e}getTotalCapacity(){return{totalKeys:this.apiKeys.length,estimatedRPM:500*this.apiKeys.length,estimatedTokensPerMonth:1e6*this.apiKeys.length}}}let a=new i},90726:(e,t,r)=>{"use strict";let i=r(64487);e.exports=(e,t,r,a,n)=>{"string"==typeof r&&(n=a,a=r,r=void 0);try{return new i(e instanceof i?e.version:e,r).inc(t,a,n).version}catch(e){return null}}},91645:e=>{"use strict";e.exports=require("net")},93419:(e,t,r)=>{"use strict";let i=r(58361);e.exports=(e,t)=>{let r=i(e,null,!0),a=i(t,null,!0),n=r.compare(a);if(0===n)return null;let s=n>0,o=s?r:a,l=s?a:r,u=!!o.prerelease.length;if(l.prerelease.length&&!u){if(!l.patch&&!l.minor)return"major";if(0===l.compareMain(o))return l.minor&&!l.patch?"minor":"patch"}let c=u?"pre":"";return r.major!==a.major?c+"major":r.minor!==a.minor?c+"minor":r.patch!==a.patch?c+"patch":"prerelease"}},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},98300:e=>{"use strict";let t=Object.freeze({loose:!0}),r=Object.freeze({});e.exports=e=>e?"object"!=typeof e?t:e:r}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[7719,580,9398,3410,5697],()=>r(51191));module.exports=i})();