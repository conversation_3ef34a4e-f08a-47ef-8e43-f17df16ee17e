/**
 * Multi-Key Jina Classification Implementation
 * Replaces Gemini classification with semantic understanding
 * Uses the same 9 Jina API keys in rotation for classification
 */

interface JinaClassificationResponse {
  usage: {
    total_tokens: number;
  };
  data: Array<{
    object: string;
    index: number;
    prediction: string;
    score: number;
    predictions: Array<{
      label: string;
      score: number;
    }>;
  }>;
}

interface ClassificationResult {
  roleId: string;
  confidence: number;
  isMultiRole?: boolean;
  roles?: Array<{
    roleId: string;
    confidence: number;
    executionOrder: number;
  }>;
  reasoning?: string;
}

interface KeyUsageStats {
  requests: number;
  tokens: number;
  lastUsed: Date;
  errors: number;
  lastError?: Date;
}

export class MultiKeyJinaClassification {
  private apiKeys: string[];
  private currentKeyIndex = 0;
  private keyUsage = new Map<string, KeyUsageStats>();
  private baseUrl = 'https://api.jina.ai/v1/classify';
  private model = 'jina-embeddings-v3';
  private multiRoleThreshold = 0.3; // Confidence threshold for multi-role detection
  private singleRoleThreshold = 0.2; // Minimum confidence for single role

  constructor() {
    // Load all 9 Jina API keys from environment
    this.apiKeys = [
      process.env.JINA_API_KEY,
      process.env.JINA_API_KEY_2,
      process.env.JINA_API_KEY_3,
      process.env.JINA_API_KEY_4,
      process.env.JINA_API_KEY_5,
      process.env.JINA_API_KEY_6,
      process.env.JINA_API_KEY_7,
      process.env.JINA_API_KEY_9,
      process.env.JINA_API_KEY_10,
    ].filter(Boolean) as string[];

    if (this.apiKeys.length === 0) {
      throw new Error('No Jina API keys found in environment variables');
    }

    // Initialize usage stats for each key
    this.apiKeys.forEach(key => {
      this.keyUsage.set(key, {
        requests: 0,
        tokens: 0,
        lastUsed: new Date(),
        errors: 0
      });
    });

    console.log(`[Jina Classification] Initialized with ${this.apiKeys.length} API keys`);
  }

  /**
   * Get the next API key using round-robin rotation
   */
  private getNextKey(): string {
    const key = this.apiKeys[this.currentKeyIndex];
    this.currentKeyIndex = (this.currentKeyIndex + 1) % this.apiKeys.length;
    return key;
  }

  /**
   * Update usage statistics for a key
   */
  private updateKeyUsage(apiKey: string, tokens: number, isError = false) {
    const stats = this.keyUsage.get(apiKey);
    if (stats) {
      stats.requests++;
      stats.tokens += tokens;
      stats.lastUsed = new Date();
      
      if (isError) {
        stats.errors++;
        stats.lastError = new Date();
      }
    }
  }

  /**
   * Create role descriptions for classification
   */
  private createRoleDescriptions(roles: any[]): { [key: string]: string } {
    const descriptions: { [key: string]: string } = {};
    
    roles.forEach(role => {
      // Create semantic descriptions based on role names and any available descriptions
      const roleName = role.role_name || role.name || role.id;
      const roleDescription = role.description || '';
      
      // Generate semantic description based on role name patterns
      let semanticDescription = '';
      
      if (roleName.includes('brainstorm') || roleName.includes('ideation') || roleName.includes('creative')) {
        semanticDescription = 'Generate creative ideas, brainstorm concepts, innovative thinking, creative problem solving, come up with ideas, conceptualize, imagine';
      } else if (roleName.includes('coding') && roleName.includes('backend')) {
        semanticDescription = 'Write code, programming, backend development, APIs, databases, server logic, create applications, build software, develop systems';
      } else if (roleName.includes('coding') && roleName.includes('frontend')) {
        semanticDescription = 'Frontend development, user interface, React, JavaScript, CSS, UI/UX, web development, create interfaces, build websites';
      } else if (roleName.includes('writing')) {
        semanticDescription = 'Write content, articles, blog posts, copywriting, creative writing, compose text, author content, create documents';
      } else if (roleName.includes('research') || roleName.includes('synthesis')) {
        semanticDescription = 'Research information, analyze data, synthesize findings, investigate topics, gather information, study subjects';
      } else if (roleName.includes('logic') || roleName.includes('reasoning')) {
        semanticDescription = 'Logical reasoning, problem solving, analytical thinking, mathematical calculations, solve problems, analyze situations';
      } else if (roleName.includes('general') || roleName.includes('chat')) {
        semanticDescription = 'General conversation, casual chat, questions and answers, help with various topics, general assistance';
      } else {
        // For custom roles, use the role name and description
        semanticDescription = `${roleName} ${roleDescription}`.trim();
      }
      
      descriptions[role.id] = semanticDescription;
    });
    
    return descriptions;
  }

  /**
   * Classify a prompt using Jina's zero-shot classification
   */
  async classifyPrompt(
    prompt: string,
    roles: any[],
    messages: any[] = []
  ): Promise<ClassificationResult | null> {
    if (!prompt || roles.length === 0) {
      return null;
    }

    const apiKey = this.getNextKey();
    const roleDescriptions = this.createRoleDescriptions(roles);
    const labels = Object.values(roleDescriptions);
    
    console.log(`[Jina Classification] Classifying prompt: "${prompt.substring(0, 100)}..."`);
    console.log(`[Jina Classification] Available roles: ${roles.map(r => r.id).join(', ')}`);

    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          model: this.model,
          input: [prompt],
          labels: labels
        })
      });

      if (!response.ok) {
        throw new Error(`Jina Classification API error: ${response.status} ${response.statusText}`);
      }

      const result: JinaClassificationResponse = await response.json();
      this.updateKeyUsage(apiKey, result.usage?.total_tokens || 0);

      if (!result.data || result.data.length === 0) {
        console.warn('[Jina Classification] No classification results returned');
        return null;
      }

      const classificationData = result.data[0];
      const allPredictions = classificationData.predictions || [];
      
      // Map predictions back to role IDs
      const roleScores = allPredictions.map(pred => {
        // Find the role that matches this label
        const roleEntry = Object.entries(roleDescriptions).find(([_, desc]) => desc === pred.label);
        const roleId = roleEntry ? roleEntry[0] : null;
        const role = roles.find(r => r.id === roleId);
        
        return {
          roleId: roleId || pred.label,
          confidence: pred.score,
          role: role
        };
      }).filter(item => item.roleId && item.role);

      // Sort by confidence
      roleScores.sort((a, b) => b.confidence - a.confidence);

      console.log(`[Jina Classification] Results:`, roleScores.map(r => `${r.roleId}: ${r.confidence.toFixed(3)}`).join(', '));

      // Determine if this is multi-role or single-role
      const highConfidenceRoles = roleScores.filter(r => r.confidence >= this.multiRoleThreshold);
      
      if (highConfidenceRoles.length > 1) {
        // Multi-role scenario
        console.log(`[Jina Classification] Multi-role detected: ${highConfidenceRoles.length} roles above threshold`);
        
        return {
          roleId: highConfidenceRoles[0].roleId,
          confidence: highConfidenceRoles[0].confidence,
          isMultiRole: true,
          roles: highConfidenceRoles.map((role, index) => ({
            roleId: role.roleId,
            confidence: role.confidence,
            executionOrder: index + 1
          })),
          reasoning: `Multi-role task detected requiring: ${highConfidenceRoles.map(r => r.roleId).join(', ')}`
        };
      } else if (roleScores.length > 0 && roleScores[0].confidence >= this.singleRoleThreshold) {
        // Single-role scenario
        console.log(`[Jina Classification] Single-role detected: ${roleScores[0].roleId} (${roleScores[0].confidence.toFixed(3)})`);
        
        return {
          roleId: roleScores[0].roleId,
          confidence: roleScores[0].confidence
        };
      } else {
        console.warn('[Jina Classification] No roles met minimum confidence threshold');
        return null;
      }

    } catch (error) {
      console.error('[Jina Classification] Error:', error);
      this.updateKeyUsage(apiKey, 0, true);
      throw error;
    }
  }

  /**
   * Get usage statistics for all keys
   */
  getUsageStats(): Map<string, KeyUsageStats> {
    return new Map(this.keyUsage);
  }

  /**
   * Get the current key being used
   */
  getCurrentKeyIndex(): number {
    return this.currentKeyIndex;
  }
}

// Export singleton instance
export const jinaClassification = new MultiKeyJinaClassification();
