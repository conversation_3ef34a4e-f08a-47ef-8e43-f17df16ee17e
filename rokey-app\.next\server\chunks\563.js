exports.id=563,exports.ids=[563,1489],exports.modules={2507:(e,t,i)=>{"use strict";i.d(t,{Q:()=>o,createSupabaseServerClientOnRequest:()=>s});var n=i(34386),a=i(44999);async function s(){let e=await (0,a.UL)();return(0,n.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,i,n){try{e.set({name:t,value:i,...n})}catch(e){}},remove(t,i){try{e.set({name:t,value:"",...i})}catch(e){}}}})}function o(e){return(0,n.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,i){},remove(e,t){}}})}},23097:(e,t,i)=>{"use strict";i.d(t,{z:()=>s});var n=i(2507);class a{constructor(){this.initializeRoleCapabilities()}async initializeSupabase(){this.supabase||(this.supabase=await (0,n.createSupabaseServerClientOnRequest)())}initializeRoleCapabilities(){this.roleCapabilities={brainstorming_ideation:{capabilities:["creative_thinking","idea_generation","concept_development","innovation"],expertiseLevel:9,consultationTriggers:["creative","innovative","brainstorm","ideas","concept"]},coding_backend:{capabilities:["server_logic","apis","databases","algorithms","architecture"],expertiseLevel:9,consultationTriggers:["backend","server","api","database","algorithm"]},coding_frontend:{capabilities:["ui_ux","javascript","react","css","user_interface"],expertiseLevel:9,consultationTriggers:["frontend","ui","react","javascript","interface"]},research_synthesis:{capabilities:["information_gathering","analysis","synthesis","fact_checking"],expertiseLevel:8,consultationTriggers:["research","analyze","information","data","facts"]},writing:{capabilities:["content_creation","copywriting","storytelling","communication"],expertiseLevel:8,consultationTriggers:["write","content","copy","story","article"]},logic_reasoning:{capabilities:["problem_solving","logical_analysis","decision_making","optimization"],expertiseLevel:9,consultationTriggers:["logic","reasoning","problem","solve","analyze"]},general_chat:{capabilities:["conversation","general_assistance","fallback_support"],expertiseLevel:6,consultationTriggers:["general","help","assist","chat"]}}}async orchestrate(e,t,i,n={}){await this.initializeSupabase();let a=await this.analyzePrompt(i,n),s=await this.getAvailableAgents(t),o=await this.createExecutionPlan(e,t,i,a,s,n);return await this.saveExecutionToDatabase(o),await this.executeHybridPlan(o),await this.updateExecutionInDatabase(o),o}async analyzePrompt(e,t){let i=[],n="non_conversational",a=5,s=[],o=e.toLowerCase();for(let e of((o.includes("code")||o.includes("program")||o.includes("develop"))&&((o.includes("frontend")||o.includes("ui")||o.includes("react"))&&i.push("coding_frontend"),(o.includes("backend")||o.includes("api")||o.includes("server"))&&i.push("coding_backend"),i.includes("coding_frontend")||i.includes("coding_backend")||i.push("coding_backend")),(o.includes("write")||o.includes("content")||o.includes("article"))&&i.push("writing"),(o.includes("research")||o.includes("analyze")||o.includes("information"))&&i.push("research_synthesis"),(o.includes("idea")||o.includes("brainstorm")||o.includes("creative"))&&i.push("brainstorming_ideation"),o.includes("discuss")||o.includes("conversation")||o.includes("chat")?n="conversational":i.length>1&&(n="hybrid",a=Math.min(10,5+i.length)),0===i.length&&i.push("general_chat"),i))this.roleCapabilities[e]&&s.push(...this.roleCapabilities[e].capabilities);return{type:n,requiredRoles:i,complexity:a,consultationNeeds:[...new Set(s)]}}async getAvailableAgents(e){let{data:t}=await this.supabase.from("api_keys").select("*").eq("custom_api_config_id",e).eq("status","active"),{data:i}=await this.supabase.from("role_assignments").select("*").eq("custom_api_config_id",e);if(!t||!i)return[];let n=[];for(let e of i){let i=t.find(t=>t.id===e.api_key_id);if(!i)continue;let a=this.roleCapabilities[e.role_name];if(!a)continue;let s={id:`agent_${e.id}`,name:`${e.role_name.replace("_"," ").toUpperCase()} Specialist`,role:e.role_name,apiKeyId:i.id,model:i.predefined_model_id||"unknown",capabilities:a.capabilities,isExpert:a.expertiseLevel>=8,consultationScore:0};n.push(s)}return n}async createExecutionPlan(e,t,i,n,a,s){let o=a.filter(e=>n.requiredRoles.includes(e.role));if(0===o.length){let e=a.find(e=>"general_chat"===e.role);e&&o.push(e)}let r=this.createTasks(n,i,s);return{id:`hybrid_exec_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,userId:e,configId:t,originalPrompt:i,tasks:r,agents:o,status:"pending",results:[],consultationHistory:[],createdAt:new Date}}createTasks(e,t,i){let n=[];return"conversational"===e.type?n.push({id:`task_conversation_${Date.now()}`,description:`Engage in conversational orchestration for: ${t}`,type:"conversational",requiredRoles:e.requiredRoles,priority:1,dependencies:[],context:{...i,conversational:!0}}):"non_conversational"===e.type&&e.requiredRoles.forEach((a,s)=>{n.push({id:`task_${a}_${Date.now()}_${s}`,description:`${a.replace("_"," ").toUpperCase()} task for: ${t}`,type:"non_conversational",requiredRoles:[a],priority:s+1,dependencies:s>0?[`task_${e.requiredRoles[s-1]}_${Date.now()}_${s-1}`]:[],context:{...i,sequential:!0,step:s+1}})}),n}async executeHybridPlan(e){e.status="in_progress";try{for(let t of e.tasks.sort((e,t)=>e.priority-t.priority)){let i;t.dependencies.every(t=>e.results.some(e=>e.taskId===t&&"completed"===e.status))&&(i="conversational"===t.type?await this.executeConversationalTask(t,e):await this.executeNonConversationalTask(t,e),e.results.push({taskId:t.id,result:i,status:"completed",timestamp:new Date}),await this.checkForDynamicConsultation(t,i,e))}e.status="completed",e.completedAt=new Date}catch(t){throw e.status="failed",t}}async executeConversationalTask(e,t){let i=t.agents.filter(t=>e.requiredRoles.includes(t.role));if(0===i.length)throw Error(`No agents available for conversational task ${e.id}`);let n={task:e.description,originalPrompt:t.originalPrompt,previousResults:t.results,phase:e.context.phase||"discussion"},a=await this.simulateAgentConversation(i,n);return{type:"conversational",participants:i.map(e=>e.name),conversation:a,outcome:a.finalDecision||a.summary}}async executeNonConversationalTask(e,t){let i=t.agents.find(t=>e.requiredRoles.includes(t.role));if(!i)throw Error(`No agent available for non-conversational task ${e.id}`);let n={task:e.description,originalPrompt:t.originalPrompt,previousResults:t.results,role:i.role,step:e.context.step||1},a=await this.executeAgentTask(i,n);return{type:"non_conversational",agent:i.name,role:i.role,result:a}}async simulateAgentConversation(e,t){let i=[];for(let n=0;n<3;n++)for(let a=0;a<e.length;a++){let s=e[a],o=await this.generateAgentMessage(s,t,i);if(i.push({agent:s.name,role:s.role,message:o,round:n+1,timestamp:new Date}),o.includes("CONVERSATION_COMPLETE")||o.includes("FINAL_DECISION"))break}return{messages:i,summary:this.summarizeConversation(i),finalDecision:i[i.length-1]?.message||"No final decision reached"}}async executeAgentTask(e,t){let{data:i}=await this.supabase.from("api_keys").select("*").eq("id",e.apiKeyId).single();if(!i)throw Error(`API key not found for agent ${e.name}`);return this.createRoleSpecificPrompt(e.role,t),`[${e.name}] Completed task: ${t.task}

Role-specific analysis and output based on ${e.role} expertise.`}async checkForDynamicConsultation(e,t,i){let n=await this.analyzeConsultationNeeds(t,e);if(0!==n.length)for(let a of(await this.findAvailableExperts(i.configId,n,i.agents))){let s=await this.consultWithExpert(a,e,t,i);i.consultationHistory.push({taskId:e.id,consultedAgent:a,reason:`Dynamic consultation for: ${n.join(", ")}`,result:s,timestamp:new Date})}}async analyzeConsultationNeeds(e,t){let i=[],n=("string"==typeof e?e:JSON.stringify(e)).toLowerCase();return(n.includes("need help")||n.includes("uncertain")||n.includes("not sure"))&&i.push("general_assistance"),!n.includes("code")||t.requiredRoles.includes("coding_backend")||t.requiredRoles.includes("coding_frontend")||i.push("coding_expertise"),n.includes("research")&&!t.requiredRoles.includes("research_synthesis")&&i.push("research_expertise"),(n.includes("creative")||n.includes("innovative")&&!t.requiredRoles.includes("brainstorming_ideation"))&&i.push("creative_expertise"),(n.includes("write")||n.includes("content")&&!t.requiredRoles.includes("writing"))&&i.push("writing_expertise"),i}async findAvailableExperts(e,t,i){let n=await this.getAvailableAgents(e),a=i.map(e=>e.id),s=n.filter(e=>!a.includes(e.id)),o=[];for(let e of t){let t=[];switch(e){case"coding_expertise":t=s.filter(e=>"coding_backend"===e.role||"coding_frontend"===e.role);break;case"research_expertise":t=s.filter(e=>"research_synthesis"===e.role);break;case"creative_expertise":t=s.filter(e=>"brainstorming_ideation"===e.role);break;case"writing_expertise":t=s.filter(e=>"writing"===e.role);break;case"general_assistance":t=s.filter(e=>"general_chat"===e.role||"logic_reasoning"===e.role)}if(t.length>0){t.forEach(t=>{t.consultationScore=this.calculateConsultationScore(t,e)}),t.sort((e,t)=>t.consultationScore-e.consultationScore);let i=t[0];o.some(e=>e.id===i.id)||o.push(i)}}return o}calculateConsultationScore(e,t){let i=this.roleCapabilities[e.role];if(!i)return 0;let n=i.expertiseLevel;for(let e of t.split("_"))i.capabilities.some(t=>t.includes(e))&&(n+=2),i.consultationTriggers.some(t=>t.includes(e))&&(n+=3);return n}async consultWithExpert(e,t,i,n){let a={originalTask:t.description,taskResult:i,originalPrompt:n.originalPrompt,previousResults:n.results,consultationReason:"Dynamic expert consultation",expertRole:e.role},s=await this.executeAgentTask(e,a);return{expert:e.name,role:e.role,consultation:s,timestamp:new Date}}async generateAgentMessage(e,t,i){let n=this.getRoleContext(e.role);return i.map(e=>`${e.agent}: ${e.message}`).join("\n"),`[${e.name}] Based on my ${e.role} expertise: ${n.response}`}summarizeConversation(e){if(0===e.length)return"No conversation occurred";let t=[...new Set(e.map(e=>e.agent))],i=e.map(e=>e.message.substring(0,100)).join("; ");return`Conversation between ${t.join(", ")}. Key points: ${i}`}createRoleSpecificPrompt(e,t){let i=this.getRoleContext(e);return`You are a ${i.title} specialist. ${i.instructions}

Task: ${t.task}
Original Request: ${t.originalPrompt}
Previous Results: ${JSON.stringify(t.previousResults,null,2)}

Please provide your specialized analysis and recommendations based on your ${e} expertise.`}getRoleContext(e){let t={brainstorming_ideation:{title:"Creative Ideation",instructions:"Focus on generating innovative ideas, creative solutions, and novel approaches.",response:"I suggest exploring creative alternatives and innovative approaches to this challenge."},coding_backend:{title:"Backend Development",instructions:"Focus on server-side logic, APIs, databases, and system architecture.",response:"From a backend perspective, we need to consider scalability, data flow, and API design."},coding_frontend:{title:"Frontend Development",instructions:"Focus on user interface, user experience, and client-side implementation.",response:"For the frontend, we should prioritize user experience, responsive design, and performance."},research_synthesis:{title:"Research & Analysis",instructions:"Focus on gathering information, analyzing data, and synthesizing insights.",response:"Based on research and analysis, here are the key findings and recommendations."},writing:{title:"Content Creation",instructions:"Focus on clear communication, engaging content, and effective messaging.",response:"From a content perspective, we should focus on clear, engaging communication."},logic_reasoning:{title:"Logical Analysis",instructions:"Focus on logical reasoning, problem-solving, and systematic analysis.",response:"Applying logical reasoning, here is a systematic approach to this problem."},general_chat:{title:"General Assistant",instructions:"Provide helpful, general assistance and coordinate between specialists.",response:"I can help coordinate and provide general assistance for this task."}};return t[e]||t.general_chat}async getExecutionStatus(e){return null}async getConsultationHistory(e){await this.initializeSupabase();let{data:t}=await this.supabase.from("hybrid_orchestration_executions").select("consultation_history").eq("id",e).single();return t?.consultation_history||[]}async saveExecutionToDatabase(e){await this.initializeSupabase();let t={id:e.id,user_id:e.userId,config_id:e.configId,original_prompt:e.originalPrompt,orchestration_type:e.tasks[0]?.type||"hybrid",detected_roles:e.agents.map(e=>e.role),confidence:.8,reasoning:`Hybrid orchestration with ${e.agents.length} agents and ${e.tasks.length} tasks`,agents_involved:e.agents,tasks_created:e.tasks,consultation_history:e.consultationHistory,status:e.status,results:e.results,created_at:e.createdAt.toISOString()},{error:i}=await this.supabase.from("hybrid_orchestration_executions").insert(t)}async updateExecutionInDatabase(e){await this.initializeSupabase();let t={status:e.status,results:e.results,consultation_history:e.consultationHistory,completed_at:e.completedAt?.toISOString(),processing_duration_ms:e.completedAt&&e.createdAt?e.completedAt.getTime()-e.createdAt.getTime():null,error_message:"failed"===e.status?"Execution failed":null},{error:i}=await this.supabase.from("hybrid_orchestration_executions").update(t).eq("id",e.id)}}class s{constructor(){this.orchestrator=new a}async initializeSupabase(){this.supabase||(this.supabase=await (0,n.createSupabaseServerClientOnRequest)())}async analyzeForHybridOrchestration(e,t=[],i){await this.initializeSupabase();let{data:n}=await this.supabase.from("role_assignments").select("role_name").eq("custom_api_config_id",i),a=n?.map(e=>e.role_name)||[];return 0===a.length?{shouldUseHybrid:!1,confidence:0,reasoning:"No role assignments available",detectedRoles:[],orchestrationType:"non_conversational"}:await this.performHybridAnalysis(e,t,a)}async performHybridAnalysis(e,t,i){let n=e.toLowerCase(),a=[],s="non_conversational",o=0,r=["and then","after that","next","also","additionally","furthermore","both","multiple","various","different","several"].some(e=>n.includes(e));for(let[e,t]of Object.entries({brainstorming_ideation:["brainstorm","idea","creative","innovative","concept","generate ideas"],coding_backend:["backend","server","api","database","algorithm","code"],coding_frontend:["frontend","ui","interface","react","javascript","css"],research_synthesis:["research","analyze","study","investigate","information"],writing:["write","content","article","copy","blog","documentation"],logic_reasoning:["solve","problem","logic","reasoning","analyze","think"],general_chat:["help","assist","explain","tell me","what is"]}))if(i.includes(e)){let i=t.filter(e=>n.includes(e)).length;i>0&&(a.push(e),o+=.2*i)}let c=["discuss","conversation","chat","talk about","debate"].some(e=>n.includes(e));c?(s="conversational",o+=.3):a.length>1&&r&&(s="hybrid",o+=.5);let l=["comprehensive","detailed","complete","full","thorough","extensive","step by step","end to end","from scratch","entire","whole"].some(e=>n.includes(e));l&&(o+=.3),[/brainstorm.*and.*code/i,/research.*and.*write/i,/design.*and.*implement/i,/analyze.*and.*create/i,/plan.*and.*execute/i].some(t=>t.test(e))&&(o+=.4,a.length<2&&(n.includes("brainstorm")&&!a.includes("coding_backend")&&a.push("coding_backend"),n.includes("research")&&!a.includes("writing")&&a.push("writing"))),0===a.length&&i.includes("general_chat")&&a.push("general_chat");let d=(a.length>1||1===a.length&&o>.7||c)&&o>.4;o=Math.min(o,1);let u="";return d?(u=`Detected ${a.length} roles (${a.join(", ")}) with ${s} orchestration. `,r&&(u+="Multi-role indicators found. "),c&&(u+="Conversational approach needed. "),l&&(u+="Complex task requiring coordination. ")):u=`Single-role task detected. Confidence too low (${o.toFixed(2)}) for hybrid orchestration.`,{shouldUseHybrid:d,confidence:o,reasoning:u,detectedRoles:a,orchestrationType:s}}async initiateHybridOrchestration(e,t,i,n=[],a={}){try{let s=await this.orchestrator.orchestrate(e,t,i,{...a,messages:n,timestamp:new Date}),o=await this.createHybridStreamingResponse(s);return{isHybridOrchestration:!0,executionId:s.id,streamingResponse:o}}catch(e){return{isHybridOrchestration:!1,error:e instanceof Error?e.message:"Unknown error"}}}async createHybridStreamingResponse(e){let t=new TextEncoder;return new Response(new ReadableStream({async start(i){try{let n={id:crypto.randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-hybrid-orchestration",choices:[{index:0,delta:{content:`🚀 **Revolutionary Hybrid AI Orchestration Started!**

Your request is being processed by our advanced CrewAI + AutoGen hybrid system.

**Execution ID:** ${e.id}
**Orchestration Type:** ${e.tasks[0]?.type||"hybrid"}
**Agents Involved:** ${e.agents.map(e=>e.name).join(", ")}
**Tasks:** ${e.tasks.length}

🤖 **System Status:** Initializing hybrid orchestration...

`},finish_reason:null}]};i.enqueue(t.encode(`data: ${JSON.stringify(n)}

`)),await new Promise(e=>setTimeout(e,1e3));let a={id:crypto.randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-hybrid-orchestration",choices:[{index:0,delta:{content:`⚡ **Hybrid System Active:** Agents are collaborating using both CrewAI sequential execution and AutoGen conversational patterns.

🔄 **Dynamic Consultation:** System ready to consult additional experts as needed.

📊 **Processing:** Your request is being handled by specialized AI agents working in coordination.

`},finish_reason:null}]};i.enqueue(t.encode(`data: ${JSON.stringify(a)}

`)),await new Promise(e=>setTimeout(e,2e3));let s={id:crypto.randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-hybrid-orchestration",choices:[{index:0,delta:{content:`✅ **Hybrid Orchestration Complete!**

The revolutionary CrewAI + AutoGen hybrid system has successfully processed your request.

**Results:** Comprehensive analysis and solutions provided by multiple specialized AI agents.
**Consultations:** ${e.consultationHistory.length} dynamic expert consultations performed.
**Quality:** Superior output achieved through hybrid orchestration approach.

🎉 **Your hybrid AI orchestration is complete!**`},finish_reason:"stop"}]};i.enqueue(t.encode(`data: ${JSON.stringify(s)}

`)),i.enqueue(t.encode(`data: [DONE]

`)),i.close()}catch(n){let e={id:crypto.randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-hybrid-orchestration",choices:[{index:0,delta:{content:`❌ **Hybrid Orchestration Error:** ${n instanceof Error?n.message:"Unknown error"}

`},finish_reason:"stop"}]};i.enqueue(t.encode(`data: ${JSON.stringify(e)}

`)),i.enqueue(t.encode(`data: [DONE]

`)),i.close()}},cancel(){}}),{headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","Access-Control-Allow-Origin":"*","X-Accel-Buffering":"no","X-RoKey-Hybrid-Orchestration":"true","X-RoKey-Execution-ID":e.id}})}}},39727:()=>{},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},78335:()=>{},96487:()=>{}};