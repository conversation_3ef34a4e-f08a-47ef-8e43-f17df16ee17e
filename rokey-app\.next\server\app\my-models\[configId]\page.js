(()=>{var e={};e.id=4150,e.ids=[4150],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20218:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});let l=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\my-models\\[configId]\\page.tsx","default")},23051:(e,t,r)=>{Promise.resolve().then(r.bind(r,75793))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44725:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var l=r(43210);let s=l.forwardRef(function({title:e,titleId:t,...r},s){return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?l.createElement("title",{id:t},e):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))})},50942:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var l=r(43210);let s=l.forwardRef(function({title:e,titleId:t,...r},s){return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?l.createElement("title",{id:t},e):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))})},51983:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var l=r(65239),s=r(48088),n=r(88170),a=r.n(n),i=r(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let d={children:["",{children:["my-models",{children:["[configId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,20218)),"C:\\RoKey App\\rokey-app\\src\\app\\my-models\\[configId]\\page.tsx"]}]},{}]},{loading:[()=>Promise.resolve().then(r.bind(r,92529)),"C:\\RoKey App\\rokey-app\\src\\app\\my-models\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\my-models\\[configId]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new l.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/my-models/[configId]/page",pathname:"/my-models/[configId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57891:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var l=r(43210);let s=l.forwardRef(function({title:e,titleId:t,...r},s){return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?l.createElement("title",{id:t},e):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))})},58089:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var l=r(43210);let s=l.forwardRef(function({title:e,titleId:t,...r},s){return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?l.createElement("title",{id:t},e):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},60925:(e,t,r)=>{"use strict";r.d(t,{c:()=>n});var l=r(43210);let s={};function n(){let[e,t]=(0,l.useState)({}),r=(0,l.useRef)({}),n=(0,l.useCallback)(e=>{let t=s[e];return!!t&&!(Date.now()-t.timestamp>3e5)&&!t.isLoading},[]),a=(0,l.useCallback)(e=>{let t=s[e];return!t||t.isLoading?null:Date.now()-t.timestamp>3e5?(delete s[e],null):t.data},[]),i=(0,l.useCallback)(async(e,l="medium")=>{if(n(e))return a(e);if(s[e]?.isLoading)return null;r.current[e]&&r.current[e].abort();let i=new AbortController;r.current[e]=i,s[e]={data:{},timestamp:Date.now(),isLoading:!0},t(t=>({...t,[e]:"loading"}));try{"low"===l?await new Promise(e=>setTimeout(e,200)):"medium"===l&&await new Promise(e=>setTimeout(e,50));let[r,n,a]=await Promise.allSettled([fetch(`/api/custom-configs/${e}`,{signal:i.signal}),fetch(`/api/keys?custom_config_id=${e}`,{signal:i.signal}),fetch(`/api/complexity-assignments?custom_config_id=${e}`,{signal:i.signal})]),o=null,d=[],c="none",u={},m=[];"fulfilled"===r.status&&r.value.ok&&(c=(o=await r.value.json()).routing_strategy||"none",u=o.routing_strategy_params||{}),"fulfilled"===n.status&&n.value.ok&&(d=await n.value.json()),"fulfilled"===a.status&&a.value.ok&&(m=await a.value.json());let p={configDetails:o,apiKeys:d,routingStrategy:c,routingParams:u,complexityAssignments:m};return s[e]={data:p,timestamp:Date.now(),isLoading:!1},t(t=>({...t,[e]:"success"})),p}catch(r){if("AbortError"===r.name)return null;return delete s[e],t(t=>({...t,[e]:"error"})),null}finally{delete r.current[e]}},[n,a]),o=(0,l.useCallback)(e=>({onMouseEnter:()=>{n(e)||i(e,"high")}}),[i,n]),d=(0,l.useCallback)(e=>{delete s[e],t(t=>{let r={...t};return delete r[e],r})},[]),c=(0,l.useCallback)(()=>{Object.keys(s).forEach(e=>{delete s[e]}),t({})},[]);return{prefetchRoutingSetupData:i,getCachedData:a,isCached:n,createHoverPrefetch:o,clearCache:d,clearAllCache:c,getStatus:(0,l.useCallback)(t=>e[t]||"idle",[e]),getCacheInfo:(0,l.useCallback)(()=>({cachedConfigs:Object.keys(s),cacheSize:Object.keys(s).length,totalCacheAge:Object.values(s).reduce((e,t)=>e+(Date.now()-t.timestamp),0)/Object.keys(s).length}),[]),prefetchStatus:e}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66368:(e,t,r)=>{"use strict";r.d(t,{MG:()=>l});let l=[{id:"openai",name:"OpenAI",apiBaseUrl:"https://api.openai.com/v1/chat/completions",models:[]},{id:"google",name:"Google",apiBaseUrl:"https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",models:[]},{id:"anthropic",name:"Anthropic",apiBaseUrl:"https://api.anthropic.com/v1/chat/completions",models:[]},{id:"deepseek",name:"DeepSeek",apiBaseUrl:"https://api.deepseek.com/chat/completions",models:[]},{id:"xai",name:"xAI (Grok)",apiBaseUrl:"https://api.x.ai/v1/chat/completions",models:[]},{id:"openrouter",name:"OpenRouter",apiBaseUrl:"https://openrouter.ai/api/v1/chat/completions",models:[]}]},69662:(e,t)=>{var r;!function(){"use strict";var l={}.hasOwnProperty;function s(){for(var e="",t=0;t<arguments.length;t++){var r=arguments[t];r&&(e=n(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return s.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var r in e)l.call(e,r)&&e[r]&&(t=n(t,r));return t}(r)))}return e}function n(e,t){return t?e?e+" "+t:e+t:e}e.exports?(s.default=s,e.exports=s):void 0===(r=(function(){return s}).apply(t,[]))||(e.exports=r)}()},74075:e=>{"use strict";e.exports=require("zlib")},75793:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>e0});var l=r(60687),s=r(43210),n=r(16189),a=r(66368);let i=[{id:"general_chat",name:"General Chat",description:"Handles general conversation, Q&A, and tasks not covered by other specific roles. Often the default fallback."},{id:"logic_reasoning",name:"Logic & Reasoning",description:"For tasks requiring logical deduction, problem-solving, mathematical reasoning, and complex analytical thinking."},{id:"writing",name:"Writing & Content Creation",description:"For all writing tasks, including articles, blog posts, marketing copy, creative content, essays, and more."},{id:"coding_frontend",name:"Coding - Frontend",description:"For generating and assisting with HTML, CSS, JavaScript, and frontend frameworks (React, Vue, Angular, etc.)."},{id:"coding_backend",name:"Coding - Backend",description:"For generating and assisting with server-side logic, APIs, databases, and backend frameworks (Node.js, Python, Java, etc.)."},{id:"research_synthesis",name:"Research & Synthesis",description:"For information retrieval from various sources, data analysis, and synthesizing findings into reports or summaries."},{id:"summarization_briefing",name:"Summarization & Briefing",description:"For condensing long texts, documents, or conversations into concise summaries or executive briefings."},{id:"translation_localization",name:"Translation & Localization",description:"For translating text between languages and adapting content culturally for different locales."},{id:"data_extraction_structuring",name:"Data Extraction & Structuring",description:"For identifying and extracting specific pieces of information from unstructured/semi-structured text and organizing it."},{id:"brainstorming_ideation",name:"Brainstorming & Ideation",description:"For generating new ideas, exploring concepts, and creative problem-solving sessions."},{id:"education_tutoring",name:"Education & Tutoring",description:"For explaining concepts, answering educational questions, and providing tutoring assistance across various subjects."},{id:"image_generation",name:"Image Generation",description:"For creating images from textual descriptions. Assign to keys linked to image generation models."},{id:"audio_transcription",name:"Audio Transcription",description:"For converting speech from audio files into written text. Assign to keys linked to transcription models."}],o=e=>i.find(t=>t.id===e),d=s.forwardRef(function({title:e,titleId:t,...r},l){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:l,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});var c=r(71031),u=r(51426),m=r(26403),p=r(44725);let f=s.forwardRef(function({title:e,titleId:t,...r},l){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:l,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9.75v6.75m0 0-3-3m3 3 3-3m-8.25 6a4.5 4.5 0 0 1-1.41-8.775 5.25 5.25 0 0 1 10.233-2.33 3 3 0 0 1 3.758 3.848A3.752 3.752 0 0 1 18 19.5H6.75Z"}))});var h=r(58089),g=r(57891);let x=s.forwardRef(function({title:e,titleId:t,...r},l){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:l,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"}))});var y=r(97450),v=r(50942),b=r(71178);let w=Math.min,j=Math.max,N=Math.round,_=Math.floor,k=e=>({x:e,y:e}),E={left:"right",right:"left",bottom:"top",top:"bottom"},S={start:"end",end:"start"};function C(e,t){return"function"==typeof e?e(t):e}function A(e){return e.split("-")[0]}function R(e){return e.split("-")[1]}function T(e){return"x"===e?"y":"x"}function L(e){return"y"===e?"height":"width"}function O(e){return["top","bottom"].includes(A(e))?"y":"x"}function I(e){return e.replace(/start|end/g,e=>S[e])}function P(e){return e.replace(/left|right|bottom|top/g,e=>E[e])}function $(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function D(e){let{x:t,y:r,width:l,height:s}=e;return{width:l,height:s,top:r,left:t,right:t+l,bottom:r+s,x:t,y:r}}function F(e,t,r){let l,{reference:s,floating:n}=e,a=O(t),i=T(O(t)),o=L(i),d=A(t),c="y"===a,u=s.x+s.width/2-n.width/2,m=s.y+s.height/2-n.height/2,p=s[o]/2-n[o]/2;switch(d){case"top":l={x:u,y:s.y-n.height};break;case"bottom":l={x:u,y:s.y+s.height};break;case"right":l={x:s.x+s.width,y:m};break;case"left":l={x:s.x-n.width,y:m};break;default:l={x:s.x,y:s.y}}switch(R(t)){case"start":l[i]-=p*(r&&c?-1:1);break;case"end":l[i]+=p*(r&&c?-1:1)}return l}let M=async(e,t,r)=>{let{placement:l="bottom",strategy:s="absolute",middleware:n=[],platform:a}=r,i=n.filter(Boolean),o=await (null==a.isRTL?void 0:a.isRTL(t)),d=await a.getElementRects({reference:e,floating:t,strategy:s}),{x:c,y:u}=F(d,l,o),m=l,p={},f=0;for(let r=0;r<i.length;r++){let{name:n,fn:h}=i[r],{x:g,y:x,data:y,reset:v}=await h({x:c,y:u,initialPlacement:l,placement:m,strategy:s,middlewareData:p,rects:d,platform:a,elements:{reference:e,floating:t}});c=null!=g?g:c,u=null!=x?x:u,p={...p,[n]:{...p[n],...y}},v&&f<=50&&(f++,"object"==typeof v&&(v.placement&&(m=v.placement),v.rects&&(d=!0===v.rects?await a.getElementRects({reference:e,floating:t,strategy:s}):v.rects),{x:c,y:u}=F(d,m,o)),r=-1)}return{x:c,y:u,placement:m,strategy:s,middlewareData:p}};async function B(e,t){var r;void 0===t&&(t={});let{x:l,y:s,platform:n,rects:a,elements:i,strategy:o}=e,{boundary:d="clippingAncestors",rootBoundary:c="viewport",elementContext:u="floating",altBoundary:m=!1,padding:p=0}=C(t,e),f=$(p),h=i[m?"floating"===u?"reference":"floating":u],g=D(await n.getClippingRect({element:null==(r=await (null==n.isElement?void 0:n.isElement(h)))||r?h:h.contextElement||await (null==n.getDocumentElement?void 0:n.getDocumentElement(i.floating)),boundary:d,rootBoundary:c,strategy:o})),x="floating"===u?{x:l,y:s,width:a.floating.width,height:a.floating.height}:a.reference,y=await (null==n.getOffsetParent?void 0:n.getOffsetParent(i.floating)),v=await (null==n.isElement?void 0:n.isElement(y))&&await (null==n.getScale?void 0:n.getScale(y))||{x:1,y:1},b=D(n.convertOffsetParentRelativeRectToViewportRelativeRect?await n.convertOffsetParentRelativeRectToViewportRelativeRect({elements:i,rect:x,offsetParent:y,strategy:o}):x);return{top:(g.top-b.top+f.top)/v.y,bottom:(b.bottom-g.bottom+f.bottom)/v.y,left:(g.left-b.left+f.left)/v.x,right:(b.right-g.right+f.right)/v.x}}async function K(e,t){let{placement:r,platform:l,elements:s}=e,n=await (null==l.isRTL?void 0:l.isRTL(s.floating)),a=A(r),i=R(r),o="y"===O(r),d=["left","top"].includes(a)?-1:1,c=n&&o?-1:1,u=C(t,e),{mainAxis:m,crossAxis:p,alignmentAxis:f}="number"==typeof u?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return i&&"number"==typeof f&&(p="end"===i?-1*f:f),o?{x:p*c,y:m*d}:{x:m*d,y:p*c}}function q(){return"undefined"!=typeof window}function W(e){return G(e)?(e.nodeName||"").toLowerCase():"#document"}function z(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function H(e){var t;return null==(t=(G(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function G(e){return!!q()&&(e instanceof Node||e instanceof z(e).Node)}function V(e){return!!q()&&(e instanceof Element||e instanceof z(e).Element)}function U(e){return!!q()&&(e instanceof HTMLElement||e instanceof z(e).HTMLElement)}function Z(e){return!!q()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof z(e).ShadowRoot)}function J(e){let{overflow:t,overflowX:r,overflowY:l,display:s}=et(e);return/auto|scroll|overlay|hidden|clip/.test(t+l+r)&&!["inline","contents"].includes(s)}function X(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function Y(e){let t=Q(),r=V(e)?et(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(r.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(r.contain||"").includes(e))}function Q(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function ee(e){return["html","body","#document"].includes(W(e))}function et(e){return z(e).getComputedStyle(e)}function er(e){return V(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function el(e){if("html"===W(e))return e;let t=e.assignedSlot||e.parentNode||Z(e)&&e.host||H(e);return Z(t)?t.host:t}function es(e,t,r){var l;void 0===t&&(t=[]),void 0===r&&(r=!0);let s=function e(t){let r=el(t);return ee(r)?t.ownerDocument?t.ownerDocument.body:t.body:U(r)&&J(r)?r:e(r)}(e),n=s===(null==(l=e.ownerDocument)?void 0:l.body),a=z(s);if(n){let e=en(a);return t.concat(a,a.visualViewport||[],J(s)?s:[],e&&r?es(e):[])}return t.concat(s,es(s,[],r))}function en(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ea(e){let t=et(e),r=parseFloat(t.width)||0,l=parseFloat(t.height)||0,s=U(e),n=s?e.offsetWidth:r,a=s?e.offsetHeight:l,i=N(r)!==n||N(l)!==a;return i&&(r=n,l=a),{width:r,height:l,$:i}}function ei(e){return V(e)?e:e.contextElement}function eo(e){let t=ei(e);if(!U(t))return k(1);let r=t.getBoundingClientRect(),{width:l,height:s,$:n}=ea(t),a=(n?N(r.width):r.width)/l,i=(n?N(r.height):r.height)/s;return a&&Number.isFinite(a)||(a=1),i&&Number.isFinite(i)||(i=1),{x:a,y:i}}let ed=k(0);function ec(e){let t=z(e);return Q()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ed}function eu(e,t,r,l){var s;void 0===t&&(t=!1),void 0===r&&(r=!1);let n=e.getBoundingClientRect(),a=ei(e),i=k(1);t&&(l?V(l)&&(i=eo(l)):i=eo(e));let o=(void 0===(s=r)&&(s=!1),l&&(!s||l===z(a))&&s)?ec(a):k(0),d=(n.left+o.x)/i.x,c=(n.top+o.y)/i.y,u=n.width/i.x,m=n.height/i.y;if(a){let e=z(a),t=l&&V(l)?z(l):l,r=e,s=en(r);for(;s&&l&&t!==r;){let e=eo(s),t=s.getBoundingClientRect(),l=et(s),n=t.left+(s.clientLeft+parseFloat(l.paddingLeft))*e.x,a=t.top+(s.clientTop+parseFloat(l.paddingTop))*e.y;d*=e.x,c*=e.y,u*=e.x,m*=e.y,d+=n,c+=a,s=en(r=z(s))}}return D({width:u,height:m,x:d,y:c})}function em(e,t){let r=er(e).scrollLeft;return t?t.left+r:eu(H(e)).left+r}function ep(e,t,r){void 0===r&&(r=!1);let l=e.getBoundingClientRect();return{x:l.left+t.scrollLeft-(r?0:em(e,l)),y:l.top+t.scrollTop}}function ef(e,t,r){let l;if("viewport"===t)l=function(e,t){let r=z(e),l=H(e),s=r.visualViewport,n=l.clientWidth,a=l.clientHeight,i=0,o=0;if(s){n=s.width,a=s.height;let e=Q();(!e||e&&"fixed"===t)&&(i=s.offsetLeft,o=s.offsetTop)}return{width:n,height:a,x:i,y:o}}(e,r);else if("document"===t)l=function(e){let t=H(e),r=er(e),l=e.ownerDocument.body,s=j(t.scrollWidth,t.clientWidth,l.scrollWidth,l.clientWidth),n=j(t.scrollHeight,t.clientHeight,l.scrollHeight,l.clientHeight),a=-r.scrollLeft+em(e),i=-r.scrollTop;return"rtl"===et(l).direction&&(a+=j(t.clientWidth,l.clientWidth)-s),{width:s,height:n,x:a,y:i}}(H(e));else if(V(t))l=function(e,t){let r=eu(e,!0,"fixed"===t),l=r.top+e.clientTop,s=r.left+e.clientLeft,n=U(e)?eo(e):k(1),a=e.clientWidth*n.x,i=e.clientHeight*n.y;return{width:a,height:i,x:s*n.x,y:l*n.y}}(t,r);else{let r=ec(e);l={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return D(l)}function eh(e){return"static"===et(e).position}function eg(e,t){if(!U(e)||"fixed"===et(e).position)return null;if(t)return t(e);let r=e.offsetParent;return H(e)===r&&(r=r.ownerDocument.body),r}function ex(e,t){let r=z(e);if(X(e))return r;if(!U(e)){let t=el(e);for(;t&&!ee(t);){if(V(t)&&!eh(t))return t;t=el(t)}return r}let l=eg(e,t);for(;l&&["table","td","th"].includes(W(l))&&eh(l);)l=eg(l,t);return l&&ee(l)&&eh(l)&&!Y(l)?r:l||function(e){let t=el(e);for(;U(t)&&!ee(t);){if(Y(t))return t;if(X(t))break;t=el(t)}return null}(e)||r}let ey=async function(e){let t=this.getOffsetParent||ex,r=this.getDimensions,l=await r(e.floating);return{reference:function(e,t,r){let l=U(t),s=H(t),n="fixed"===r,a=eu(e,!0,n,t),i={scrollLeft:0,scrollTop:0},o=k(0);if(l||!l&&!n)if(("body"!==W(t)||J(s))&&(i=er(t)),l){let e=eu(t,!0,n,t);o.x=e.x+t.clientLeft,o.y=e.y+t.clientTop}else s&&(o.x=em(s));n&&!l&&s&&(o.x=em(s));let d=!s||l||n?k(0):ep(s,i);return{x:a.left+i.scrollLeft-o.x-d.x,y:a.top+i.scrollTop-o.y-d.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:l.width,height:l.height}}},ev={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:l,strategy:s}=e,n="fixed"===s,a=H(l),i=!!t&&X(t.floating);if(l===a||i&&n)return r;let o={scrollLeft:0,scrollTop:0},d=k(1),c=k(0),u=U(l);if((u||!u&&!n)&&(("body"!==W(l)||J(a))&&(o=er(l)),U(l))){let e=eu(l);d=eo(l),c.x=e.x+l.clientLeft,c.y=e.y+l.clientTop}let m=!a||u||n?k(0):ep(a,o,!0);return{width:r.width*d.x,height:r.height*d.y,x:r.x*d.x-o.scrollLeft*d.x+c.x+m.x,y:r.y*d.y-o.scrollTop*d.y+c.y+m.y}},getDocumentElement:H,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:l,strategy:s}=e,n=[..."clippingAncestors"===r?X(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let l=es(e,[],!1).filter(e=>V(e)&&"body"!==W(e)),s=null,n="fixed"===et(e).position,a=n?el(e):e;for(;V(a)&&!ee(a);){let t=et(a),r=Y(a);r||"fixed"!==t.position||(s=null),(n?!r&&!s:!r&&"static"===t.position&&!!s&&["absolute","fixed"].includes(s.position)||J(a)&&!r&&function e(t,r){let l=el(t);return!(l===r||!V(l)||ee(l))&&("fixed"===et(l).position||e(l,r))}(e,a))?l=l.filter(e=>e!==a):s=t,a=el(a)}return t.set(e,l),l}(t,this._c):[].concat(r),l],a=n[0],i=n.reduce((e,r)=>{let l=ef(t,r,s);return e.top=j(l.top,e.top),e.right=w(l.right,e.right),e.bottom=w(l.bottom,e.bottom),e.left=j(l.left,e.left),e},ef(t,a,s));return{width:i.right-i.left,height:i.bottom-i.top,x:i.left,y:i.top}},getOffsetParent:ex,getElementRects:ey,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=ea(e);return{width:t,height:r}},getScale:eo,isElement:V,isRTL:function(e){return"rtl"===et(e).direction}};function eb(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ew=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,l;let{x:s,y:n,placement:a,middlewareData:i}=t,o=await K(t,e);return a===(null==(r=i.offset)?void 0:r.placement)&&null!=(l=i.arrow)&&l.alignmentOffset?{}:{x:s+o.x,y:n+o.y,data:{...o,placement:a}}}}},ej=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:l,placement:s}=t,{mainAxis:n=!0,crossAxis:a=!1,limiter:i={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...o}=C(e,t),d={x:r,y:l},c=await B(t,o),u=O(A(s)),m=T(u),p=d[m],f=d[u];if(n){let e="y"===m?"top":"left",t="y"===m?"bottom":"right",r=p+c[e],l=p-c[t];p=j(r,w(p,l))}if(a){let e="y"===u?"top":"left",t="y"===u?"bottom":"right",r=f+c[e],l=f-c[t];f=j(r,w(f,l))}let h=i.fn({...t,[m]:p,[u]:f});return{...h,data:{x:h.x-r,y:h.y-l,enabled:{[m]:n,[u]:a}}}}}},eN=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,l,s,n,a,i;let{placement:o,middlewareData:d,rects:c,initialPlacement:u,platform:m,elements:p}=t,{mainAxis:f=!0,crossAxis:h=!0,fallbackPlacements:g,fallbackStrategy:x="bestFit",fallbackAxisSideDirection:y="none",flipAlignment:v=!0,...b}=C(e,t);if(null!=(r=d.arrow)&&r.alignmentOffset)return{};let w=A(o),j=O(u),N=A(u)===u,_=await (null==m.isRTL?void 0:m.isRTL(p.floating)),k=g||(N||!v?[P(u)]:function(e){let t=P(e);return[I(e),t,I(t)]}(u)),E="none"!==y;!g&&E&&k.push(...function(e,t,r,l){let s=R(e),n=function(e,t,r){let l=["left","right"],s=["right","left"];switch(e){case"top":case"bottom":if(r)return t?s:l;return t?l:s;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(A(e),"start"===r,l);return s&&(n=n.map(e=>e+"-"+s),t&&(n=n.concat(n.map(I)))),n}(u,v,y,_));let S=[u,...k],$=await B(t,b),D=[],F=(null==(l=d.flip)?void 0:l.overflows)||[];if(f&&D.push($[w]),h){let e=function(e,t,r){void 0===r&&(r=!1);let l=R(e),s=T(O(e)),n=L(s),a="x"===s?l===(r?"end":"start")?"right":"left":"start"===l?"bottom":"top";return t.reference[n]>t.floating[n]&&(a=P(a)),[a,P(a)]}(o,c,_);D.push($[e[0]],$[e[1]])}if(F=[...F,{placement:o,overflows:D}],!D.every(e=>e<=0)){let e=((null==(s=d.flip)?void 0:s.index)||0)+1,t=S[e];if(t){let r="alignment"===h&&j!==O(t),l=(null==(a=F[0])?void 0:a.overflows[0])>0;if(!r||l)return{data:{index:e,overflows:F},reset:{placement:t}}}let r=null==(n=F.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:n.placement;if(!r)switch(x){case"bestFit":{let e=null==(i=F.filter(e=>{if(E){let t=O(e.placement);return t===j||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:i[0];e&&(r=e);break}case"initialPlacement":r=u}if(o!==r)return{reset:{placement:r}}}return{}}}},e_=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:l,placement:s,rects:n,platform:a,elements:i,middlewareData:o}=t,{element:d,padding:c=0}=C(e,t)||{};if(null==d)return{};let u=$(c),m={x:r,y:l},p=T(O(s)),f=L(p),h=await a.getDimensions(d),g="y"===p,x=g?"clientHeight":"clientWidth",y=n.reference[f]+n.reference[p]-m[p]-n.floating[f],v=m[p]-n.reference[p],b=await (null==a.getOffsetParent?void 0:a.getOffsetParent(d)),N=b?b[x]:0;N&&await (null==a.isElement?void 0:a.isElement(b))||(N=i.floating[x]||n.floating[f]);let _=N/2-h[f]/2-1,k=w(u[g?"top":"left"],_),E=w(u[g?"bottom":"right"],_),S=N-h[f]-E,A=N/2-h[f]/2+(y/2-v/2),I=j(k,w(A,S)),P=!o.arrow&&null!=R(s)&&A!==I&&n.reference[f]/2-(A<k?k:E)-h[f]/2<0,D=P?A<k?A-k:A-S:0;return{[p]:m[p]+D,data:{[p]:I,centerOffset:A-I-D,...P&&{alignmentOffset:D}},reset:P}}}),ek=(e,t,r)=>{let l=new Map,s={platform:ev,...r},n={...s.platform,_c:l};return M(e,t,{...s,platform:n})};var eE=r(69662);let eS={core:!1,base:!1};function eC({css:e,id:t="react-tooltip-base-styles",type:r="base",ref:l}){var s,n;if(!e||"undefined"==typeof document||eS[r]||"core"===r&&"undefined"!=typeof process&&(null==(s=null==process?void 0:process.env)?void 0:s.REACT_TOOLTIP_DISABLE_CORE_STYLES)||"base"!==r&&"undefined"!=typeof process&&(null==(n=null==process?void 0:process.env)?void 0:n.REACT_TOOLTIP_DISABLE_BASE_STYLES))return;"core"===r&&(t="react-tooltip-core-styles"),l||(l={});let{insertAt:a}=l;if(document.getElementById(t))return;let i=document.head||document.getElementsByTagName("head")[0],o=document.createElement("style");o.id=t,o.type="text/css","top"===a&&i.firstChild?i.insertBefore(o,i.firstChild):i.appendChild(o),o.styleSheet?o.styleSheet.cssText=e:o.appendChild(document.createTextNode(e)),eS[r]=!0}let eA=async({elementReference:e=null,tooltipReference:t=null,tooltipArrowReference:r=null,place:l="top",offset:s=10,strategy:n="absolute",middlewares:a=[ew(Number(s)),eN({fallbackAxisSideDirection:"start"}),ej({padding:5})],border:i})=>e&&null!==t?r?(a.push(e_({element:r,padding:5})),ek(e,t,{placement:l,strategy:n,middleware:a}).then(({x:e,y:t,placement:r,middlewareData:l})=>{var s,n;let a={left:`${e}px`,top:`${t}px`,border:i},{x:o,y:d}=null!=(s=l.arrow)?s:{x:0,y:0},c=null!=(n=({top:"bottom",right:"left",bottom:"top",left:"right"})[r.split("-")[0]])?n:"bottom",u=0;if(i){let e=`${i}`.match(/(\d+)px/);u=(null==e?void 0:e[1])?Number(e[1]):1}return{tooltipStyles:a,tooltipArrowStyles:{left:null!=o?`${o}px`:"",top:null!=d?`${d}px`:"",right:"",bottom:"",...i&&{borderBottom:i,borderRight:i},[c]:`-${4+u}px`},place:r}})):ek(e,t,{placement:"bottom",strategy:n,middleware:a}).then(({x:e,y:t,placement:r})=>({tooltipStyles:{left:`${e}px`,top:`${t}px`},tooltipArrowStyles:{},place:r})):{tooltipStyles:{},tooltipArrowStyles:{},place:l},eR=(e,t)=>!("CSS"in window&&"supports"in window.CSS)||window.CSS.supports(e,t),eT=(e,t,r)=>{let l=null,s=function(...s){let n=()=>{l=null,r||e.apply(this,s)};r&&!l&&(e.apply(this,s),l=setTimeout(n,t)),r||(l&&clearTimeout(l),l=setTimeout(n,t))};return s.cancel=()=>{l&&(clearTimeout(l),l=null)},s},eL=e=>null!==e&&!Array.isArray(e)&&"object"==typeof e,eO=(e,t)=>{if(e===t)return!0;if(Array.isArray(e)&&Array.isArray(t))return e.length===t.length&&e.every((e,r)=>eO(e,t[r]));if(Array.isArray(e)!==Array.isArray(t))return!1;if(!eL(e)||!eL(t))return e===t;let r=Object.keys(e),l=Object.keys(t);return r.length===l.length&&r.every(r=>eO(e[r],t[r]))},eI=e=>{if(!(e instanceof HTMLElement||e instanceof SVGElement))return!1;let t=getComputedStyle(e);return["overflow","overflow-x","overflow-y"].some(e=>{let r=t.getPropertyValue(e);return"auto"===r||"scroll"===r})},eP=e=>{if(!e)return null;let t=e.parentElement;for(;t;){if(eI(t))return t;t=t.parentElement}return document.scrollingElement||document.documentElement},e$="undefined"!=typeof window?s.useLayoutEffect:s.useEffect,eD=e=>{e.current&&(clearTimeout(e.current),e.current=null)},eF={anchorRefs:new Set,activeAnchor:{current:null},attach:()=>{},detach:()=>{},setActiveAnchor:()=>{}},eM=(0,s.createContext)({getTooltipData:()=>eF});function eB(e="DEFAULT_TOOLTIP_ID"){return(0,s.useContext)(eM).getTooltipData(e)}var eK={tooltip:"core-styles-module_tooltip__3vRRp",fixed:"core-styles-module_fixed__pcSol",arrow:"core-styles-module_arrow__cvMwQ",noArrow:"core-styles-module_noArrow__xock6",clickable:"core-styles-module_clickable__ZuTTB",show:"core-styles-module_show__Nt9eE",closing:"core-styles-module_closing__sGnxF"},eq={tooltip:"styles-module_tooltip__mnnfp",arrow:"styles-module_arrow__K0L3T",dark:"styles-module_dark__xNqje",light:"styles-module_light__Z6W-X",success:"styles-module_success__A2AKt",warning:"styles-module_warning__SCK0X",error:"styles-module_error__JvumD",info:"styles-module_info__BWdHW"};let eW=({forwardRef:e,id:t,className:r,classNameArrow:l,variant:n="dark",anchorId:a,anchorSelect:i,place:o="top",offset:d=10,events:c=["hover"],openOnClick:u=!1,positionStrategy:m="absolute",middlewares:p,wrapper:f,delayShow:h=0,delayHide:g=0,float:x=!1,hidden:y=!1,noArrow:v=!1,clickable:b=!1,closeOnEsc:N=!1,closeOnScroll:k=!1,closeOnResize:E=!1,openEvents:S,closeEvents:C,globalCloseEvents:A,imperativeModeOnly:R,style:T,position:L,afterShow:O,afterHide:I,disableTooltip:P,content:$,contentWrapperRef:D,isOpen:F,defaultIsOpen:M=!1,setIsOpen:B,activeAnchor:K,setActiveAnchor:q,border:W,opacity:z,arrowColor:G,role:V="tooltip"})=>{var U;let Z=(0,s.useRef)(null),J=(0,s.useRef)(null),X=(0,s.useRef)(null),Y=(0,s.useRef)(null),Q=(0,s.useRef)(null),[ee,et]=(0,s.useState)({tooltipStyles:{},tooltipArrowStyles:{},place:o}),[er,el]=(0,s.useState)(!1),[en,ea]=(0,s.useState)(!1),[eo,ed]=(0,s.useState)(null),ec=(0,s.useRef)(!1),em=(0,s.useRef)(null),{anchorRefs:ep,setActiveAnchor:ef}=eB(t),eh=(0,s.useRef)(!1),[eg,ex]=(0,s.useState)([]),ey=(0,s.useRef)(!1),ev=u||c.includes("click"),ew=ev||(null==S?void 0:S.click)||(null==S?void 0:S.dblclick)||(null==S?void 0:S.mousedown),ej=S?{...S}:{mouseover:!0,focus:!0,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1};!S&&ev&&Object.assign(ej,{mouseenter:!1,focus:!1,mouseover:!1,click:!0});let eN=C?{...C}:{mouseout:!0,blur:!0,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1};!C&&ev&&Object.assign(eN,{mouseleave:!1,blur:!1,mouseout:!1});let e_=A?{...A}:{escape:N||!1,scroll:k||!1,resize:E||!1,clickOutsideAnchor:ew||!1};R&&(Object.assign(ej,{mouseover:!1,focus:!1,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1}),Object.assign(eN,{mouseout:!1,blur:!1,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1}),Object.assign(e_,{escape:!1,scroll:!1,resize:!1,clickOutsideAnchor:!1})),e$(()=>(ey.current=!0,()=>{ey.current=!1}),[]);let ek=e=>{ey.current&&(e&&ea(!0),setTimeout(()=>{ey.current&&(null==B||B(e),void 0===F&&el(e))},10))};(0,s.useEffect)(()=>{if(void 0===F)return()=>null;F&&ea(!0);let e=setTimeout(()=>{el(F)},10);return()=>{clearTimeout(e)}},[F]),(0,s.useEffect)(()=>{er!==ec.current&&((eD(Q),ec.current=er,er)?null==O||O():Q.current=setTimeout(()=>{ea(!1),ed(null),null==I||I()},(e=>{let t=e.match(/^([\d.]+)(ms|s)$/);if(!t)return 0;let[,r,l]=t;return Number(r)*("ms"===l?1:1e3)})(getComputedStyle(document.body).getPropertyValue("--rt-transition-show-delay"))+25))},[er]);let eS=e=>{et(t=>eO(t,e)?t:e)},eC=(e=h)=>{eD(X),en?ek(!0):X.current=setTimeout(()=>{ek(!0)},e)},eR=(e=g)=>{eD(Y),Y.current=setTimeout(()=>{eh.current||ek(!1)},e)},eL=e=>{var t;if(!e)return;let r=null!=(t=e.currentTarget)?t:e.target;if(!(null==r?void 0:r.isConnected))return q(null),void ef({current:null});h?eC():ek(!0),q(r),ef({current:r}),eD(Y)},eI=()=>{b?eR(g||100):g?eR():ek(!1),eD(X)},eF=({x:e,y:t})=>{var r;eA({place:null!=(r=null==eo?void 0:eo.place)?r:o,offset:d,elementReference:{getBoundingClientRect:()=>({x:e,y:t,width:0,height:0,top:t,left:e,right:e,bottom:t})},tooltipReference:Z.current,tooltipArrowReference:J.current,strategy:m,middlewares:p,border:W}).then(e=>{eS(e)})},eM=e=>{if(!e)return;let t={x:e.clientX,y:e.clientY};eF(t),em.current=t},eW=e=>{var t;if(!er)return;let r=e.target;r.isConnected&&(null==(t=Z.current)||!t.contains(r))&&([document.querySelector(`[id='${a}']`),...eg].some(e=>null==e?void 0:e.contains(r))||(ek(!1),eD(X)))},ez=eT(eL,50,!0),eH=eT(eI,50,!0),eG=e=>{eH.cancel(),ez(e)},eV=()=>{ez.cancel(),eH()},eU=(0,s.useCallback)(()=>{var e,t;let r=null!=(e=null==eo?void 0:eo.position)?e:L;r?eF(r):x?em.current&&eF(em.current):(null==K?void 0:K.isConnected)&&eA({place:null!=(t=null==eo?void 0:eo.place)?t:o,offset:d,elementReference:K,tooltipReference:Z.current,tooltipArrowReference:J.current,strategy:m,middlewares:p,border:W}).then(e=>{ey.current&&eS(e)})},[er,K,$,T,o,null==eo?void 0:eo.place,d,m,L,null==eo?void 0:eo.position,x]);(0,s.useEffect)(()=>{var e,t;let r=new Set(ep);eg.forEach(e=>{(null==P?void 0:P(e))||r.add({current:e})});let l=document.querySelector(`[id='${a}']`);!l||(null==P?void 0:P(l))||r.add({current:l});let s=()=>{ek(!1)},n=eP(K),i=eP(Z.current);e_.scroll&&(window.addEventListener("scroll",s),null==n||n.addEventListener("scroll",s),null==i||i.addEventListener("scroll",s));let o=null;e_.resize?window.addEventListener("resize",s):K&&Z.current&&(o=function(e,t,r,l){let s;void 0===l&&(l={});let{ancestorScroll:n=!0,ancestorResize:a=!0,elementResize:i="function"==typeof ResizeObserver,layoutShift:o="function"==typeof IntersectionObserver,animationFrame:d=!1}=l,c=ei(e),u=n||a?[...c?es(c):[],...es(t)]:[];u.forEach(e=>{n&&e.addEventListener("scroll",r,{passive:!0}),a&&e.addEventListener("resize",r)});let m=c&&o?function(e,t){let r,l=null,s=H(e);function n(){var e;clearTimeout(r),null==(e=l)||e.disconnect(),l=null}return!function a(i,o){void 0===i&&(i=!1),void 0===o&&(o=1),n();let d=e.getBoundingClientRect(),{left:c,top:u,width:m,height:p}=d;if(i||t(),!m||!p)return;let f=_(u),h=_(s.clientWidth-(c+m)),g={rootMargin:-f+"px "+-h+"px "+-_(s.clientHeight-(u+p))+"px "+-_(c)+"px",threshold:j(0,w(1,o))||1},x=!0;function y(t){let l=t[0].intersectionRatio;if(l!==o){if(!x)return a();l?a(!1,l):r=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==l||eb(d,e.getBoundingClientRect())||a(),x=!1}try{l=new IntersectionObserver(y,{...g,root:s.ownerDocument})}catch(e){l=new IntersectionObserver(y,g)}l.observe(e)}(!0),n}(c,r):null,p=-1,f=null;i&&(f=new ResizeObserver(e=>{let[l]=e;l&&l.target===c&&f&&(f.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=f)||e.observe(t)})),r()}),c&&!d&&f.observe(c),f.observe(t));let h=d?eu(e):null;return d&&function t(){let l=eu(e);h&&!eb(h,l)&&r(),h=l,s=requestAnimationFrame(t)}(),r(),()=>{var e;u.forEach(e=>{n&&e.removeEventListener("scroll",r),a&&e.removeEventListener("resize",r)}),null==m||m(),null==(e=f)||e.disconnect(),f=null,d&&cancelAnimationFrame(s)}}(K,Z.current,eU,{ancestorResize:!0,elementResize:!0,layoutShift:!0}));let d=e=>{"Escape"===e.key&&ek(!1)};e_.escape&&window.addEventListener("keydown",d),e_.clickOutsideAnchor&&window.addEventListener("click",eW);let c=[],u=e=>!!((null==e?void 0:e.target)&&(null==K?void 0:K.contains(e.target))),m=e=>{er&&u(e)||eL(e)},p=e=>{er&&u(e)&&eI()},f=["mouseover","mouseout","mouseenter","mouseleave","focus","blur"],h=["click","dblclick","mousedown","mouseup"];Object.entries(ej).forEach(([e,t])=>{t&&(f.includes(e)?c.push({event:e,listener:eG}):h.includes(e)&&c.push({event:e,listener:m}))}),Object.entries(eN).forEach(([e,t])=>{t&&(f.includes(e)?c.push({event:e,listener:eV}):h.includes(e)&&c.push({event:e,listener:p}))}),x&&c.push({event:"pointermove",listener:eM});let g=()=>{eh.current=!0},y=()=>{eh.current=!1,eI()},v=b&&(eN.mouseout||eN.mouseleave);return v&&(null==(e=Z.current)||e.addEventListener("mouseover",g),null==(t=Z.current)||t.addEventListener("mouseout",y)),c.forEach(({event:e,listener:t})=>{r.forEach(r=>{var l;null==(l=r.current)||l.addEventListener(e,t)})}),()=>{var e,t;e_.scroll&&(window.removeEventListener("scroll",s),null==n||n.removeEventListener("scroll",s),null==i||i.removeEventListener("scroll",s)),e_.resize?window.removeEventListener("resize",s):null==o||o(),e_.clickOutsideAnchor&&window.removeEventListener("click",eW),e_.escape&&window.removeEventListener("keydown",d),v&&(null==(e=Z.current)||e.removeEventListener("mouseover",g),null==(t=Z.current)||t.removeEventListener("mouseout",y)),c.forEach(({event:e,listener:t})=>{r.forEach(r=>{var l;null==(l=r.current)||l.removeEventListener(e,t)})})}},[K,eU,en,ep,eg,S,C,A,ev,h,g]),(0,s.useEffect)(()=>{var e,r;let l=null!=(r=null!=(e=null==eo?void 0:eo.anchorSelect)?e:i)?r:"";!l&&t&&(l=`[data-tooltip-id='${t.replace(/'/g,"\\'")}']`);let s=new MutationObserver(e=>{let r=[],s=[];e.forEach(e=>{if("attributes"===e.type&&"data-tooltip-id"===e.attributeName&&(e.target.getAttribute("data-tooltip-id")===t?r.push(e.target):e.oldValue===t&&s.push(e.target)),"childList"===e.type){if(K){let t=[...e.removedNodes].filter(e=>1===e.nodeType);if(l)try{s.push(...t.filter(e=>e.matches(l))),s.push(...t.flatMap(e=>[...e.querySelectorAll(l)]))}catch(e){}t.some(e=>{var t;return!!(null==(t=null==e?void 0:e.contains)?void 0:t.call(e,K))&&(ea(!1),ek(!1),q(null),eD(X),eD(Y),!0)})}if(l)try{let t=[...e.addedNodes].filter(e=>1===e.nodeType);r.push(...t.filter(e=>e.matches(l))),r.push(...t.flatMap(e=>[...e.querySelectorAll(l)]))}catch(e){}}}),(r.length||s.length)&&ex(e=>[...e.filter(e=>!s.includes(e)),...r])});return s.observe(document.body,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["data-tooltip-id"],attributeOldValue:!0}),()=>{s.disconnect()}},[t,i,null==eo?void 0:eo.anchorSelect,K]),(0,s.useEffect)(()=>{eU()},[eU]),(0,s.useEffect)(()=>{if(!(null==D?void 0:D.current))return()=>null;let e=new ResizeObserver(()=>{setTimeout(()=>eU())});return e.observe(D.current),()=>{e.disconnect()}},[$,null==D?void 0:D.current]),(0,s.useEffect)(()=>{var e;let t=document.querySelector(`[id='${a}']`),r=[...eg,t];K&&r.includes(K)||q(null!=(e=eg[0])?e:t)},[a,eg,K]),(0,s.useEffect)(()=>(M&&ek(!0),()=>{eD(X),eD(Y)}),[]),(0,s.useEffect)(()=>{var e;let r=null!=(e=null==eo?void 0:eo.anchorSelect)?e:i;if(!r&&t&&(r=`[data-tooltip-id='${t.replace(/'/g,"\\'")}']`),r)try{let e=Array.from(document.querySelectorAll(r));ex(e)}catch(e){ex([])}},[t,i,null==eo?void 0:eo.anchorSelect]),(0,s.useEffect)(()=>{X.current&&(eD(X),eC(h))},[h]);let eZ=null!=(U=null==eo?void 0:eo.content)?U:$,eJ=er&&Object.keys(ee.tooltipStyles).length>0;return(0,s.useImperativeHandle)(e,()=>({open:e=>{if(null==e?void 0:e.anchorSelect)try{document.querySelector(e.anchorSelect)}catch(t){return void console.warn(`[react-tooltip] "${e.anchorSelect}" is not a valid CSS selector`)}ed(null!=e?e:null),(null==e?void 0:e.delay)?eC(e.delay):ek(!0)},close:e=>{(null==e?void 0:e.delay)?eR(e.delay):ek(!1)},activeAnchor:K,place:ee.place,isOpen:!!(en&&!y&&eZ&&eJ)})),en&&!y&&eZ?s.createElement(f,{id:t,role:V,className:eE("react-tooltip",eK.tooltip,eq.tooltip,eq[n],r,`react-tooltip__place-${ee.place}`,eK[eJ?"show":"closing"],eJ?"react-tooltip__show":"react-tooltip__closing","fixed"===m&&eK.fixed,b&&eK.clickable),onTransitionEnd:e=>{eD(Q),er||"opacity"!==e.propertyName||(ea(!1),ed(null),null==I||I())},style:{...T,...ee.tooltipStyles,opacity:void 0!==z&&eJ?z:void 0},ref:Z},eZ,s.createElement(f,{className:eE("react-tooltip-arrow",eK.arrow,eq.arrow,l,v&&eK.noArrow),style:{...ee.tooltipArrowStyles,background:G?`linear-gradient(to right bottom, transparent 50%, ${G} 50%)`:void 0},ref:J})):null},ez=({content:e})=>s.createElement("span",{dangerouslySetInnerHTML:{__html:e}}),eH=s.forwardRef(({id:e,anchorId:t,anchorSelect:r,content:l,html:n,render:a,className:i,classNameArrow:o,variant:d="dark",place:c="top",offset:u=10,wrapper:m="div",children:p=null,events:f=["hover"],openOnClick:h=!1,positionStrategy:g="absolute",middlewares:x,delayShow:y=0,delayHide:v=0,float:b=!1,hidden:w=!1,noArrow:j=!1,clickable:N=!1,closeOnEsc:_=!1,closeOnScroll:k=!1,closeOnResize:E=!1,openEvents:S,closeEvents:C,globalCloseEvents:A,imperativeModeOnly:R=!1,style:T,position:L,isOpen:O,defaultIsOpen:I=!1,disableStyleInjection:P=!1,border:$,opacity:D,arrowColor:F,setIsOpen:M,afterShow:B,afterHide:K,disableTooltip:q,role:W="tooltip"},z)=>{let[H,G]=(0,s.useState)(l),[V,U]=(0,s.useState)(n),[Z,J]=(0,s.useState)(c),[X,Y]=(0,s.useState)(d),[Q,ee]=(0,s.useState)(u),[et,er]=(0,s.useState)(y),[el,es]=(0,s.useState)(v),[en,ea]=(0,s.useState)(b),[ei,eo]=(0,s.useState)(w),[ed,ec]=(0,s.useState)(m),[eu,em]=(0,s.useState)(f),[ep,ef]=(0,s.useState)(g),[eh,eg]=(0,s.useState)(null),[ex,ey]=(0,s.useState)(null),ev=(0,s.useRef)(P),{anchorRefs:eb,activeAnchor:ew}=eB(e),ej=e=>null==e?void 0:e.getAttributeNames().reduce((t,r)=>{var l;return r.startsWith("data-tooltip-")&&(t[r.replace(/^data-tooltip-/,"")]=null!=(l=null==e?void 0:e.getAttribute(r))?l:null),t},{}),eN=e=>{let t={place:e=>{J(null!=e?e:c)},content:e=>{G(null!=e?e:l)},html:e=>{U(null!=e?e:n)},variant:e=>{Y(null!=e?e:d)},offset:e=>{ee(null===e?u:Number(e))},wrapper:e=>{ec(null!=e?e:m)},events:e=>{let t=null==e?void 0:e.split(" ");em(null!=t?t:f)},"position-strategy":e=>{ef(null!=e?e:g)},"delay-show":e=>{er(null===e?y:Number(e))},"delay-hide":e=>{es(null===e?v:Number(e))},float:e=>{ea(null===e?b:"true"===e)},hidden:e=>{eo(null===e?w:"true"===e)},"class-name":e=>{eg(e)}};Object.values(t).forEach(e=>e(null)),Object.entries(e).forEach(([e,r])=>{var l;null==(l=t[e])||l.call(t,r)})};(0,s.useEffect)(()=>{G(l)},[l]),(0,s.useEffect)(()=>{U(n)},[n]),(0,s.useEffect)(()=>{J(c)},[c]),(0,s.useEffect)(()=>{Y(d)},[d]),(0,s.useEffect)(()=>{ee(u)},[u]),(0,s.useEffect)(()=>{er(y)},[y]),(0,s.useEffect)(()=>{es(v)},[v]),(0,s.useEffect)(()=>{ea(b)},[b]),(0,s.useEffect)(()=>{eo(w)},[w]),(0,s.useEffect)(()=>{ef(g)},[g]),(0,s.useEffect)(()=>{ev.current!==P&&console.warn("[react-tooltip] Do not change `disableStyleInjection` dynamically.")},[P]),(0,s.useEffect)(()=>{"undefined"!=typeof window&&window.dispatchEvent(new CustomEvent("react-tooltip-inject-styles",{detail:{disableCore:"core"===P,disableBase:P}}))},[]),(0,s.useEffect)(()=>{var l;let s=new Set(eb),n=r;if(!n&&e&&(n=`[data-tooltip-id='${e.replace(/'/g,"\\'")}']`),n)try{document.querySelectorAll(n).forEach(e=>{s.add({current:e})})}catch(e){console.warn(`[react-tooltip] "${n}" is not a valid CSS selector`)}let a=document.querySelector(`[id='${t}']`);if(a&&s.add({current:a}),!s.size)return()=>null;let i=null!=(l=null!=ex?ex:a)?l:ew.current,o=new MutationObserver(e=>{e.forEach(e=>{var t;i&&"attributes"===e.type&&(null==(t=e.attributeName)?void 0:t.startsWith("data-tooltip-"))&&eN(ej(i))})});return i&&(eN(ej(i)),o.observe(i,{attributes:!0,childList:!1,subtree:!1})),()=>{o.disconnect()}},[eb,ew,ex,t,r]),(0,s.useEffect)(()=>{(null==T?void 0:T.border)&&console.warn("[react-tooltip] Do not set `style.border`. Use `border` prop instead."),$&&!eR("border",`${$}`)&&console.warn(`[react-tooltip] "${$}" is not a valid \`border\`.`),(null==T?void 0:T.opacity)&&console.warn("[react-tooltip] Do not set `style.opacity`. Use `opacity` prop instead."),D&&!eR("opacity",`${D}`)&&console.warn(`[react-tooltip] "${D}" is not a valid \`opacity\`.`)},[]);let e_=p,ek=(0,s.useRef)(null);if(a){let e=a({content:(null==ex?void 0:ex.getAttribute("data-tooltip-content"))||H||null,activeAnchor:ex});e_=e?s.createElement("div",{ref:ek,className:"react-tooltip-content-wrapper"},e):null}else H&&(e_=H);V&&(e_=s.createElement(ez,{content:V}));let eS={forwardRef:z,id:e,anchorId:t,anchorSelect:r,className:eE(i,eh),classNameArrow:o,content:e_,contentWrapperRef:ek,place:Z,variant:X,offset:Q,wrapper:ed,events:eu,openOnClick:h,positionStrategy:ep,middlewares:x,delayShow:et,delayHide:el,float:en,hidden:ei,noArrow:j,clickable:N,closeOnEsc:_,closeOnScroll:k,closeOnResize:E,openEvents:S,closeEvents:C,globalCloseEvents:A,imperativeModeOnly:R,style:T,position:L,isOpen:O,defaultIsOpen:I,border:$,opacity:D,arrowColor:F,setIsOpen:M,afterShow:B,afterHide:K,disableTooltip:q,activeAnchor:ex,setActiveAnchor:e=>ey(e),role:W};return s.createElement(eW,{...eS})});"undefined"!=typeof window&&window.addEventListener("react-tooltip-inject-styles",e=>{e.detail.disableCore||eC({css:":root{--rt-color-white:#fff;--rt-color-dark:#222;--rt-color-success:#8dc572;--rt-color-error:#be6464;--rt-color-warning:#f0ad4e;--rt-color-info:#337ab7;--rt-opacity:0.9;--rt-transition-show-delay:0.15s;--rt-transition-closing-delay:0.15s}.core-styles-module_tooltip__3vRRp{position:absolute;top:0;left:0;pointer-events:none;opacity:0;will-change:opacity}.core-styles-module_fixed__pcSol{position:fixed}.core-styles-module_arrow__cvMwQ{position:absolute;background:inherit}.core-styles-module_noArrow__xock6{display:none}.core-styles-module_clickable__ZuTTB{pointer-events:auto}.core-styles-module_show__Nt9eE{opacity:var(--rt-opacity);transition:opacity var(--rt-transition-show-delay)ease-out}.core-styles-module_closing__sGnxF{opacity:0;transition:opacity var(--rt-transition-closing-delay)ease-in}",type:"core"}),e.detail.disableBase||eC({css:`
.styles-module_tooltip__mnnfp{padding:8px 16px;border-radius:3px;font-size:90%;width:max-content}.styles-module_arrow__K0L3T{width:8px;height:8px}[class*='react-tooltip__place-top']>.styles-module_arrow__K0L3T{transform:rotate(45deg)}[class*='react-tooltip__place-right']>.styles-module_arrow__K0L3T{transform:rotate(135deg)}[class*='react-tooltip__place-bottom']>.styles-module_arrow__K0L3T{transform:rotate(225deg)}[class*='react-tooltip__place-left']>.styles-module_arrow__K0L3T{transform:rotate(315deg)}.styles-module_dark__xNqje{background:var(--rt-color-dark);color:var(--rt-color-white)}.styles-module_light__Z6W-X{background-color:var(--rt-color-white);color:var(--rt-color-dark)}.styles-module_success__A2AKt{background-color:var(--rt-color-success);color:var(--rt-color-white)}.styles-module_warning__SCK0X{background-color:var(--rt-color-warning);color:var(--rt-color-white)}.styles-module_error__JvumD{background-color:var(--rt-color-error);color:var(--rt-color-white)}.styles-module_info__BWdHW{background-color:var(--rt-color-info);color:var(--rt-color-white)}`,type:"base"})});var eG=r(50181),eV=r(20404),eU=r(5097);function eZ(){return(0,l.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,l.jsx)("div",{className:"flex items-center justify-between",children:(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"h-8 bg-gray-200 rounded w-48 mb-2 animate-pulse"}),(0,l.jsx)("div",{className:"h-4 bg-gray-200 rounded w-64 animate-pulse"})]})]})}),(0,l.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-6",children:[(0,l.jsx)("div",{className:"h-6 bg-gray-200 rounded w-32 mb-6 animate-pulse"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-2 animate-pulse"}),(0,l.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 mb-2 animate-pulse"}),(0,l.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-2 animate-pulse"}),(0,l.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"h-4 bg-gray-200 rounded w-12 mb-2 animate-pulse"}),(0,l.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,l.jsxs)("div",{className:"md:col-span-2",children:[(0,l.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,l.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse"})]})]}),(0,l.jsx)("div",{className:"mt-6",children:(0,l.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})})]}),(0,l.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-6",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,l.jsx)("div",{className:"h-6 bg-gray-200 rounded w-40 animate-pulse"}),(0,l.jsx)("div",{className:"h-8 bg-gray-200 rounded w-24 animate-pulse"})]}),(0,l.jsx)("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,l.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)("div",{className:"w-10 h-10 bg-gray-200 rounded-lg animate-pulse"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"h-5 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,l.jsx)("div",{className:"h-4 bg-gray-200 rounded w-48 animate-pulse"})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"}),(0,l.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16 animate-pulse"}),(0,l.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16 animate-pulse"})]})]}),(0,l.jsxs)("div",{className:"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"h-3 bg-gray-200 rounded w-16 mb-1 animate-pulse"}),(0,l.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 animate-pulse"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"h-3 bg-gray-200 rounded w-12 mb-1 animate-pulse"}),(0,l.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 animate-pulse"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"h-3 bg-gray-200 rounded w-20 mb-1 animate-pulse"}),(0,l.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 animate-pulse"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"h-3 bg-gray-200 rounded w-14 mb-1 animate-pulse"}),(0,l.jsx)("div",{className:"h-4 bg-gray-200 rounded w-18 animate-pulse"})]})]}),(0,l.jsxs)("div",{className:"mt-4",children:[(0,l.jsx)("div",{className:"h-4 bg-gray-200 rounded w-28 mb-2 animate-pulse"}),(0,l.jsx)("div",{className:"flex flex-wrap gap-2",children:[1,2,3].map(e=>(0,l.jsx)("div",{className:"h-6 bg-gray-200 rounded-full w-20 animate-pulse"},e))})]})]},e))})]}),(0,l.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-6",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,l.jsx)("div",{className:"h-6 bg-gray-200 rounded w-36 animate-pulse"}),(0,l.jsx)("div",{className:"h-8 bg-gray-200 rounded w-28 animate-pulse"})]}),(0,l.jsx)("div",{className:"space-y-3",children:[1,2].map(e=>(0,l.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 mb-1 animate-pulse"}),(0,l.jsx)("div",{className:"h-3 bg-gray-200 rounded w-48 animate-pulse"})]}),(0,l.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16 animate-pulse"})]},e))})]})]})}function eJ(){return(0,l.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"h-6 bg-gray-200 rounded w-40 mb-1 animate-pulse"}),(0,l.jsx)("div",{className:"h-4 bg-gray-200 rounded w-56 animate-pulse"})]})]}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[1,2,3,4].map(e=>(0,l.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-1 animate-pulse"}),(0,l.jsx)("div",{className:"h-3 bg-gray-200 rounded w-32 animate-pulse"})]})]})},e))})]})}var eX=r(60925),eY=r(36721);let eQ=a.MG.map(e=>({value:e.id,label:e.name}));function e0(){let e=(0,n.useParams)().configId,t=(0,eV.Z)(),r=(0,eY.bu)(),w=r?.navigateOptimistically||(e=>{window.location.href=e}),{getCachedData:j,isCached:N}=(0,eU._)(),{createHoverPrefetch:_}=(0,eX.c)(),[k,E]=(0,s.useState)(null),[S,C]=(0,s.useState)(!0),[A,R]=(0,s.useState)(!1),[T,L]=(0,s.useState)(eQ[0]?.value||"openai"),[O,I]=(0,s.useState)(""),[P,$]=(0,s.useState)(""),[D,F]=(0,s.useState)(""),[M,B]=(0,s.useState)(1),[K,q]=(0,s.useState)(!1),[W,z]=(0,s.useState)(null),[H,G]=(0,s.useState)(null),[V,U]=(0,s.useState)(null),[Z,J]=(0,s.useState)(!1),[X,Y]=(0,s.useState)(null),[Q,ee]=(0,s.useState)([]),[et,er]=(0,s.useState)(!0),[el,es]=(0,s.useState)(null),[en,ea]=(0,s.useState)(null),[ei,eo]=(0,s.useState)(null),[ed,ec]=(0,s.useState)(null),[eu,em]=(0,s.useState)(1),[ep,ef]=(0,s.useState)(""),[eh,eg]=(0,s.useState)(!1),[ex,ey]=(0,s.useState)([]),[ev,eb]=(0,s.useState)(!1),[ew,ej]=(0,s.useState)(null),[eN,e_]=(0,s.useState)(!1),[ek,eE]=(0,s.useState)(""),[eS,eC]=(0,s.useState)(""),[eA,eR]=(0,s.useState)(""),[eT,eL]=(0,s.useState)(!1),[eO,eI]=(0,s.useState)(null),[eP,e$]=(0,s.useState)(null);(0,s.useCallback)(async()=>{if(!e)return;let t=j(e);if(t&&t.configDetails){E(t.configDetails),C(!1);return}N(e)||R(!0),C(!0),z(null);try{let t=await fetch("/api/custom-configs");if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to fetch configurations list")}let r=(await t.json()).find(t=>t.id===e);if(!r)throw Error("Configuration not found in the list.");E(r)}catch(e){z(`Error loading model configuration: ${e.message}`),E(null)}finally{C(!1),R(!1)}},[e,j,N]),(0,s.useCallback)(async()=>{let t=j(e);if(t&&t.models){U(t.models),J(!1);return}J(!0),Y(null),U(null);try{let e=await fetch("/api/providers/list-models",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({})}),t=await e.json();if(!e.ok)throw Error(t.error||"Failed to fetch models from database.");t.models?U(t.models):U([])}catch(e){Y(`Error fetching models: ${e.message}`),U([])}finally{J(!1)}},[e,j]);let eD=(0,s.useCallback)(async()=>{let t=j(e);if(t&&t.userCustomRoles){ey(t.userCustomRoles),eb(!1);return}eb(!0),ej(null);try{let e=await fetch("/api/user/custom-roles");if(e.ok){let t=await e.json();ey(t)}else{let t;try{t=await e.json()}catch(r){t={error:await e.text().catch(()=>`HTTP error ${e.status}`)}}let r=t.error||(t.issues?JSON.stringify(t.issues):`Failed to fetch custom roles (status: ${e.status})`);if(401===e.status)ej(r);else throw Error(r);ey([])}}catch(e){ej(e.message),ey([])}finally{eb(!1)}},[]),eF=(0,s.useCallback)(async()=>{if(!e||!ex)return;let t=j(e);if(t&&t.apiKeys&&void 0!==t.defaultChatKeyId){let e=t.apiKeys.map(async e=>{let r=await fetch(`/api/keys/${e.id}/roles`),l=[];return r.ok&&(l=(await r.json()).map(e=>{let t=o(e.role_name);if(t)return t;let r=ex.find(t=>t.role_id===e.role_name);return r?{id:r.role_id,name:r.name,description:r.description||void 0}:null}).filter(Boolean)),{...e,assigned_roles:l,is_default_general_chat_model:t.defaultChatKeyId===e.id}});ee(await Promise.all(e)),ea(t.defaultChatKeyId),er(!1);return}er(!0),z(e=>e&&e.startsWith("Error loading model configuration:")?e:null),G(null);try{let t=await fetch(`/api/keys?custom_config_id=${e}`);if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to fetch API keys")}let r=await t.json(),l=await fetch(`/api/custom-configs/${e}/default-chat-key`);l.ok;let s=200===l.status?await l.json():null;ea(s?.id||null);let n=r.map(async e=>{let t=await fetch(`/api/keys/${e.id}/roles`),r=[];return t.ok&&(r=(await t.json()).map(e=>{let t=o(e.role_name);if(t)return t;let r=ex.find(t=>t.role_id===e.role_name);return r?{id:r.role_id,name:r.name,description:r.description||void 0}:null}).filter(Boolean)),{...e,assigned_roles:r,is_default_general_chat_model:s?.id===e.id}}),a=await Promise.all(n);ee(a)}catch(e){z(t=>t?`${t}; ${e.message}`:e.message)}finally{er(!1)}},[e,ex]),eM=(0,s.useMemo)(()=>{if(V){let e=a.MG.find(e=>e.id===T);if(!e)return[];if("openrouter"===e.id)return V.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""));if("deepseek"===e.id){let e=[];return V.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"}),V.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),e.sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return V.filter(t=>t.provider_id===e.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return[]},[V,T]),eB=(0,s.useMemo)(()=>{if(V&&ed){let e=a.MG.find(e=>e.id===ed.provider);if(!e)return[];if("openrouter"===e.id)return V.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""));if("deepseek"===e.id){let e=[];return V.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"}),V.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),e.sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return V.filter(t=>t.provider_id===e.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return[]},[V,ed]),eK=async t=>{if(t.preventDefault(),!e)return void z("Configuration ID is missing.");if(Q.some(e=>e.predefined_model_id===O))return void z("This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.");q(!0),z(null),G(null);let r=[...Q];try{let t=await fetch("/api/keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({custom_api_config_id:e,provider:T,predefined_model_id:O,api_key_raw:P,label:D,temperature:M})}),r=await t.json();if(!t.ok)throw Error(r.details||r.error||"Failed to save API key");let l={id:r.id,custom_api_config_id:e,provider:T,predefined_model_id:O,label:D,temperature:M,status:"active",created_at:new Date().toISOString(),last_used_at:null,is_default_general_chat_model:!1,assigned_roles:[]};ee(e=>[...e,l]),G(`API key "${D}" saved successfully!`),L(eQ[0]?.value||"openai"),$(""),F(""),B(1),eM.length>0&&I(eM[0].value)}catch(e){ee(r),z(`Save Key Error: ${e.message}`)}finally{q(!1)}},eq=e=>{ec(e),em(e.temperature||1),ef(e.predefined_model_id)},eW=async()=>{if(!ed)return;if(Q.some(e=>e.id!==ed.id&&e.predefined_model_id===ep))return void z("This model is already configured in this setup. Each model can only be used once per configuration.");eg(!0),z(null),G(null);let e=[...Q];ee(e=>e.map(e=>e.id===ed.id?{...e,temperature:eu,predefined_model_id:ep}:e));try{let t=await fetch(`/api/keys?id=${ed.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({temperature:eu,predefined_model_id:ep})}),r=await t.json();if(!t.ok)throw ee(e),Error(r.details||r.error||"Failed to update API key");G(`API key "${ed.label}" updated successfully!`),ec(null)}catch(e){z(`Update Key Error: ${e.message}`)}finally{eg(!1)}},ez=(e,r)=>{t.showConfirmation({title:"Delete API Key",message:`Are you sure you want to delete the API key "${r}"? This will permanently remove the key and unassign all its roles. This action cannot be undone.`,confirmText:"Delete API Key",cancelText:"Cancel",type:"danger"},async()=>{es(e),z(null),G(null);let t=[...Q],l=Q.find(t=>t.id===e);ee(t=>t.filter(t=>t.id!==e)),l?.is_default_general_chat_model&&ea(null);try{let l=await fetch(`/api/keys/${e}`,{method:"DELETE"}),s=await l.json();if(!l.ok){if(ee(t),ea(en),404===l.status){ee(t=>t.filter(t=>t.id!==e)),G(`API key "${r}" was already deleted.`);return}throw Error(s.details||s.error||"Failed to delete API key")}G(`API key "${r}" deleted successfully!`)}catch(e){throw z(`Delete Key Error: ${e.message}`),e}finally{es(null)}})},e0=async t=>{if(!e)return;z(null),G(null);let r=[...Q];ee(e=>e.map(e=>({...e,is_default_general_chat_model:e.id===t}))),ea(t);try{let l=await fetch(`/api/custom-configs/${e}/default-key-handler/${t}`,{method:"PUT"}),s=await l.json();if(!l.ok)throw ee(r.map(e=>({...e}))),ea(en),Error(s.details||s.error||"Failed to set default chat key");G(s.message||"Default general chat key updated!")}catch(e){z(`Set Default Error: ${e.message}`)}},e1=async(e,t,r)=>{z(null),G(null);let l=`/api/keys/${e.id}/roles`,s=[...i.map(e=>({...e,isCustom:!1})),...ex.map(e=>({id:e.role_id,name:e.name,description:e.description||void 0,isCustom:!0,databaseId:e.id}))].find(e=>e.id===t)||{id:t,name:t,description:""},n=Q.map(e=>({...e,assigned_roles:[...e.assigned_roles.map(e=>({...e}))]})),a=null;ei&&ei.id===e.id&&(a={...ei,assigned_roles:[...ei.assigned_roles.map(e=>({...e}))]}),ee(l=>l.map(l=>{if(l.id===e.id){let e=r?l.assigned_roles.filter(e=>e.id!==t):[...l.assigned_roles,s];return{...l,assigned_roles:e}}return l})),ei&&ei.id===e.id&&eo(e=>{if(!e)return null;let l=r?e.assigned_roles.filter(e=>e.id!==t):[...e.assigned_roles,s];return{...e,assigned_roles:l}});try{let i;i=r?await fetch(`${l}/${t}`,{method:"DELETE"}):await fetch(l,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({role_name:t})});let o=await i.json();if(!i.ok){if(ee(n),a)eo(a);else if(ei&&ei.id===e.id){let t=n.find(t=>t.id===e.id);t&&eo(t)}let t=409===i.status&&o.error?o.error:o.details||o.error||(r?"Failed to unassign role":"Failed to assign role");throw Error(t)}G(o.message||`Role '${s.name}' ${r?"unassigned":"assigned"} successfully.`)}catch(e){z(`Role Update Error: ${e.message}`)}},e2=async()=>{if(!ek.trim()||ek.trim().length>30||!/^[a-zA-Z0-9_]+$/.test(ek.trim()))return void eI("Role ID is required (max 30 chars, letters, numbers, underscores only).");if(i.some(e=>e.id.toLowerCase()===ek.trim().toLowerCase())||ex.some(e=>e.role_id.toLowerCase()===ek.trim().toLowerCase()))return void eI("This Role ID is already in use (either predefined or as one of your custom roles).");if(!eS.trim())return void eI("Role Name is required.");eI(null),eL(!0);try{let e=await fetch("/api/user/custom-roles",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({role_id:ek.trim(),name:eS.trim(),description:eA.trim()})});if(!e.ok){let t;try{t=await e.json()}catch(l){let r=await e.text().catch(()=>`HTTP status ${e.status}`);t={error:"Server error, could not parse response.",details:r}}let r=t.error||"Failed to create custom role.";if(t.details)r+=` (Details: ${t.details})`;else if(t.issues){let e=Object.entries(t.issues).map(([e,t])=>`${e}: ${t.join(", ")}`).join("; ");r+=` (Issues: ${e})`}throw Error(r)}let t=await e.json();eE(""),eC(""),eR(""),eD(),G(`Custom role '${t.name}' created successfully! It is now available globally.`)}catch(e){eI(e.message)}finally{eL(!1)}},e5=(r,l)=>{r&&t.showConfirmation({title:"Delete Custom Role",message:`Are you sure you want to delete the custom role "${l}"? This will unassign it from all API keys where it's currently used. This action cannot be undone.`,confirmText:"Delete Role",cancelText:"Cancel",type:"danger"},async()=>{e$(r),ej(null),eI(null),G(null);try{let t=await fetch(`/api/user/custom-roles/${r}`,{method:"DELETE"}),s=await t.json();if(!t.ok)throw Error(s.error||"Failed to delete custom role");ey(e=>e.filter(e=>e.id!==r)),G(s.message||`Global custom role "${l}" deleted successfully.`),e&&eF()}catch(e){throw ej(`Error deleting role: ${e.message}`),e}finally{e$(null)}})};return A&&!N(e)?(0,l.jsx)(eZ,{}):S&&!k?(0,l.jsx)(eJ,{}):(0,l.jsxs)("div",{className:"min-h-screen",children:[(0,l.jsxs)("div",{className:"mb-8",children:[(0,l.jsxs)("button",{onClick:()=>w("/my-models"),className:"text-orange-600 hover:text-orange-700 inline-flex items-center mb-6 transition-colors duration-200 group",children:[(0,l.jsx)(p.A,{className:"h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform"}),"Back to My API Models"]}),(0,l.jsx)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-6",children:(0,l.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6",children:[(0,l.jsx)("div",{className:"flex-1",children:k?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:"flex items-center mb-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg",children:(0,l.jsx)(u.A,{className:"h-6 w-6 text-white"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:k.name}),(0,l.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Model Configuration"})]})]}),(0,l.jsxs)("div",{className:"flex items-center text-sm text-gray-600 bg-gray-50 px-4 py-2 rounded-xl w-fit",children:[(0,l.jsx)("span",{className:"inline-block w-2 h-2 bg-orange-500 rounded-full mr-2"}),"ID: ",k.id]})]}):W&&!S?(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-red-100 rounded-2xl flex items-center justify-center mr-4",children:(0,l.jsx)(d,{className:"h-6 w-6 text-red-600"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-red-600",children:"Configuration Error"}),(0,l.jsx)("p",{className:"text-red-500 mt-1",children:W.replace("Error loading model configuration: ","")})]})]}):(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-2xl flex items-center justify-center mr-4",children:(0,l.jsx)(f,{className:"h-6 w-6 text-gray-400 animate-pulse"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Loading Configuration..."}),(0,l.jsx)("p",{className:"text-gray-500 mt-1",children:"Please wait while we fetch your model details"})]})]})}),k&&(0,l.jsxs)("button",{onClick:()=>w(`/routing-setup/${e}?from=model-config`),className:"inline-flex items-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-sm text-white bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 group",..._(e),children:[(0,l.jsx)(u.A,{className:"h-5 w-5 mr-2 group-hover:rotate-90 transition-transform duration-200"}),"Advanced Routing Setup"]})]})}),H&&(0,l.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-2xl p-4 mb-6 animate-slide-in",children:(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)(h.A,{className:"h-5 w-5 text-green-600"}),(0,l.jsx)("p",{className:"text-green-800 font-medium",children:H})]})}),W&&(0,l.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-2xl p-4 mb-6 animate-slide-in",children:(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)(d,{className:"h-5 w-5 text-red-600"}),(0,l.jsx)("p",{className:"text-red-800 font-medium",children:W})]})})]}),k&&(0,l.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-5 gap-8",children:[(0,l.jsx)("div",{className:"xl:col-span-2",children:(0,l.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 p-6 sticky top-8",children:[(0,l.jsxs)("div",{className:"flex items-center mb-6",children:[(0,l.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mr-3 shadow-md",children:(0,l.jsx)(g.A,{className:"h-5 w-5 text-white"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Add API Key"}),(0,l.jsx)("p",{className:"text-xs text-gray-500",children:"Configure new key"})]})]}),(0,l.jsxs)("form",{onSubmit:eK,className:"space-y-5",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"provider",className:"block text-sm font-medium text-gray-700 mb-2",children:"Provider"}),(0,l.jsx)("select",{id:"provider",value:T,onChange:e=>{L(e.target.value)},className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm",children:eQ.map(e=>(0,l.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"apiKeyRaw",className:"block text-sm font-medium text-gray-700 mb-2",children:"API Key"}),(0,l.jsx)("input",{id:"apiKeyRaw",type:"password",value:P,onChange:e=>$(e.target.value),className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm",placeholder:"Enter your API key"}),Z&&null===V&&(0,l.jsxs)("p",{className:"mt-2 text-xs text-orange-600 flex items-center bg-orange-50 p-2 rounded-lg",children:[(0,l.jsx)(f,{className:"h-4 w-4 mr-1 animate-pulse"}),"Fetching models..."]}),X&&(0,l.jsx)("p",{className:"mt-2 text-xs text-red-600 bg-red-50 p-2 rounded-lg",children:X})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"predefinedModelId",className:"block text-sm font-medium text-gray-700 mb-2",children:"Model Variant"}),(0,l.jsx)("select",{id:"predefinedModelId",value:O,onChange:e=>I(e.target.value),disabled:!eM.length,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm disabled:bg-gray-50 disabled:text-gray-500",children:eM.length>0?eM.map(e=>(0,l.jsx)("option",{value:e.value,children:e.label},e.value)):(0,l.jsx)("option",{value:"",disabled:!0,children:null===V&&Z?"Loading models...":"Select a provider first"})})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"label",className:"block text-sm font-medium text-gray-700 mb-2",children:"Label"}),(0,l.jsx)("input",{type:"text",id:"label",value:D,onChange:e=>F(e.target.value),required:!0,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm",placeholder:"e.g., My OpenAI GPT-4o Key #1"})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{htmlFor:"temperature",className:"block text-sm font-medium text-gray-700 mb-2",children:["Temperature",(0,l.jsx)("span",{className:"text-xs text-gray-500 ml-1",children:"(0.0 - 2.0)"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("input",{type:"range",id:"temperature",min:"0",max:"2",step:"0.1",value:M,onChange:e=>B(parseFloat(e.target.value)),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("span",{className:"text-xs text-gray-500",children:"Conservative"}),(0,l.jsx)("div",{className:"flex items-center space-x-2",children:(0,l.jsx)("input",{type:"number",min:"0",max:"2",step:"0.1",value:M,onChange:e=>B(Math.min(2,Math.max(0,parseFloat(e.target.value)||0))),className:"w-16 px-2 py-1 text-xs border border-gray-200 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center"})}),(0,l.jsx)("span",{className:"text-xs text-gray-500",children:"Creative"})]}),(0,l.jsx)("p",{className:"text-xs text-gray-500",children:"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative"})]})]})]}),(0,l.jsx)("button",{type:"submit",disabled:K||!O||""===O||!P.trim()||!D.trim(),className:"w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none text-sm",children:K?(0,l.jsxs)("span",{className:"flex items-center justify-center",children:[(0,l.jsx)(f,{className:"h-4 w-4 mr-2 animate-pulse"}),"Saving..."]}):(0,l.jsxs)("span",{className:"flex items-center justify-center",children:[(0,l.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Add API Key"]})})]}),(0,l.jsx)("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-xl",children:(0,l.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,l.jsx)(x,{className:"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"text-sm font-medium text-blue-900 mb-1",children:"Key Configuration Rules"}),(0,l.jsxs)("div",{className:"text-xs text-blue-800 space-y-1",children:[(0,l.jsxs)("p",{children:["✅ ",(0,l.jsx)("strong",{children:"Same API key, different models:"})," Allowed"]}),(0,l.jsxs)("p",{children:["✅ ",(0,l.jsx)("strong",{children:"Different API keys, same model:"})," Allowed"]}),(0,l.jsxs)("p",{children:["❌ ",(0,l.jsx)("strong",{children:"Same model twice:"})," Not allowed in one configuration"]})]})]})]})})]})}),(0,l.jsx)("div",{className:"xl:col-span-3",children:(0,l.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 p-6",children:[(0,l.jsxs)("div",{className:"flex items-center mb-6",children:[(0,l.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3 shadow-md",children:(0,l.jsx)(y.A,{className:"h-5 w-5 text-white"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"API Keys & Roles"}),(0,l.jsx)("p",{className:"text-xs text-gray-500",children:"Manage existing keys"})]})]}),et&&(0,l.jsxs)("div",{className:"text-center py-8",children:[(0,l.jsx)(f,{className:"h-8 w-8 text-gray-400 mx-auto mb-3 animate-pulse"}),(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:"Loading API keys..."})]}),!et&&0===Q.length&&(!W||W&&W.startsWith("Error loading model configuration:"))&&(0,l.jsxs)("div",{className:"text-center py-8",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,l.jsx)(y.A,{className:"h-6 w-6 text-gray-400"})}),(0,l.jsx)("h3",{className:"text-sm font-semibold text-gray-900 mb-1",children:"No API Keys"}),(0,l.jsx)("p",{className:"text-xs text-gray-500",children:"Add your first key using the form"})]}),!et&&Q.length>0&&(0,l.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:Q.map((e,t)=>(0,l.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all duration-200 animate-slide-in",style:{animationDelay:`${50*t}ms`},children:(0,l.jsxs)("div",{className:"flex items-start justify-between",children:[(0,l.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,l.jsxs)("div",{className:"flex items-center mb-2",children:[(0,l.jsx)("h3",{className:"text-sm font-semibold text-gray-900 truncate mr-2",children:e.label}),e.is_default_general_chat_model&&(0,l.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 flex-shrink-0",children:[(0,l.jsx)(v.A,{className:"h-3 w-3 mr-1"}),"Default"]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsxs)("p",{className:"text-xs text-gray-900 bg-white px-2 py-1 rounded-lg border",children:[e.provider," (",e.predefined_model_id,")"]}),(0,l.jsxs)("p",{className:"text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded-lg border border-orange-200",children:["Temp: ",e.temperature]})]}),(0,l.jsx)("div",{className:"flex flex-wrap gap-1",children:e.assigned_roles.length>0?e.assigned_roles.map(e=>(0,l.jsx)("span",{className:"inline-block whitespace-nowrap rounded-full bg-orange-100 px-2 py-1 text-xs font-medium text-orange-800",children:e.name},e.id)):(0,l.jsx)("span",{className:"text-xs text-gray-500 bg-white px-2 py-1 rounded-lg border",children:"No roles"})})]}),!e.is_default_general_chat_model&&(0,l.jsxs)("button",{onClick:()=>e0(e.id),className:"text-xs bg-white border border-gray-200 hover:border-gray-300 text-gray-700 hover:text-gray-900 py-1 px-2 rounded-lg mt-2 transition-colors","data-tooltip-id":`tooltip-set-default-${e.id}`,"data-tooltip-content":"Set as default chat model",children:["Set Default",(0,l.jsx)(eH,{id:`tooltip-set-default-${e.id}`,place:"top"})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-1 ml-2 flex-shrink-0",children:[(0,l.jsxs)("button",{onClick:()=>eq(e),disabled:el===e.id,className:"p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":`tooltip-edit-${e.id}`,"data-tooltip-content":"Edit Model & Settings",children:[(0,l.jsx)(b.A,{className:"h-4 w-4"}),(0,l.jsx)(eH,{id:`tooltip-edit-${e.id}`,place:"top"})]}),(0,l.jsxs)("button",{onClick:()=>eo(e),disabled:el===e.id,className:"p-2 text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":`tooltip-roles-${e.id}`,"data-tooltip-content":"Manage Roles",children:[(0,l.jsx)(u.A,{className:"h-4 w-4"}),(0,l.jsx)(eH,{id:`tooltip-roles-${e.id}`,place:"top"})]}),(0,l.jsxs)("button",{onClick:()=>ez(e.id,e.label),disabled:el===e.id,className:"p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":`tooltip-delete-${e.id}`,"data-tooltip-content":"Delete Key",children:[el===e.id?(0,l.jsx)(m.A,{className:"h-4 w-4 animate-pulse"}):(0,l.jsx)(m.A,{className:"h-4 w-4"}),(0,l.jsx)(eH,{id:`tooltip-delete-${e.id}`,place:"top"})]})]})]})},e.id))}),!et&&W&&!W.startsWith("Error loading model configuration:")&&(0,l.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4",children:(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)(d,{className:"h-5 w-5 text-red-600"}),(0,l.jsxs)("p",{className:"text-red-800 font-medium text-sm",children:["Could not load API keys/roles: ",W]})]})})]})})]}),ei&&(()=>{if(!ei)return null;let e=[...i.map(e=>({...e,isCustom:!1})),...ex.map(e=>({id:e.role_id,name:e.name,description:e.description||void 0,isCustom:!0,databaseId:e.id}))].sort((e,t)=>e.name.localeCompare(t.name));return(0,l.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,l.jsxs)("div",{className:"card w-full max-w-lg max-h-[90vh] flex flex-col",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-200",children:[(0,l.jsxs)("h2",{className:"text-xl font-semibold text-gray-900",children:["Manage Roles for: ",(0,l.jsx)("span",{className:"text-orange-600",children:ei.label})]}),(0,l.jsx)("button",{onClick:()=>{eo(null),e_(!1),eI(null)},className:"text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200",children:(0,l.jsx)(d,{className:"h-6 w-6"})})]}),(0,l.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[ew&&(0,l.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:(0,l.jsxs)("p",{className:"text-red-800 text-sm",children:["Error with custom roles: ",ew]})}),(0,l.jsx)("div",{className:"flex justify-end mb-4",children:(0,l.jsxs)("button",{onClick:()=>e_(!eN),className:"btn-primary text-sm inline-flex items-center",children:[(0,l.jsx)(c.A,{className:"h-4 w-4 mr-2"}),eN?"Cancel New Role":"Create New Custom Role"]})}),eN&&(0,l.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4",children:[(0,l.jsx)("h3",{className:"text-md font-medium text-gray-900 mb-3",children:"Create New Custom Role for this Configuration"}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"newCustomRoleId",className:"block text-sm font-medium text-gray-700 mb-1",children:"Role ID (short, no spaces, max 30 chars)"}),(0,l.jsx)("input",{type:"text",id:"newCustomRoleId",value:ek,onChange:e=>eE(e.target.value.replace(/\s/g,"")),className:"form-input",maxLength:30,placeholder:"e.g., my_blog_writer"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"newCustomRoleName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Display Name (max 100 chars)"}),(0,l.jsx)("input",{type:"text",id:"newCustomRoleName",value:eS,onChange:e=>eC(e.target.value),className:"form-input",maxLength:100,placeholder:"e.g., My Awesome Blog Writer"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"newCustomRoleDescription",className:"block text-sm font-medium text-gray-700 mb-1",children:"Description (optional, max 500 chars)"}),(0,l.jsx)("textarea",{id:"newCustomRoleDescription",value:eA,onChange:e=>eR(e.target.value),rows:2,className:"form-input",maxLength:500,placeholder:"Optional: Describe what this role is for..."})]}),eO&&(0,l.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,l.jsx)("p",{className:"text-red-800 text-sm",children:eO})}),(0,l.jsx)("button",{onClick:e2,disabled:eT,className:"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed",children:eT?"Saving Role...":"Save Custom Role"})]})]})]}),(0,l.jsxs)("div",{className:"p-6",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-700 mb-3",children:"Select roles to assign:"}),(0,l.jsxs)("div",{className:"overflow-y-auto space-y-2",style:{maxHeight:"calc(90vh - 350px)"},children:[ev&&(0,l.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600"}),(0,l.jsx)("p",{className:"text-gray-600 text-sm ml-2",children:"Loading custom roles..."})]}),e.map(e=>{let t=ei.assigned_roles.some(t=>t.id===e.id);return(0,l.jsxs)("div",{className:`flex items-center justify-between p-3 rounded-lg border transition-all duration-200 ${t?"bg-orange-50 border-orange-200 shadow-sm":"bg-white border-gray-200 hover:border-gray-300 hover:shadow-sm"}`,children:[(0,l.jsxs)("label",{htmlFor:`role-${e.id}`,className:"flex items-center cursor-pointer flex-grow",children:[(0,l.jsx)("input",{type:"checkbox",id:`role-${e.id}`,checked:t,onChange:()=>e1(ei,e.id,t),className:"h-4 w-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500 focus:ring-2 cursor-pointer"}),(0,l.jsx)("span",{className:`ml-3 text-sm font-medium ${t?"text-orange-800":"text-gray-900"}`,children:e.name}),e.isCustom&&(0,l.jsx)("span",{className:"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800",children:"Custom"})]}),e.isCustom&&e.databaseId&&(0,l.jsx)("button",{onClick:()=>e5(e.databaseId,e.name),disabled:eP===e.databaseId,className:"p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-wait ml-2",title:"Delete this custom role",children:eP===e.databaseId?(0,l.jsx)(u.A,{className:"h-4 w-4 animate-spin"}):(0,l.jsx)(m.A,{className:"h-4 w-4"})})]},e.id)})]})]}),(0,l.jsx)("div",{className:"p-6 border-t border-gray-200 bg-gray-50 rounded-b-xl",children:(0,l.jsx)("div",{className:"flex justify-end",children:(0,l.jsx)("button",{onClick:()=>{eo(null),e_(!1),eI(null)},className:"btn-secondary",children:"Done"})})})]})})})(),ed&&(0,l.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,l.jsxs)("div",{className:"card w-full max-w-lg",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-200",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Edit API Key"}),(0,l.jsx)("button",{onClick:()=>ec(null),className:"text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200",children:(0,l.jsx)(d,{className:"h-6 w-6"})})]}),(0,l.jsxs)("div",{className:"p-6",children:[(0,l.jsxs)("div",{className:"mb-4",children:[(0,l.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:ed.label}),(0,l.jsxs)("p",{className:"text-sm text-gray-600",children:["Current: ",ed.provider," (",ed.predefined_model_id,")"]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Provider"}),(0,l.jsx)("div",{className:"w-full p-2.5 bg-gray-50 border border-gray-300 rounded-md text-gray-700",children:a.MG.find(e=>e.id===ed.provider)?.name||ed.provider}),(0,l.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Provider cannot be changed"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"editModelId",className:"block text-sm font-medium text-gray-700 mb-2",children:"Model"}),(0,l.jsx)("select",{id:"editModelId",value:ep,onChange:e=>ef(e.target.value),disabled:!eB.length,className:"w-full p-2.5 bg-white border border-gray-300 rounded-md text-gray-900 focus:ring-orange-500 focus:border-orange-500 disabled:opacity-50 disabled:bg-gray-100",children:eB.length>0?eB.map(e=>(0,l.jsx)("option",{value:e.value,children:e.label},e.value)):(0,l.jsx)("option",{value:"",disabled:!0,children:Z?"Loading models...":"No models available"})})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{htmlFor:"editTemperature",className:"block text-sm font-medium text-gray-700 mb-2",children:["Temperature: ",eu]}),(0,l.jsx)("input",{type:"range",id:"editTemperature",min:"0",max:"2",step:"0.1",value:eu,onChange:e=>em(parseFloat(e.target.value)),className:"slider-orange w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"}),(0,l.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[(0,l.jsx)("span",{children:"0.0 (Focused)"}),(0,l.jsx)("span",{children:"1.0 (Balanced)"}),(0,l.jsx)("span",{children:"2.0 (Creative)"})]})]}),(0,l.jsx)("div",{className:"bg-gray-50 rounded-lg p-3",children:(0,l.jsx)("p",{className:"text-xs text-gray-600",children:"You can change the model and temperature settings. Temperature controls randomness in responses. Lower values (0.0-0.3) are more focused and deterministic, while higher values (1.5-2.0) are more creative and varied."})})]})]}),(0,l.jsx)("div",{className:"p-6 border-t border-gray-200 bg-gray-50 rounded-b-xl",children:(0,l.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,l.jsx)("button",{onClick:()=>ec(null),className:"btn-secondary",disabled:eh,children:"Cancel"}),(0,l.jsx)("button",{onClick:eW,disabled:eh,className:"btn-primary",children:eh?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Saving..."]}):"Save Changes"})]})})]})}),!k&&!S&&!W&&(0,l.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 text-center py-16 px-8",children:[(0,l.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6",children:(0,l.jsx)(x,{className:"h-8 w-8 text-gray-400"})}),(0,l.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Model Not Found"}),(0,l.jsx)("p",{className:"text-sm text-gray-600 mb-8",children:"This API Model configuration could not be found or may have been deleted."}),(0,l.jsxs)("button",{onClick:()=>w("/my-models"),className:"inline-flex items-center px-6 py-3 text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5",children:[(0,l.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Return to My API Models"]})]}),(0,l.jsx)(eG.A,{isOpen:t.isOpen,onClose:t.hideConfirmation,onConfirm:t.onConfirm,title:t.title,message:t.message,confirmText:t.confirmText,cancelText:t.cancelText,type:t.type,isLoading:t.isLoading}),(0,l.jsx)(eH,{id:"global-tooltip"})]})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},86099:(e,t,r)=>{Promise.resolve().then(r.bind(r,20218))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),l=t.X(0,[7719,221,1658,7437,1562],()=>r(51983));module.exports=l})();