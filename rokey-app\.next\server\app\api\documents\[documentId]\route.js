(()=>{var e={};e.id=9727,e.ids=[1489,9727],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{Q:()=>u,createSupabaseServerClientOnRequest:()=>o});var s=r(34386),i=r(44999);async function o(){let e=await (0,i.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}function u(e){return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77796:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{DELETE:()=>c,GET:()=>d});var i=r(96559),o=r(48088),u=r(37719),n=r(32190),a=r(2507);async function c(e,{params:t}){try{let e=await (0,a.createSupabaseServerClientOnRequest)(),{data:{user:s},error:i}=await e.auth.getUser();if(i||!s)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{documentId:o}=await t;if(!o)return n.NextResponse.json({error:"Document ID is required"},{status:400});let{data:u,error:c}=await e.from("documents").select("id, custom_api_config_id").eq("id",o).eq("user_id",s.id).single();if(c||!u)return n.NextResponse.json({error:"Document not found or access denied"},{status:404});let{error:d}=await e.from("document_chunks").delete().eq("document_id",o).eq("user_id",s.id);if(d)return n.NextResponse.json({error:"Failed to delete document chunks",details:d.message},{status:500});let{error:p}=await e.from("documents").delete().eq("id",o).eq("user_id",s.id);if(p)return n.NextResponse.json({error:"Failed to delete document",details:p.message},{status:500});try{let{trainingDataCache:e}=await r.e(2842).then(r.bind(r,2842));e.invalidate(u.custom_api_config_id)}catch(e){}return n.NextResponse.json({success:!0,message:"Document deleted successfully"})}catch(e){return n.NextResponse.json({error:"Failed to delete document",details:e.message},{status:500})}}async function d(e,{params:t}){try{let e=await (0,a.createSupabaseServerClientOnRequest)(),{data:{user:r},error:s}=await e.auth.getUser();if(s||!r)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{documentId:i}=await t;if(!i)return n.NextResponse.json({error:"Document ID is required"},{status:400});let{data:o,error:u}=await e.from("documents").select(`
        id,
        filename,
        file_type,
        file_size,
        status,
        metadata,
        created_at,
        updated_at
      `).eq("id",i).eq("user_id",r.id).single();if(u||!o)return n.NextResponse.json({error:"Document not found or access denied"},{status:404});let{count:c}=await e.from("document_chunks").select("*",{count:"exact",head:!0}).eq("document_id",i);return n.NextResponse.json({success:!0,document:{...o,chunks_count:c||0}})}catch(e){return n.NextResponse.json({error:"Failed to fetch document",details:e.message},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/documents/[documentId]/route",pathname:"/api/documents/[documentId]",filename:"route",bundlePath:"app/api/documents/[documentId]/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\documents\\[documentId]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:h}=p;function x(){return(0,u.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,580,9398,3410],()=>r(77796));module.exports=s})();