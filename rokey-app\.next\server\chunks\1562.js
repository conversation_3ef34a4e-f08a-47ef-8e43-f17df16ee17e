exports.id=1562,exports.ids=[1562],exports.modules={5097:(e,t,o)=>{"use strict";o.d(t,{_:()=>s});var n=o(43210);let r={};function s(){let[e,t]=(0,n.useState)({}),o=(0,n.useRef)({}),s=(0,n.useCallback)(e=>{let t=r[e];return!!t&&!(Date.now()-t.timestamp>3e5)&&!t.isLoading},[]),l=(0,n.useCallback)(e=>{let t=r[e];return!t||t.isLoading?null:Date.now()-t.timestamp>3e5?(delete r[e],null):t.data},[]),i=(0,n.useCallback)(async(e,n="medium")=>{if(s(e))return l(e);if(r[e]?.isLoading)return null;o.current[e]&&o.current[e].abort();let i=new AbortController;o.current[e]=i,r[e]={data:{},timestamp:Date.now(),isLoading:!0},t(t=>({...t,[e]:"loading"}));try{"low"===n?await new Promise(e=>setTimeout(e,200)):"medium"===n&&await new Promise(e=>setTimeout(e,50));let[o,s,l,a,c]=await Promise.allSettled([fetch("/api/custom-configs",{signal:i.signal}),fetch(`/api/keys?custom_config_id=${e}`,{signal:i.signal}),fetch("/api/user/custom-roles",{signal:i.signal}),fetch("/api/providers/list-models",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({}),signal:i.signal}),fetch(`/api/custom-configs/${e}/default-chat-key`,{signal:i.signal})]),d=null,u=[],p=[],m=[],f=null;if("fulfilled"===o.status&&o.value.ok&&(d=(await o.value.json()).find(t=>t.id===e)),"fulfilled"===s.status&&s.value.ok&&(u=await s.value.json()),"fulfilled"===l.status&&l.value.ok&&(p=await l.value.json()),"fulfilled"===a.status&&a.value.ok&&(m=(await a.value.json()).models||[]),"fulfilled"===c.status&&c.value.ok){let e=await c.value.json();f=e?.id||null}let g={configDetails:d,apiKeys:u,userCustomRoles:p,models:m,defaultChatKeyId:f};return r[e]={data:g,timestamp:Date.now(),isLoading:!1},t(t=>({...t,[e]:"success"})),g}catch(o){if("AbortError"===o.name)return null;return delete r[e],t(t=>({...t,[e]:"error"})),null}finally{delete o.current[e]}},[s,l]),a=(0,n.useCallback)(e=>({onMouseEnter:()=>{s(e)||i(e,"high")}}),[i,s]),c=(0,n.useCallback)(e=>{delete r[e],t(t=>{let o={...t};return delete o[e],o})},[]),d=(0,n.useCallback)(()=>{Object.keys(r).forEach(e=>{delete r[e]}),t({})},[]);return{prefetchManageKeysData:i,getCachedData:l,isCached:s,createHoverPrefetch:a,clearCache:c,clearAllCache:d,getStatus:(0,n.useCallback)(t=>e[t]||"idle",[e]),getCacheInfo:(0,n.useCallback)(()=>({cachedConfigs:Object.keys(r),cacheSize:Object.keys(r).length,totalCacheAge:Object.values(r).reduce((e,t)=>e+(Date.now()-t.timestamp),0)/Object.keys(r).length}),[]),prefetchStatus:e}}},8345:(e,t,o)=>{Promise.resolve().then(o.bind(o,47417))},20404:(e,t,o)=>{"use strict";o.d(t,{Z:()=>r});var n=o(43210);function r(){let[e,t]=(0,n.useState)({isOpen:!1,isLoading:!1,title:"",message:"",confirmText:"Confirm",cancelText:"Cancel",type:"danger",onConfirm:()=>{}}),o=(0,n.useCallback)((e,o)=>{t({isOpen:!0,isLoading:!1,title:e.title,message:e.message,confirmText:e.confirmText||"Confirm",cancelText:e.cancelText||"Cancel",type:e.type||"danger",onConfirm:async()=>{t(e=>({...e,isLoading:!0}));try{await o(),t(e=>({...e,isOpen:!1,isLoading:!1}))}catch(e){throw t(e=>({...e,isLoading:!1})),e}}})},[]),r=(0,n.useCallback)(()=>{t(e=>({...e,isOpen:!1,isLoading:!1}))},[]);return{...e,showConfirmation:o,hideConfirmation:r}}},26403:(e,t,o)=>{"use strict";o.d(t,{A:()=>r});var n=o(43210);let r=n.forwardRef(function({title:e,titleId:t,...o},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},o),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))})},44793:(e,t,o)=>{Promise.resolve().then(o.bind(o,35291))},47417:(e,t,o)=>{"use strict";o.d(t,{AnalyticsSkeleton:()=>a,ConfigSelectorSkeleton:()=>s,MessageSkeleton:()=>r,MyModelsSkeleton:()=>l,RoutingSetupSkeleton:()=>i});var n=o(12907);(0,n.registerClientReference)(function(){throw Error("Attempted to call LoadingSkeleton() from the server but LoadingSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","LoadingSkeleton"),(0,n.registerClientReference)(function(){throw Error("Attempted to call ChatHistorySkeleton() from the server but ChatHistorySkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","ChatHistorySkeleton"),(0,n.registerClientReference)(function(){throw Error("Attempted to call EnhancedChatHistorySkeleton() from the server but EnhancedChatHistorySkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","EnhancedChatHistorySkeleton");let r=(0,n.registerClientReference)(function(){throw Error("Attempted to call MessageSkeleton() from the server but MessageSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","MessageSkeleton"),s=(0,n.registerClientReference)(function(){throw Error("Attempted to call ConfigSelectorSkeleton() from the server but ConfigSelectorSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","ConfigSelectorSkeleton");(0,n.registerClientReference)(function(){throw Error("Attempted to call DashboardSkeleton() from the server but DashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","DashboardSkeleton");let l=(0,n.registerClientReference)(function(){throw Error("Attempted to call MyModelsSkeleton() from the server but MyModelsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","MyModelsSkeleton"),i=(0,n.registerClientReference)(function(){throw Error("Attempted to call RoutingSetupSkeleton() from the server but RoutingSetupSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","RoutingSetupSkeleton");(0,n.registerClientReference)(function(){throw Error("Attempted to call TrainingSkeleton() from the server but TrainingSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","TrainingSkeleton");let a=(0,n.registerClientReference)(function(){throw Error("Attempted to call AnalyticsSkeleton() from the server but AnalyticsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","AnalyticsSkeleton");(0,n.registerClientReference)(function(){throw Error("Attempted to call PlaygroundSkeleton() from the server but PlaygroundSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","PlaygroundSkeleton"),(0,n.registerClientReference)(function(){throw Error("Attempted to call LogsSkeleton() from the server but LogsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","LogsSkeleton"),(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LoadingSkeleton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","default")},50181:(e,t,o)=>{"use strict";o.d(t,{A:()=>i});var n=o(60687);o(43210);var r=o(26403),s=o(59168),l=o(81836);function i({isOpen:e,onClose:t,onConfirm:o,title:i,message:a,confirmText:c="Delete",cancelText:d="Cancel",type:u="danger",isLoading:p=!1}){let m=(()=>{switch(u){case"danger":return{iconBg:"bg-red-100",iconColor:"text-red-600",confirmButton:"bg-red-600 hover:bg-red-700 focus:ring-red-500",icon:r.A};case"warning":return{iconBg:"bg-yellow-100",iconColor:"text-yellow-600",confirmButton:"bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500",icon:s.A};default:return{iconBg:"bg-blue-100",iconColor:"text-blue-600",confirmButton:"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500",icon:s.A}}})(),f=m.icon;return e?(0,n.jsxs)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[(0,n.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300",onClick:p?void 0:t}),(0,n.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,n.jsxs)("div",{className:"relative w-full max-w-md transform overflow-hidden rounded-2xl bg-white shadow-2xl transition-all duration-300 scale-100 opacity-100",children:[(0,n.jsx)("div",{className:"relative px-6 pt-6",children:(0,n.jsx)("button",{onClick:t,disabled:p,className:"absolute top-4 right-4 p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,n.jsx)(l.A,{className:"h-5 w-5"})})}),(0,n.jsxs)("div",{className:"px-6 pb-6",children:[(0,n.jsx)("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-full mb-6",children:(0,n.jsx)("div",{className:`${m.iconBg} rounded-full p-3`,children:(0,n.jsx)(f,{className:`h-8 w-8 ${m.iconColor}`})})}),(0,n.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3 text-center",children:i}),(0,n.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed mb-8 text-center",children:a}),(0,n.jsxs)("div",{className:"flex flex-col-reverse sm:flex-row sm:justify-center gap-3",children:[(0,n.jsx)("button",{type:"button",onClick:t,disabled:p,className:"w-full sm:w-auto px-6 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200",children:d}),(0,n.jsx)("button",{type:"button",onClick:o,disabled:p,className:`w-full sm:w-auto px-6 py-3 text-sm font-medium text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 ${m.confirmButton}`,children:p?(0,n.jsxs)("div",{className:"flex items-center justify-center",children:[(0,n.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Processing..."]}):c})]})]})]})})]}):null}},59168:(e,t,o)=>{"use strict";o.d(t,{A:()=>r});var n=o(43210);let r=n.forwardRef(function({title:e,titleId:t,...o},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},o),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))})},70440:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>r});var n=o(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71031:(e,t,o)=>{"use strict";o.d(t,{A:()=>r});var n=o(43210);let r=n.forwardRef(function({title:e,titleId:t,...o},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},o),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},71178:(e,t,o)=>{"use strict";o.d(t,{A:()=>r});var n=o(43210);let r=n.forwardRef(function({title:e,titleId:t,...o},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},o),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))})},92529:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>s});var n=o(37413),r=o(47417);function s(){return(0,n.jsx)(r.MyModelsSkeleton,{})}}};