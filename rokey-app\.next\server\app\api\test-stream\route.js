(()=>{var e={};e.id=2319,e.ids=[2319],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25047:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>l,routeModule:()=>p,serverHooks:()=>d,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>c});var s={};r.r(s),r.d(s,{GET:()=>i});var n=r(96559),a=r(48088),o=r(37719);async function i(e){let t=new TextEncoder;return new Response(new ReadableStream({start(e){let r=`data: ${JSON.stringify({message:"Test stream connected",timestamp:new Date().toISOString()})}

`;e.enqueue(t.encode(r));let s=0,n=setInterval(()=>{s++;let r=`data: ${JSON.stringify({message:`Test message ${s}`,timestamp:new Date().toISOString()})}

`;e.enqueue(t.encode(r)),s>=5&&(clearInterval(n),e.close())},1e3)},cancel(){}}),{headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","Access-Control-Allow-Origin":"*","Access-Control-Allow-Headers":"Cache-Control"}})}let p=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/test-stream/route",pathname:"/api/test-stream",filename:"route",bundlePath:"app/api/test-stream/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\test-stream\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:u,workUnitAsyncStorage:c,serverHooks:d}=p;function l(){return(0,o.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:c})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{},96559:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719],()=>r(25047));module.exports=s})();