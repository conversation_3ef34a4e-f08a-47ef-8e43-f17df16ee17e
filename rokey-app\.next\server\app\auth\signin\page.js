(()=>{var e={};e.id=4680,e.ids=[4680],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},43476:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var a=r(60687),s=r(43210),n=r(85814),o=r.n(n),i=r(30474),l=r(21121),d=r(94257),p=r(66524),c=r(56878),m=r(79481),x=r(16189);function f(){let[e,t]=(0,s.useState)(""),[r,n]=(0,s.useState)(""),[f,u]=(0,s.useState)(!1),[g,b]=(0,s.useState)(!1),[h,$]=(0,s.useState)(""),k=(0,x.useRouter)(),w=(0,x.useSearchParams)(),y=(0,m.u)(),v=async t=>{t.preventDefault(),b(!0),$("");try{let{data:t,error:a}=await y.auth.signInWithPassword({email:e,password:r});if(a)throw a;if(t.user){await new Promise(e=>setTimeout(e,500));try{let{data:e,error:r}=await y.from("user_profiles").select("subscription_status, subscription_tier").eq("id",t.user.id).single(),a=w.get("redirectTo"),s=w.get("plan"),n=w.get("email"),o=w.get("checkout_user_id");if(!e||"active"!==e.subscription_status){if(o&&s&&["starter","professional","enterprise"].includes(s)){let e=`/checkout?plan=${s}&user_id=${t.user.id}${n?`&email=${encodeURIComponent(n)}`:""}`;k.push(e)}else if(s&&["starter","professional","enterprise"].includes(s)){let e=`/checkout?plan=${s}&user_id=${t.user.id}${n?`&email=${encodeURIComponent(n)}`:""}`;k.push(e)}else k.push("/pricing?checkout=true&message=subscription_required");return}s&&["starter","professional","enterprise"].includes(s)?k.push(`/pricing?plan=${s}&checkout=true`):a?k.push(a):k.push("/dashboard")}catch(e){k.push("/dashboard")}}}catch(e){$(e.message||"Invalid email or password. Please try again.")}finally{b(!1)}};return(0,a.jsxs)("div",{className:"min-h-screen bg-white relative overflow-hidden flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:[(0,a.jsx)(c.A,{gridSize:50,opacity:.064,color:"#000000",variant:"subtle",animated:!0,className:"fixed inset-0"}),(0,a.jsx)("div",{className:"absolute inset-0",children:(0,a.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"radial-gradient(circle, rgba(255, 107, 53, 0.12) 1px, transparent 1px)",backgroundSize:"100px 100px",mask:"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)",WebkitMask:"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)"}})}),(0,a.jsx)("div",{className:"relative z-10 w-full max-w-6xl mx-auto",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,a.jsx)(l.P.div,{initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8},className:"hidden lg:block",children:(0,a.jsxs)("div",{className:"bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-3xl p-12 text-white relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 opacity-20",style:{backgroundImage:`
                    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)
                  `,backgroundSize:"30px 30px"}}),(0,a.jsxs)("div",{className:"relative z-10",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mb-6 backdrop-blur-sm",children:(0,a.jsx)(i.default,{src:"/roukey_logo.png",alt:"RouKey",width:48,height:48,className:"w-12 h-12 object-contain",priority:!0})}),(0,a.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"Welcome to RouKey"}),(0,a.jsxs)("p",{className:"text-xl text-white/90 mb-8",children:["Access to ",(0,a.jsx)("span",{className:"font-bold",children:"UNLIMITED"})," AI requests across 300+ models"]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"}),(0,a.jsx)("span",{className:"text-white/90",children:"Intelligent Role Routing"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"}),(0,a.jsx)("span",{className:"text-white/90",children:"Enterprise Security"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"}),(0,a.jsx)("span",{className:"text-white/90",children:"No Request Limits"})]})]})]})]})}),(0,a.jsxs)(l.P.div,{initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.8,delay:.2},className:"w-full max-w-md mx-auto lg:mx-0",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsxs)(o(),{href:"/",className:"inline-flex items-center space-x-3 mb-8 justify-center lg:justify-start",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-lg border border-gray-100 p-1",children:(0,a.jsx)(i.default,{src:"/roukey_logo.png",alt:"RouKey",width:40,height:40,className:"w-full h-full object-contain",priority:!0})}),(0,a.jsx)("span",{className:"text-3xl font-bold text-black",children:"RouKey"})]}),(0,a.jsx)("h2",{className:"text-4xl font-bold text-black mb-3",children:"Sign In"}),(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"Welcome back to your AI gateway"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl border border-gray-100 p-8 backdrop-blur-sm relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 opacity-5",style:{backgroundImage:`
                    linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)
                  `,backgroundSize:"20px 20px"}}),(0,a.jsxs)("div",{className:"relative z-10",children:["account_created"===w.get("message")&&(0,a.jsx)("div",{className:"mb-6 p-4 bg-green-50 border border-green-200 rounded-xl",children:(0,a.jsx)("p",{className:"text-green-600 text-sm font-medium",children:"✅ Account created successfully! Please sign in to complete your checkout."})}),h&&(0,a.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-xl",children:(0,a.jsx)("p",{className:"text-red-600 text-sm",children:h})}),(0,a.jsxs)("form",{onSubmit:v,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-semibold text-gray-800 mb-3",children:"\uD83D\uDCE7 Email Address"}),(0,a.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>t(e.target.value),className:"w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white",placeholder:"Enter your email"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-semibold text-gray-800 mb-3",children:"\uD83D\uDD12 Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{id:"password",name:"password",type:f?"text":"password",autoComplete:"current-password",required:!0,value:r,onChange:e=>n(e.target.value),className:"w-full px-5 py-4 pr-14 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white",placeholder:"Enter your password"}),(0,a.jsx)("button",{type:"button",onClick:()=>u(!f),className:"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-[#ff6b35] transition-colors",children:f?(0,a.jsx)(d.A,{className:"h-5 w-5"}):(0,a.jsx)(p.A,{className:"h-5 w-5"})})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-5 w-5 text-[#ff6b35] focus:ring-[#ff6b35] border-gray-300 rounded-lg"}),(0,a.jsx)("label",{htmlFor:"remember-me",className:"ml-3 block text-sm font-medium text-gray-700",children:"Remember me"})]}),(0,a.jsx)(o(),{href:"/auth/reset-password",className:"text-sm text-[#ff6b35] hover:text-[#e55a2b] font-semibold transition-colors",children:"Forgot password?"})]}),(0,a.jsx)("button",{type:"submit",disabled:g,className:"w-full bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white py-4 px-6 rounded-xl font-bold text-lg hover:from-[#e55a2b] hover:to-[#e8851a] focus:ring-4 focus:ring-[#ff6b35]/30 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02]",children:g?(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Signing in..."]}):"Sign In"})]})]})]}),(0,a.jsx)("div",{className:"text-center mt-8",children:(0,a.jsxs)("p",{className:"text-gray-600 text-lg",children:["Don't have an account?"," ",(0,a.jsx)(o(),{href:"/auth/signup",className:"text-[#ff6b35] hover:text-[#e55a2b] font-bold transition-colors",children:"Sign up for free"})]})})]})]})})]})}function u(){return(0,a.jsx)(s.Suspense,{fallback:(0,a.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}),children:(0,a.jsx)(f,{})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56878:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(60687),s=r(76180),n=r.n(s);function o({className:e="",gridSize:t=40,opacity:r=.1,color:s="#000000",animated:o=!1,glowEffect:i=!1,variant:l="subtle"}){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{style:{...(()=>{let e=(e,t)=>"#000000"===e?`rgba(0, 0, 0, ${t})`:"#ffffff"===e?`rgba(255, 255, 255, ${t})`:"#ff6b35"===e?`rgba(255, 107, 53, ${t})`:`${e}${Math.round(255*t).toString(16).padStart(2,"0")}`,a=3.2*r*.8;switch(l){case"tech":return{backgroundImage:`
            linear-gradient(${e(s,a)} 1px, transparent 1px),
            linear-gradient(90deg, ${e(s,a)} 1px, transparent 1px),
            radial-gradient(circle at 50% 50%, ${e(s,.5*a)} 2px, transparent 2px)
          `,backgroundSize:`${t}px ${t}px, ${t}px ${t}px, ${4*t}px ${4*t}px`,animation:o?"tech-grid-move 30s linear infinite":"none",mask:`
            radial-gradient(ellipse 90% 90% at center, black 10%, transparent 85%),
            linear-gradient(to right, transparent 0%, black 8%, black 92%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black 8%, black 92%, transparent 100%)
          `,maskComposite:"intersect",WebkitMask:`
            radial-gradient(ellipse 90% 90% at center, black 10%, transparent 85%),
            linear-gradient(to right, transparent 0%, black 8%, black 92%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black 8%, black 92%, transparent 100%)
          `,WebkitMaskComposite:"source-in"};case"premium":return{backgroundImage:`
            linear-gradient(${e(s,a)} 0.5px, transparent 0.5px),
            linear-gradient(90deg, ${e(s,a)} 0.5px, transparent 0.5px),
            linear-gradient(${e(s,.7*a)} 1px, transparent 1px),
            linear-gradient(90deg, ${e(s,.7*a)} 1px, transparent 1px)
          `,backgroundSize:`${t}px ${t}px, ${t}px ${t}px, ${5*t}px ${5*t}px, ${5*t}px ${5*t}px`,animation:o?"premium-grid-float 40s ease-in-out infinite":"none",mask:`
            radial-gradient(ellipse 85% 85% at center, black 15%, transparent 80%),
            linear-gradient(to right, transparent 2%, black 12%, black 88%, transparent 98%),
            linear-gradient(to bottom, transparent 2%, black 12%, black 88%, transparent 98%)
          `,maskComposite:"intersect",WebkitMask:`
            radial-gradient(ellipse 85% 85% at center, black 15%, transparent 80%),
            linear-gradient(to right, transparent 2%, black 12%, black 88%, transparent 98%),
            linear-gradient(to bottom, transparent 2%, black 12%, black 88%, transparent 98%)
          `,WebkitMaskComposite:"source-in"};default:return{backgroundImage:`
            linear-gradient(${e(s,a)} 1px, transparent 1px),
            linear-gradient(90deg, ${e(s,a)} 1px, transparent 1px)
          `,backgroundSize:`${t}px ${t}px`,animation:o?"subtle-grid-drift 25s linear infinite":"none",mask:`
            radial-gradient(ellipse 95% 95% at center, black 5%, transparent 90%),
            linear-gradient(to right, transparent 0%, black 5%, black 95%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black 5%, black 95%, transparent 100%)
          `,maskComposite:"intersect",WebkitMask:`
            radial-gradient(ellipse 95% 95% at center, black 5%, transparent 90%),
            linear-gradient(to right, transparent 0%, black 5%, black 95%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black 5%, black 95%, transparent 100%)
          `,WebkitMaskComposite:"source-in"}}})(),zIndex:1,filter:i?"drop-shadow(0 0 10px rgba(255, 107, 53, 0.1))":"none"},className:n().dynamic([["cdf0235daf430a20",[t,t,.5*t,.3*t,t,.7*t,.3*t,t,.2*t,-.1*t,.1*t,.2*t,-.1*t,.1*t]]])+" "+`absolute inset-0 pointer-events-none ${e}`}),(0,a.jsx)(n(),{id:"cdf0235daf430a20",dynamic:[t,t,.5*t,.3*t,t,.7*t,.3*t,t,.2*t,-.1*t,.1*t,.2*t,-.1*t,.1*t],children:`@-webkit-keyframes subtle-grid-drift{0%{-webkit-transform:translate(0,0);transform:translate(0,0)}100%{-webkit-transform:translate(${t}px,${t}px);transform:translate(${t}px,${t}px)}}@-moz-keyframes subtle-grid-drift{0%{-moz-transform:translate(0,0);transform:translate(0,0)}100%{-moz-transform:translate(${t}px,${t}px);transform:translate(${t}px,${t}px)}}@-o-keyframes subtle-grid-drift{0%{-o-transform:translate(0,0);transform:translate(0,0)}100%{-o-transform:translate(${t}px,${t}px);transform:translate(${t}px,${t}px)}}@keyframes subtle-grid-drift{0%{-webkit-transform:translate(0,0);-moz-transform:translate(0,0);-o-transform:translate(0,0);transform:translate(0,0)}100%{-webkit-transform:translate(${t}px,${t}px);-moz-transform:translate(${t}px,${t}px);-o-transform:translate(${t}px,${t}px);transform:translate(${t}px,${t}px)}}@-webkit-keyframes tech-grid-move{0%{-webkit-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-webkit-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg)}50%{-webkit-transform:translate(${t}px,${.7*t}px)rotate(0deg);transform:translate(${t}px,${.7*t}px)rotate(0deg)}75%{-webkit-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);transform:translate(${.3*t}px,${t}px)rotate(-.5deg)}100%{-webkit-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-moz-keyframes tech-grid-move{0%{-moz-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-moz-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg)}50%{-moz-transform:translate(${t}px,${.7*t}px)rotate(0deg);transform:translate(${t}px,${.7*t}px)rotate(0deg)}75%{-moz-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);transform:translate(${.3*t}px,${t}px)rotate(-.5deg)}100%{-moz-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-o-keyframes tech-grid-move{0%{-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-o-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg)}50%{-o-transform:translate(${t}px,${.7*t}px)rotate(0deg);transform:translate(${t}px,${.7*t}px)rotate(0deg)}75%{-o-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);transform:translate(${.3*t}px,${t}px)rotate(-.5deg)}100%{-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@keyframes tech-grid-move{0%{-webkit-transform:translate(0,0)rotate(0deg);-moz-transform:translate(0,0)rotate(0deg);-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-webkit-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);-moz-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);-o-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg)}50%{-webkit-transform:translate(${t}px,${.7*t}px)rotate(0deg);-moz-transform:translate(${t}px,${.7*t}px)rotate(0deg);-o-transform:translate(${t}px,${.7*t}px)rotate(0deg);transform:translate(${t}px,${.7*t}px)rotate(0deg)}75%{-webkit-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);-moz-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);-o-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);transform:translate(${.3*t}px,${t}px)rotate(-.5deg)}100%{-webkit-transform:translate(0,0)rotate(0deg);-moz-transform:translate(0,0)rotate(0deg);-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-webkit-keyframes premium-grid-float{0%,100%{-webkit-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-webkit-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);transform:translate(${.2*t}px,${-.1*t}px)scale(1.01)}50%{-webkit-transform:translate(${.1*t}px,${.2*t}px)scale(.99);transform:translate(${.1*t}px,${.2*t}px)scale(.99)}75%{-webkit-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);transform:translate(${-.1*t}px,${.1*t}px)scale(1.005)}}@-moz-keyframes premium-grid-float{0%,100%{-moz-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-moz-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);transform:translate(${.2*t}px,${-.1*t}px)scale(1.01)}50%{-moz-transform:translate(${.1*t}px,${.2*t}px)scale(.99);transform:translate(${.1*t}px,${.2*t}px)scale(.99)}75%{-moz-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);transform:translate(${-.1*t}px,${.1*t}px)scale(1.005)}}@-o-keyframes premium-grid-float{0%,100%{-o-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-o-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);transform:translate(${.2*t}px,${-.1*t}px)scale(1.01)}50%{-o-transform:translate(${.1*t}px,${.2*t}px)scale(.99);transform:translate(${.1*t}px,${.2*t}px)scale(.99)}75%{-o-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);transform:translate(${-.1*t}px,${.1*t}px)scale(1.005)}}@keyframes premium-grid-float{0%,100%{-webkit-transform:translate(0,0)scale(1);-moz-transform:translate(0,0)scale(1);-o-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-webkit-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);-moz-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);-o-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);transform:translate(${.2*t}px,${-.1*t}px)scale(1.01)}50%{-webkit-transform:translate(${.1*t}px,${.2*t}px)scale(.99);-moz-transform:translate(${.1*t}px,${.2*t}px)scale(.99);-o-transform:translate(${.1*t}px,${.2*t}px)scale(.99);transform:translate(${.1*t}px,${.2*t}px)scale(.99)}75%{-webkit-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);-moz-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);-o-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);transform:translate(${-.1*t}px,${.1*t}px)scale(1.005)}}`})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66524:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71963:(e,t,r)=>{Promise.resolve().then(r.bind(r,87578))},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82579:(e,t,r)=>{Promise.resolve().then(r.bind(r,43476))},85443:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>m,tree:()=>d});var a=r(65239),s=r(48088),n=r(88170),o=r.n(n),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["auth",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,87578)),"C:\\RoKey App\\rokey-app\\src\\app\\auth\\signin\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["C:\\RoKey App\\rokey-app\\src\\app\\auth\\signin\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/auth/signin/page",pathname:"/auth/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},87578:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\auth\\signin\\page.tsx","default")},91645:e=>{"use strict";e.exports=require("net")},94257:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))})},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[7719,221,1658,1121,7437],()=>r(85443));module.exports=a})();