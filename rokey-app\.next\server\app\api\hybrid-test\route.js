"use strict";(()=>{var e={};e.id=7765,e.ids=[7765],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},36208:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>c,serverHooks:()=>y,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{GET:()=>d,POST:()=>u});var o=t(96559),i=t(48088),n=t(37719),a=t(32190),p=t(23097);async function u(e){try{let{prompt:r,configId:t,userId:s}=await e.json();if(!r||!t||!s)return a.NextResponse.json({error:"Missing required fields: prompt, configId, userId"},{status:400});let o=new p.z,i=await o.analyzeForHybridOrchestration(r,[],t);if(!i.shouldUseHybrid)return a.NextResponse.json({message:"Hybrid orchestration not recommended for this prompt",analysis:i});{let e=await o.initiateHybridOrchestration(s,t,r,[],{test:!0});if(e.isHybridOrchestration&&e.streamingResponse)return e.streamingResponse;return a.NextResponse.json({error:"Hybrid orchestration failed",details:e.error},{status:500})}}catch(e){return a.NextResponse.json({error:"Internal server error",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function d(){return a.NextResponse.json({message:"Revolutionary Hybrid CrewAI + AutoGen Orchestration System Test Endpoint",description:"POST a request with { prompt, configId, userId } to test hybrid orchestration",features:["Dynamic expert consultation","Smart role matching from existing API keys","CrewAI + AutoGen hybrid approach","Superior performance vs individual frameworks"]})}let c=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/hybrid-test/route",pathname:"/api/hybrid-test",filename:"route",bundlePath:"app/api/hybrid-test/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\hybrid-test\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:l,serverHooks:y}=c;function h(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:l})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,580,9398,3410,563],()=>t(36208));module.exports=s})();